# Swagger Documentation for Atlas Services

This document explains how to access and use the Swagger UI for the Atlas Services API.

## Overview

Swagger UI provides a user-friendly interface to explore and test the REST APIs exposed by the Atlas Services application. It allows you to:

- View all available API endpoints
- Understand the request parameters and response formats
- Execute API calls directly from the browser
- Authenticate using OAuth2 with Key<PERSON>loak

## Accessing Swagger UI

The Swagger UI is available at the following URL when the application is running:

```
http://localhost:8050/swagger-ui.html
```

The OpenAPI specification JSON is available at:

```
http://localhost:8050/api-docs
```

## Authentication

The API is secured using OAuth2 with Keycloak. To authenticate:

1. Click on the "Authorize" button in the Swagger UI
2. Use your Keycloak credentials to log in
3. After successful authentication, you'll be able to execute secured API calls

## Configuration Details

The Swagger integration has been configured with the following components:

### 1. Dependencies

The following dependency has been added to the `pom.xml`:

```xml
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    <version>2.3.0</version>
</dependency>
```

### 2. Application Properties

The following properties have been added to `application.properties`:

```properties
# Swagger/OpenAPI Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.swagger-ui.filter=true
springdoc.swagger-ui.disable-swagger-default-url=true
springdoc.packages-to-scan=it.kimia.atlas.atlasservices
```

### 3. OpenAPI Configuration

A configuration class `OpenApiConfig` has been created to customize the Swagger documentation:

```java
@Configuration
public class OpenApiConfig {
    // Configuration for API information and OAuth2 security
}
```

## Troubleshooting

If you encounter any issues with the Swagger UI:

1. Ensure the application is running correctly
2. Check that the Keycloak server is accessible
3. Verify that your user has the appropriate roles/permissions
4. Check the browser console for any JavaScript errors

## Additional Resources

- [SpringDoc OpenAPI Documentation](https://springdoc.org/)
- [Swagger UI Documentation](https://swagger.io/tools/swagger-ui/)
- [OpenAPI Specification](https://spec.openapis.org/oas/latest.html)