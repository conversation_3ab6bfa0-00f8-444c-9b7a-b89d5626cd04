# Wood Module API Testing Guide

This guide provides comprehensive testing instructions and payloads for the Wood Module API with the new `WoodPostIntervationCheck` functionality.

## API Endpoint

```http
PUT /api/v2/projects/{projectId}/modules/{moduleId}/params
```

## Prerequisites

1. **Create a Project**: You need an existing project ID
2. **Create a Wood Module**: You need an existing Wood module ID within the project
3. **Create a Product**: You need an existing product ID for composite geometry (if using post-intervention check)
4. **Authentication**: The API is currently whitelisted (no authentication required)

## Important Notes

### Product ID vs Product Object
The `compositeGeometry` field uses `productId` (string) instead of a full `Product` object. This approach:
- **Reduces payload size** - Frontend only sends the product ID
- **Ensures data consistency** - Always fetches the latest product data from database
- **Simplifies frontend logic** - No need to manage full product objects
- **Follows REST best practices** - References by ID rather than embedding full objects

Example:
```json
"compositeGeometry": {
  "productId": "product-123",  // ✅ Use this
  "stripWidth": 50.0,
  // ... other fields
}
```

Instead of:
```json
"compositeGeometry": {
  "product": { "id": "...", "name": "...", ... },  // ❌ Don't use this
  "stripWidth": 50.0,
  // ... other fields
}
```

## Complete Wood Module Payload Structure

### 1. Basic Wood Module Payload (Minimal)

```json
{
  "moduleType": "WOOD",
  "initialDeformation": 5.5,
  "materialProperties": {
    "category": "CONIFER_AND_POPLAR_WOOD",
    "woodName": "C14",
    "characteristicBendingStrength": 14.0,
    "characteristicShearStrength": 1.7,
    "characteristicTensileStrength": 8.0,
    "characteristicCompressiveStrength": 16.0,
    "meanElasticityModulus": 7000.0,
    "characteristicElasticityModulus": 4700.0,
    "meanDensity": 350.0,
    "characteristicDensity": 290.0
  },
  "geometry": {
    "width": 100.0,
    "height": 200.0,
    "length": 3000.0,
    "span": 2800.0,
    "serviceClass": "SERVICE_CLASS_1",
    "loadDuration": "PERMANENT",
    "correctionFactor": 1.0,
    "deformabilityFactor": 2.0,
    "designBendingStrength": 14.0,
    "designShearStrength": 1.7,
    "elasticityInstantaneousModulus": 7000.0,
    "longTermElasticityModulus": 2333.0,
    "airRelativeHumidity": 65.0
  }
}
```

### 2. Wood Module with Pre-Intervention Check

```json
{
  "moduleType": "WOOD",
  "initialDeformation": 5.5,
  "materialProperties": {
    "category": "CONIFER_AND_POPLAR_WOOD",
    "woodName": "C14",
    "characteristicBendingStrength": 14.0,
    "characteristicShearStrength": 1.7,
    "characteristicTensileStrength": 8.0,
    "characteristicCompressiveStrength": 16.0,
    "meanElasticityModulus": 7000.0,
    "characteristicElasticityModulus": 4700.0,
    "meanDensity": 350.0,
    "characteristicDensity": 290.0
  },
  "geometry": {
    "width": 100.0,
    "height": 200.0,
    "length": 3000.0,
    "span": 2800.0,
    "serviceClass": "SERVICE_CLASS_1",
    "loadDuration": "PERMANENT",
    "correctionFactor": 1.0,
    "deformabilityFactor": 2.0,
    "designBendingStrength": 14.0,
    "designShearStrength": 1.7,
    "elasticityInstantaneousModulus": 7000.0,
    "longTermElasticityModulus": 2333.0,
    "airRelativeHumidity": 65.0
  },
  "preIntervationCheck": {
    "maximumBendingMoment": 15.5,
    "maximumShearForce": 8.2,
    "designBendingStress": 12.8,
    "designBendingStrength": 14.0,
    "bendingCheck": 0.91,
    "designShearStress": 1.2,
    "designShearStrength": 1.7,
    "shearCheck": 0.71,
    "permanentLoadPerLinearMeter": 2.5,
    "imposedLoadPerLinearMeter": 4.0,
    "instantaneousDeflectionPermanentLoad": 3.2,
    "instantaneousDeflectionImposedLoad": 5.1,
    "instantaneousDeflectionTotalLoads": 8.3,
    "deformabilityCheck": 0.89,
    "combinationFactor": 0.3,
    "finalDeflectionTotalLoads": 11.8,
    "finalCheckResult": 0.95
  }
}
```

### 3. Complete Wood Module with Post-Intervention Check (NEW)

```json
{
  "moduleType": "WOOD",
  "initialDeformation": 5.5,
  "materialProperties": {
    "category": "CONIFER_AND_POPLAR_WOOD",
    "woodName": "C14",
    "characteristicBendingStrength": 14.0,
    "characteristicShearStrength": 1.7,
    "characteristicTensileStrength": 8.0,
    "characteristicCompressiveStrength": 16.0,
    "meanElasticityModulus": 7000.0,
    "characteristicElasticityModulus": 4700.0,
    "meanDensity": 350.0,
    "characteristicDensity": 290.0
  },
  "geometry": {
    "width": 100.0,
    "height": 200.0,
    "length": 3000.0,
    "span": 2800.0,
    "serviceClass": "SERVICE_CLASS_1",
    "loadDuration": "PERMANENT",
    "correctionFactor": 1.0,
    "deformabilityFactor": 2.0,
    "designBendingStrength": 14.0,
    "designShearStrength": 1.7,
    "elasticityInstantaneousModulus": 7000.0,
    "longTermElasticityModulus": 2333.0,
    "airRelativeHumidity": 65.0
  },
  "preIntervationCheck": {
    "maximumBendingMoment": 15.5,
    "maximumShearForce": 8.2,
    "designBendingStress": 12.8,
    "designBendingStrength": 14.0,
    "bendingCheck": 0.91,
    "designShearStress": 1.2,
    "designShearStrength": 1.7,
    "shearCheck": 0.71,
    "permanentLoadPerLinearMeter": 2.5,
    "imposedLoadPerLinearMeter": 4.0,
    "instantaneousDeflectionPermanentLoad": 3.2,
    "instantaneousDeflectionImposedLoad": 5.1,
    "instantaneousDeflectionTotalLoads": 8.3,
    "deformabilityCheck": 0.89,
    "combinationFactor": 0.3,
    "finalDeflectionTotalLoads": 11.8,
    "finalCheckResult": 0.95
  },
  "postIntervationCheck": {
    "initialDeformation": 3.2,
    "materialProperties": {
      "category": "CONIFER_AND_POPLAR_WOOD",
      "woodName": "C14",
      "characteristicBendingStrength": 14.0,
      "characteristicShearStrength": 1.7,
      "characteristicTensileStrength": 8.0,
      "characteristicCompressiveStrength": 16.0,
      "meanElasticityModulus": 7000.0,
      "characteristicElasticityModulus": 4700.0,
      "meanDensity": 350.0,
      "characteristicDensity": 290.0
    },
    "geometry": {
      "width": 100.0,
      "height": 200.0,
      "length": 3000.0,
      "span": 2800.0,
      "serviceClass": "SERVICE_CLASS_1",
      "loadDuration": "PERMANENT",
      "correctionFactor": 1.0,
      "deformabilityFactor": 2.0,
      "designBendingStrength": 14.0,
      "designShearStrength": 1.7,
      "elasticityInstantaneousModulus": 7000.0,
      "longTermElasticityModulus": 2333.0,
      "airRelativeHumidity": 65.0
    },
    "compositeGeometry": {
      "productId": "product-123",
      "stripWidth": 50.0,
      "equivalentThickness": 0.5,
      "layersNumber": 2.0,
      "expositionType": "INTERNAL",
      "environmentalConversionFactor": 0.85
    },
    "compositeProperties": {
      "frpElasticityModulus": 230000.0,
      "frpDesignMaximumStrain": 0.012,
      "frpCharacteristicStrain": 0.015,
      "frpPartialFactorInUls": 1.25,
      "frpMaximumStrainForDebonding": 0.008,
      "loadConditionFactor": 0.9,
      "frpPartialFactorInUlsForDebonding": 1.4,
      "reinforcementToSectionWidthRatio": 0.5,
      "reinforcementToSectionWidthRatioUsefull": 0.45,
      "geometricCorrectionFactor": 0.95,
      "geometricCorrectionFactorUsefull": 0.9,
      "experimentalCorrectionFactor": 0.85,
      "confidenceFactor": 0.8,
      "sectionModulus": 666666.67,
      "momentOfInertiaAboutY": 66666666.67
    },
    "resultOfPostIntervationCheck": {
      "maximumBendingMoment": 22.5,
      "maximumShearForce": 10.2,
      "designBendingStress": 18.8,
      "designBendingStrength": 25.0,
      "designBendingCheck": 0.75,
      "designShearStress": 1.5,
      "designShearStrength": 1.7,
      "designShearCheck": 0.88,
      "permanentLoadPerLinearMeter": 2.5,
      "imposedLoadPerLinearMeter": 4.0,
      "instantaneousDeflectionPermanentLoad": 2.1,
      "instantaneousDeflectionImposedLoad": 3.4,
      "instantaneousDeflectionTotalLoads": 5.5,
      "instantaneousDeflectionCheck": 0.55,
      "combinationFactor": 0.3,
      "finalDeflectionTotalLoads": 7.8,
      "finalCheckResult": 0.65
    }
  }
}
```

## Testing Scenarios

### Scenario 1: Basic Wood Module Creation
**Purpose**: Test basic wood module parameter setting
**Payload**: Use "Basic Wood Module Payload (Minimal)" above
**Expected Result**: 200 OK with updated module

### Scenario 2: Pre-Intervention Check Only
**Purpose**: Test wood module with pre-intervention calculations
**Payload**: Use "Wood Module with Pre-Intervention Check" above
**Expected Result**: 200 OK with pre-intervention data saved

### Scenario 3: Complete Post-Intervention Check
**Purpose**: Test the new post-intervention functionality with composite materials
**Payload**: Use "Complete Wood Module with Post-Intervention Check" above
**Expected Result**: 200 OK with all post-intervention data saved

### Scenario 4: Partial Update
**Purpose**: Test updating only specific fields
**Payload**: 
```json
{
  "moduleType": "WOOD",
  "postIntervationCheck": {
    "compositeProperties": {
      "frpElasticityModulus": 250000.0,
      "frpDesignMaximumStrain": 0.014
    }
  }
}
```
**Expected Result**: 200 OK with only specified fields updated

## Validation Testing

### Test Invalid Values
```json
{
  "moduleType": "WOOD",
  "initialDeformation": -5.0,
  "postIntervationCheck": {
    "compositeGeometry": {
      "stripWidth": -10.0,
      "layersNumber": 0.0
    }
  }
}
```
**Expected Result**: 400 Bad Request with validation errors

## cURL Examples

### Basic Test
```bash
curl -X PUT "http://localhost:8080/api/v2/projects/{projectId}/modules/{moduleId}/params" \
  -H "Content-Type: application/json" \
  -d '{
    "moduleType": "WOOD",
    "initialDeformation": 5.5,
    "materialProperties": {
      "category": "CONIFER_AND_POPLAR_WOOD",
      "woodName": "C14",
      "characteristicBendingStrength": 14.0
    }
  }'
```

### Complete Post-Intervention Test
```bash
curl -X PUT "http://localhost:8080/api/v2/projects/{projectId}/modules/{moduleId}/params" \
  -H "Content-Type: application/json" \
  -d @complete-wood-payload.json
```

## Expected Response Format

```json
{
  "id": "module-id-123",
  "moduleType": "WOOD",
  "params": {
    "initialDeformation": 5.5,
    "materialProperties": { ... },
    "geometry": { ... },
    "preIntervationCheck": { ... },
    "postIntervationCheck": { ... }
  },
  "createdAt": "2024-01-15T10:30:00Z",
  "lastModified": "2024-01-15T10:30:00Z"
}
```

## Enum Values Reference

### WoodCategory
- `CONIFER_AND_POPLAR_WOOD`
- `HARDWOOD` 
- `GLUED_LAMINATED_TIMBER`

### WoodGeometryServiceClass
- `SERVICE_CLASS_1`
- `SERVICE_CLASS_2`
- `SERVICE_CLASS_3`

### WoodGeometryLoadDuration
- `PERMANENT`
- `LONG_TERM`
- `MEDIUM_TERM`
- `SHORT_TERM`
- `INSTANTANEOUS`

### ExpositionType
- `INTERNAL`
- `EXTERNAL`
- `AGGRESSIVE`

### FiberType
- `basalt`
- `carbon`
- `galvanized_steel`
- `steel`
- `glass`
- `preformed_carbon`
- `unknown`

## Common Issues and Solutions

1. **Module Not Found (404)**: Ensure the moduleId exists and belongs to the specified project
2. **Validation Errors (400)**: Check that all required fields are provided and values are positive where required
3. **Product Not Found**: Ensure the product ID in compositeGeometry exists in the products collection
4. **Type Mismatch**: Ensure enum values match exactly (case-sensitive)

## Next Steps

After successful testing:
1. Verify data persistence by retrieving the module
2. Test calculation workflows that use the post-intervention data
3. Test integration with frontend components
4. Validate business logic calculations
