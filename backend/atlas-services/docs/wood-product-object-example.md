# Wood Product Object Population Example

This document shows how the `WoodCompositeGeometry` automatically gets the full `Product` object populated from just the `productId`.

## How It Works

### 1. Frontend Sends Clean Payload
```json
{
  "moduleType": "WOOD",
  "postIntervationCheck": {
    "compositeGeometry": {
      "productId": "product-123",
      "stripWidth": 50.0,
      "equivalentThickness": 0.5,
      "layersNumber": 2.0,
      "expositionType": "INTERNAL",
      "environmentalConversionFactor": 0.85
    }
  }
}
```

### 2. Backend Automatically Populates Product Object

When `WoodParamApplier.apply()` is called:

```java
// 1. Parse parameters from JSON
WoodParams parsedParams = objectMapper.convertValue(params, WoodParams.class);
// At this point: compositeGeometry.productId = "product-123", product = null

// 2. Validate and populate Product objects
populateProductObjects(parsedParams);
// At this point: compositeGeometry.product = full Product object!

// 3. Merge and save
WoodParams mergedParams = mapper.merge(module.getParams(), parsedParams);
module.setParams(mergedParams);
```

### 3. Product Object Available for Calculations

```java
// In your calculation service
public void calculateCompositeStrength(WoodParams woodParams) {
    WoodCompositeGeometry compositeGeometry = woodParams.getPostIntervationCheck()
        .getCompositeGeometry();
    
    // Product object is already populated!
    Product product = compositeGeometry.getProduct();
    
    if (product != null) {
        // All product properties available
        BigDecimal elasticModulus = product.getElasticModulus();
        BigDecimal tensileStrength = product.getTensileStrength();
        FiberType fiberType = product.getFiberType();
        String productName = product.getName();
        
        // Use in calculations
        BigDecimal compositeMoment = calculateCompositeMoment(
            elasticModulus, 
            tensileStrength, 
            compositeGeometry.getStripWidth(),
            compositeGeometry.getLayersNumber()
        );
        
        log.info("Calculated composite moment for product {}: {}", 
            productName, compositeMoment);
    }
}
```

## Data Flow Diagram

```
Frontend                Backend                     Database
--------                -------                     --------

1. Send JSON with       2. Parse JSON              
   productId: "123" ──────► productId = "123"
                           product = null

                       3. Validate & Populate      4. Query Product
                          populateProductObjects() ──► SELECT * FROM products 
                                                      WHERE id = "123"
                                                   
                       5. Set Product Object       6. Return Product
                          product = fullObject ◄────── { id: "123", 
                                                        name: "Carbon Fiber",
                                                        elasticModulus: 230000,
                                                        ... }

                       7. Save to Module
                          module.setParams(params)

8. Use in Calculations
   product.getElasticModulus()
```

## Benefits

### ✅ **Clean API**
- Frontend only sends `productId`
- No need to manage full product objects
- Smaller payloads

### ✅ **Rich Data Model**
- Backend has full `Product` object
- All product properties available
- No additional database queries needed

### ✅ **Data Consistency**
- Always fetches latest product data
- Single source of truth
- Automatic validation

### ✅ **Developer Experience**
```java
// Simple and clean usage
Product product = compositeGeometry.getProduct();
BigDecimal modulus = product.getElasticModulus();
```

## JSON Serialization

### Input (from Frontend)
```json
{
  "compositeGeometry": {
    "productId": "product-123",
    "stripWidth": 50.0
  }
}
```

### Storage (in Database)
```json
{
  "compositeGeometry": {
    "productId": "product-123",
    "stripWidth": 50.0
    // Note: product object is @JsonIgnore, not serialized
  }
}
```

### Runtime (in Memory)
```java
WoodCompositeGeometry geometry = new WoodCompositeGeometry();
geometry.setProductId("product-123");
geometry.setProduct(fullProductObject);  // Populated automatically
geometry.setStripWidth(50.0);
```

## Error Handling

### Product Not Found
```java
// If productId doesn't exist
throw new ValidationException("Product not found with ID: product-123", null);
```

### Null Product ID
```java
// If productId is null or empty, product remains null
// Calculations should check for null:
Product product = compositeGeometry.getProduct();
if (product != null) {
    // Safe to use product
}
```

## Testing

### Unit Test Example
```java
@Test
void shouldPopulateProductObject() {
    // Given
    WoodParams params = new WoodParams();
    WoodPostIntervationCheck postCheck = new WoodPostIntervationCheck();
    WoodCompositeGeometry geometry = new WoodCompositeGeometry();
    geometry.setProductId("product-123");
    postCheck.setCompositeGeometry(geometry);
    params.setPostIntervationCheck(postCheck);
    
    Product mockProduct = Product.builder()
        .id("product-123")
        .name("Carbon Fiber")
        .elasticModulus(BigDecimal.valueOf(230000))
        .build();
    
    when(productService.getProductById("product-123"))
        .thenReturn(Optional.of(mockProduct));
    
    // When
    woodParamApplier.apply(woodModule, params);
    
    // Then
    Product populatedProduct = woodModule.getParams()
        .getPostIntervationCheck()
        .getCompositeGeometry()
        .getProduct();
    
    assertThat(populatedProduct).isNotNull();
    assertThat(populatedProduct.getName()).isEqualTo("Carbon Fiber");
    assertThat(populatedProduct.getElasticModulus()).isEqualTo(BigDecimal.valueOf(230000));
}
```

This approach gives you the perfect balance: clean API with rich data model!
