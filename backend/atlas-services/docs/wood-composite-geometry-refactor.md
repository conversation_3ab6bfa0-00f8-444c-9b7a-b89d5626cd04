# Wood Composite Geometry Refactor: Product ID vs Product Object

## Overview

This document explains the refactoring of `WoodCompositeGeometry` to use `productId` (string) instead of a full `Product` object, addressing the concern about payload size and data consistency.

## Problem Statement

The original `WoodCompositeGeometry` class contained a full `Product` object:

```java
public class WoodCompositeGeometry {
    private Product product;  // ❌ Full object in payload
    // ... other fields
}
```

This approach had several issues:
1. **Large payloads** - Frontend must send complete product data
2. **Data inconsistency** - Product data might be outdated
3. **Coupling** - Frontend needs to manage product object structure
4. **Network overhead** - Unnecessary data transfer

## Solution

Refactored to use `productId` reference:

```java
public class WoodCompositeGeometry {
    private String productId;  // ✅ Just the ID reference
    // ... other fields
}
```

## Changes Made

### 1. Updated `WoodCompositeGeometry.java`
```java
// Before
private Product product;

// After
private String productId;        // ✅ For API input
@JsonIgnore
private Product product;         // ✅ Populated automatically by backend
```

### 2. Updated `WoodCompositeGeometryDto.java`
Added missing fields to match the main class:
```java
public class WoodCompositeGeometryDto {
    private String productId;
    private BigDecimal stripWidth;
    private BigDecimal equivalentThickness;  // ✅ Added
    private BigDecimal layersNumber;
    private ExpositionType expositionType;
    private BigDecimal environmentalConversionFactor;  // ✅ Added
}
```

### 3. Enhanced `WoodParamApplier.java`
Added product validation and helper methods:

```java
@Component
public class WoodParamApplier implements ParamApplier {
    private final ProductService productService;  // ✅ Added dependency

    // ✅ Validates productId and populates Product object
    private void populateProductObjects(WoodParams params) {
        // 1. Validates that productId exists in database
        // 2. Fetches full Product object
        // 3. Sets Product object in WoodCompositeGeometry
        compositeGeometry.setProduct(product);
    }
}
```

### 4. Updated Test Payloads
```json
// Before
"compositeGeometry": {
  "product": {
    "id": "product-123",
    "name": "Carbon Fiber Mesh",
    "fiberType": "carbon",
    // ... many more fields
  },
  "stripWidth": 50.0
}

// After
"compositeGeometry": {
  "productId": "product-123",  // ✅ Just the ID
  "stripWidth": 50.0
}
```

## Benefits

### 1. **Cleaner API Design**
- Frontend only needs to send product ID
- Follows REST best practices (reference by ID)
- Smaller, more focused payloads

### 2. **Data Consistency**
- Always fetches latest product data from database
- No risk of stale product information
- Single source of truth for product data

### 3. **Better Performance**
- Reduced network traffic
- Faster JSON parsing
- Less memory usage

### 4. **Improved Maintainability**
- Cleaner separation of concerns
- Product management stays in ProductService
- Easier to test and mock

## Usage Examples

### Frontend Payload (New)
```json
{
  "moduleType": "WOOD",
  "postIntervationCheck": {
    "compositeGeometry": {
      "productId": "product-123",
      "stripWidth": 50.0,
      "equivalentThickness": 0.5,
      "layersNumber": 2.0,
      "expositionType": "INTERNAL",
      "environmentalConversionFactor": 0.85
    }
  }
}
```

### Backend Usage (Product Object Available)
```java
// In calculation services - Product is automatically populated!
WoodCompositeGeometry compositeGeometry = woodParams.getPostIntervationCheck()
    .getCompositeGeometry();

// Product object is already available
Product product = compositeGeometry.getProduct();
if (product != null) {
    // Use product for calculations
    BigDecimal elasticModulus = product.getElasticModulus();
    String productName = product.getName();
    FiberType fiberType = product.getFiberType();
    // ... all product properties available
}
```

## Validation

The system now validates that:
1. **Product exists** - `productId` references a valid product in the database
2. **Product is accessible** - ProductService can retrieve the product
3. **Early failure** - Validation happens during parameter application, not calculation

## Migration Notes

### For Frontend Developers
- Change from sending full `product` object to just `productId` string
- Use product selection APIs to get available products
- Send only the selected product's ID in the payload

### For Backend Developers
- Use `WoodParamApplier.getProductById()` when Product object is needed
- Product validation is automatic during parameter application
- ProductService dependency is available in WoodParamApplier

## Testing

Updated test files:
- `docs/test-payloads/wood-with-post-intervention.json`
- `docs/wood-module-testing-guide.md`

All payloads now use `productId` instead of full product objects.

## Backward Compatibility

This is a **breaking change** for the API. Frontend applications must be updated to:
1. Send `productId` instead of `product` object
2. Use product APIs to get available products for selection
3. Update any existing stored payloads

## Next Steps

1. **Update Frontend** - Modify frontend to use new payload structure
2. **Test Integration** - Verify product validation works correctly
3. **Update Documentation** - Ensure all examples use new format
4. **Performance Testing** - Measure improvement in payload size and processing time
