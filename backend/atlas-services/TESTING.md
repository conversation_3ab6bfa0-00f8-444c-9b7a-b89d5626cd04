# Testing the Spring Profiles

This document outlines the steps to test the different Spring profiles to ensure they work as expected.

## Testing the Base/Local Profile

1. Start the local services (MongoDB, Keycloak) using the local Docker Compose file:
   ```bash
   cd infrastructure
   docker-compose -f docker-compose.local.yaml up -d
   ```

2. Run the application without specifying a profile:
   ```bash
   cd backend/atlas-services
   ./mvnw spring-boot:run
   ```

3. Verify that the application starts successfully and connects to the local services.
4. Access the Swagger UI at http://localhost:8050/swagger-ui.html to verify the API is working.
5. Test some API endpoints to ensure they can communicate with MongoDB and Keycloak.

## Testing the Dev Profile

1. Start the staging services using the test Docker Compose file:
   ```bash
   cd infrastructure
   docker-compose -f docker-compose.test.yaml up -d
   ```

2. Run the application with the dev profile:
   ```bash
   cd backend/atlas-services
   ./mvnw spring-boot:run -Dspring.profiles.active=dev
   ```

3. Verify that the application starts successfully and connects to the staging services from outside Docker.
4. Access the Swagger UI at http://localhost:8050/swagger-ui.html to verify the API is working.
5. Test some API endpoints to ensure they can communicate with MongoDB and Keycloak running in the test containers.

## Testing the Staging Profile

1. Start the staging services using the test Docker Compose file:
   ```bash
   cd infrastructure
   docker-compose -f docker-compose.test.yaml up -d
   ```

2. Build the Docker image:
   ```bash
   cd backend
   docker build -t kimia/atlas-services:latest .
   ```

3. Run the Docker container with the staging profile:
   ```bash
   docker run -d --name atlas_services_test --network app_network -p 8000:8000 \
       -e SPRING_PROFILES_ACTIVE=staging \
       kimia/atlas-services:latest
   ```

4. Verify that the container starts successfully and connects to the staging services.
5. Access the Swagger UI at http://localhost:8000/swagger-ui.html to verify the API is working.
6. Test some API endpoints to ensure they can communicate with MongoDB and Keycloak running in the test containers.

## Testing in Jenkins

1. Push the changes to the repository.
2. Trigger a Jenkins build.
3. Monitor the Jenkins logs to ensure the application starts successfully with the staging profile.
4. Verify that the application can connect to the services defined in docker-compose.test.yaml.
5. Check that the API endpoints are working correctly in the Jenkins environment.

## Expected Results

- The base/local profile should connect to services on localhost.
- The dev profile should connect to staging services from outside Docker.
- The staging profile should connect to staging services from within the Docker network.
- All profiles should allow the application to function correctly with their respective service configurations.