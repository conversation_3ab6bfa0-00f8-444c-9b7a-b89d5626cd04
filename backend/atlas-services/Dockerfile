# Build stage
FROM eclipse-temurin:24-jdk AS build

RUN apt-get update && apt-get install -y maven

# Set the working directory
WORKDIR /app

# Copy the project files
COPY pom.xml .
COPY src src

# Build the application
RUN mvn clean package -DskipTests

# Runtime stage
FROM openjdk:24-jdk-slim

# Set the working directory
WORKDIR /app

# Copy the JAR file from the build stage (version-agnostic)
COPY --from=build /app/target/atlas-services-*.jar /app/atlas-services.jar

# Expose the port specified in docker-compose
EXPOSE 8000

# Start the application
ENTRYPOINT ["java", "-jar", "/app/atlas-services.jar"]
