# Atlas Services

This is the backend service for the Atlas application.

## Spring Profiles

The application supports multiple Spring profiles for different environments:

### Base/Local Profile (Default)

This is the default profile used for local development. It connects to services running on localhost.

To use this profile, you don't need to specify anything special. Just run the application normally:

```bash
./mvnw spring-boot:run
```

### Dev Profile

This profile is intended for development use when connecting to staging services from outside Docker. It allows developers to connect to the staging environment services (defined in `infrastructure/docker-compose.test.yaml`) from their local machine.

To use this profile, run the application with the `dev` profile activated:

```bash
./mvnw spring-boot:run -Dspring.profiles.active=dev
```

Or set the environment variable:

```bash
export SPRING_PROFILES_ACTIVE=dev
./mvnw spring-boot:run
```

### Staging Profile

This profile is intended for use in the Jenkins CI/CD pipeline and connects to services defined in `infrastructure/docker-compose.test.yaml`. It's configured to work within the Docker network used by the test environment.

To use this profile, run the application with the `staging` profile activated:

```bash
./mvnw spring-boot:run -Dspring.profiles.active=staging
```

Or set the environment variable:

```bash
export SPRING_PROFILES_ACTIVE=staging
./mvnw spring-boot:run
```

In the Jenkins pipeline, this profile is automatically activated by setting the `SPRING_PROFILES_ACTIVE` environment variable in the Docker container.

## Profile Configuration Details

### Base/Local Profile
- MongoDB: `mongodb://localhost:27017/atlas`
- Keycloak: `http://localhost:8080`
- Server port: 8050
- CORS allowed origins: `http://localhost:3000`

### Dev Profile
- MongoDB: `************************************************************************************`
- Keycloak: `http://localhost:9080`
- Server port: 8050
- CORS allowed origins: `http://localhost:3000`

### Staging Profile
- MongoDB: `*******************************************************************************************`
- Keycloak: `http://keycloak_auth_test:8080`
- Server port: 8000
- CORS allowed origins: `http://atlas_frontend_test:3000`