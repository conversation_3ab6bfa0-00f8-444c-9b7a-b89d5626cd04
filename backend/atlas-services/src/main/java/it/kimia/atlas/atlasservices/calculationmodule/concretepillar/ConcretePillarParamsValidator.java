package it.kimia.atlas.atlasservices.calculationmodule.concretepillar;

import it.kimia.atlas.atlasservices.calculationmodule.validator.ParamsValidator;
import it.kimia.atlas.atlasservices.calculationmodule.validator.model.ValidationResult;
import it.kimia.atlas.atlasservices.shared.model.FieldErrorDTO;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * Validator for ConcretePillarParams.
 * Validates parameters based on their presence in the request.
 * If a parameter is present, it validates its type and value.
 * If a parameter is missing, it's considered valid (allowing partial updates).
 */
@Component
public class ConcretePillarParamsValidator implements ParamsValidator {

    @Autowired
    private Validator validator;

    /**
     * Validates parameters for a RectangularBeamModule.
     *
     * @param params the parameters to validate, must be a RectangularBeamParams object
     * @return a validation result containing whether the validation passed and any error messages
     */
    @Override
    public ValidationResult validate(Object params) {
        if (params instanceof ConcretePillarParams concretePillarParams) {
            return validateRectangularBeamParams(concretePillarParams);
        } else {
            return new ValidationResult(false, "Invalid params type. Expected RectangularBeamParams");
        }
    }

    /**
     * Validates a ConcretePillarParams object using Spring validation.
     * Also performs additional validations not covered by annotations.
     *
     * @param params the ConcretePillarParams object to validate
     * @return a validation result containing whether the validation passed and any error messages
     */
    private ValidationResult validateRectangularBeamParams(ConcretePillarParams params) {
        // First, perform standard validation using annotations
        Set<ConstraintViolation<ConcretePillarParams>> violations = validator.validate(params);
        List<FieldErrorDTO> fieldErrors = new ArrayList<>();

        // Process standard validation errors
        if (!violations.isEmpty()) {
            for (ConstraintViolation<ConcretePillarParams> violation : violations) {
                String fieldName = violation.getPropertyPath().toString();
                String errorCode = violation.getMessage();
                String defaultMessage = "Validation failed for field: " + fieldName;

                fieldErrors.add(new FieldErrorDTO(fieldName, errorCode, defaultMessage));
            }
        }

        // Perform additional validations not covered by annotations
        performAdditionalValidations(params, fieldErrors);

        if (fieldErrors.isEmpty()) {
            return ValidationResult.valid();
        } else {
            return ValidationResult.invalid("Validation Failed", fieldErrors);
        }
    }

    /**
     * Performs additional validations not covered by standard annotations.
     * This includes complex business rules and cross-field validations.
     *
     * @param params      the ConcretePillarParams object to validate
     * @param fieldErrors the list of field errors to add to
     */
    private void performAdditionalValidations(ConcretePillarParams params, List<FieldErrorDTO> fieldErrors) {
        // Example of additional validations that might not be covered by annotations

        // Validate that effectiveDepth is less than or equal to height
        if (params.getGeometry() != null &&
                params.getGeometry().getEffectiveDepth() != null &&
                params.getGeometry().getHeight() != null) {

            if (params.getGeometry().getEffectiveDepth().compareTo(params.getGeometry().getHeight()) > 0) {
                fieldErrors.add(new FieldErrorDTO(
                        "geometry.effectiveDepth",
                        "validation.rectangularbeam.geometry.effectiveDepth.lessThanHeight",
                        "Effective depth must be less than or equal to height"
                ));
            }
        }

        // Validate that topConcreteCover + bottomConcreteCover is less than height
        if (params.getGeometry() != null &&
                params.getGeometry().getTopConcreteCover() != null &&
                params.getGeometry().getBottomConcreteCover() != null &&
                params.getGeometry().getHeight() != null) {

            BigDecimal totalCover = params.getGeometry().getTopConcreteCover()
                    .add(params.getGeometry().getBottomConcreteCover());

            if (totalCover.compareTo(params.getGeometry().getHeight()) >= 0) {
                fieldErrors.add(new FieldErrorDTO(
                        "geometry.concreteCover",
                        "validation.rectangularbeam.geometry.totalConcreteCover.lessThanHeight",
                        "Total concrete cover must be less than height"
                ));
            }
        }

        // Add more business rule validations as needed
    }

}