/**
 * This package represents the Security component in the Atlas Services application.
 * <p>
 * It provides functionality for managing authentication, authorization,
 * and user identity models like {@link it.kimia.atlas.atlasservices.security.model.IdpUser}.
 * <p>
 * This package is designed to be accessible by all other components in the application.
 */
package it.kimia.atlas.atlasservices.security;
