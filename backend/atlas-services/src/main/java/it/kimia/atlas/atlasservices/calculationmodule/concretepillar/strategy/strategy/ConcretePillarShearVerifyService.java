package it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.strategy;

import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.ConcretePillarParams;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input.ConcretePillarShearVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.output.ConcretePillarShearCalculationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ConcretePillarShearVerifyService implements ConcretePillarShearVerify {
    @Override
    public ConcretePillarShearCalculationResult execute(ConcretePillarParams params, ConcretePillarShearVerifyExecutionInput input) {
        return ConcretePillarShearCalculationResult.builder().build();
    }
}
