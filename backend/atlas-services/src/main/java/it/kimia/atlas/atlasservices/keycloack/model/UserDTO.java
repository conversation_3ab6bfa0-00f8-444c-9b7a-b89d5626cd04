package it.kimia.atlas.atlasservices.keycloack.model;

import lombok.Builder;
import lombok.Data;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;

import java.util.List;
import java.util.Map;

@Builder
@Data
public class UserDTO {

    private UserRepresentation userRepresentation;
    private List<RoleRepresentation> rolesRepresentation;
    private Map<String, List<UserRepresentation>> subGroupsRepresentation;
}
