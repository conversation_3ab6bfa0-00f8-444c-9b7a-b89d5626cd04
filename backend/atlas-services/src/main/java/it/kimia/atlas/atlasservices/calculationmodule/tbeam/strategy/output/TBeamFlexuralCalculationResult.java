package it.kimia.atlas.atlasservices.calculationmodule.tbeam.strategy.output;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder(toBuilder = true)
public class TBeamFlexuralCalculationResult {

    private boolean checkResult;
    private BigDecimal firstExperimentalData;
    private BigDecimal secondExperimentalData;
    private BigDecimal environmentalConversionFactor;
    private BigDecimal partialBendingFactor;
    private BigDecimal materialPartialFactor;
    private BigDecimal geometricCorrectionCoefficient;
    private BigDecimal optimalDesignAnchorageLength;
    private BigDecimal maxReinforcementStress;
    private BigDecimal maxReinforcementStrain;
    private BigDecimal maxDesignReinforcementStrain;
    private BigDecimal firstAdimensionalCoefficient;
    private BigDecimal secondAdimensionalCoefficient;
    private BigDecimal concreteStrain;
    private BigDecimal tensileSteelStrain;
    private BigDecimal compressedSteelStrain;
    private BigDecimal frpReinforcementStrain;
    private BigDecimal momentCapacity;
}
