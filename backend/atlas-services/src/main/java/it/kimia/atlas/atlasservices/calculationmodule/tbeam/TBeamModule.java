package it.kimia.atlas.atlasservices.calculationmodule.tbeam;

import it.kimia.atlas.atlasservices.calculationmodule.CalculationModule;
import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import it.kimia.atlas.atlasservices.calculationmodule.tbeam.strategy.input.TBeamFlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.tbeam.strategy.output.TBeamFlexuralCalculationResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * T-shaped beam calculation module.
 * <p>
 * This module is used for calculations on T-shaped beams.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TBeamModule extends CalculationModule {

    public static ModuleType moduleType = ModuleType.T_BEAM;

    private TBeamParams params;

    private TBeamFlexuralVerifyExecutionInput flexuralVerifyExecutionInput;
    private TBeamFlexuralCalculationResult flexuralVerifyExecutionResult;

    @Override
    public ModuleType getType() {
        return moduleType;
    }
}