package it.kimia.atlas.atlasservices.materials;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Service implementation for managing reinforced concrete materials using MongoDB.
 */
@Service
@RequiredArgsConstructor
public class ReinforcedConcreteServiceMongoDb implements ReinforcedConcreteService {

    private final ReinforcedConcreteRepository reinforcedConcreteRepository;

    /**
     * Get all reinforced concretes with pagination and optional filtering.
     *
     * @param pageable pagination information
     * @return a page of reinforced concretes matching the criteria
     */
    @Override
    public Page<ReinforcedConcrete> getReinforcedConcretes(Pageable pageable) {
        return reinforcedConcreteRepository.findAll(pageable);
    }

    /**
     * Get a reinforced concrete by ID.
     *
     * @param id the reinforced concrete ID
     * @return the reinforced concrete, if found
     */
    @Override
    public Optional<ReinforcedConcrete> getReinforcedConcreteById(String id) {
        return reinforcedConcreteRepository.findById(id);
    }

}