package it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.strategy;

import it.kimia.atlas.atlasservices.calculationmodule.Exposure;
import it.kimia.atlas.atlasservices.calculationmodule.KnowledgeLevel;
import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProduct;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.ConcretePillarParams;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input.ConcretePillarFlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.output.ConcretePillarFlexuralCalculationResult;
import it.kimia.atlas.atlasservices.calculationmodule.dto.MaterialPropertiesReinforcedConcreteClass;
import it.kimia.atlas.atlasservices.calculationmodule.dto.MaterialPropertiesSteelGrade;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services.EnvironmentalAndSafetyFactorService;
import it.kimia.atlas.atlasservices.calculationmodule.utility.RootFinder;
import it.kimia.atlas.atlasservices.product.FiberType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;

@Service
@RequiredArgsConstructor
@Slf4j
public class ConcretePillarFlexuralVerifyService implements ConcretePillarFlexuralVerify {

    // ε c0 [-]	Dato di progetto	0,00175
    public static final BigDecimal E_C0 = new BigDecimal("0.00175").setScale(SCALE, RoundingMode.HALF_UP);
    //ε cu [-]	Dato di progetto	0,00350
    public static final BigDecimal E_CU = new BigDecimal("0.00350").setScale(SCALE, RoundingMode.HALF_UP);
    public static final MathContext MC = new MathContext(SCALE, RoundingMode.HALF_UP);
    private final EnvironmentalAndSafetyFactorService environmentalAndSafetyFactorService;
    private final RootFinder rootFinder;

    @Override
    public ConcretePillarFlexuralCalculationResult execute(ConcretePillarParams params, ConcretePillarFlexuralVerifyExecutionInput input) {

        /*
         * DEFORMAZIONI INIZIALI
         */

        // C2 - Deformazione iniziale (mm)
        final BigDecimal C2 = params.getInitialDeformation();

        /*
         * MATERIALI ORIGINARI – GEOMETRIA
         */

        // C6 - b [mm]	Larghezza sezione -
        final BigDecimal C6 = params.getGeometry().getWidth();
        // C7 - h [mm]	Altezza sezione -
        final BigDecimal C7 = params.getGeometry().getHeight().setScale(SCALE, RoundingMode.HALF_UP);
        // C8 - c1 [mm]	Copriferro inferiore
        final BigDecimal C8 = params.getGeometry().getBottomConcreteCover().setScale(SCALE, RoundingMode.HALF_UP);
        // C9 - c2 [mm]	Copriferro superiore
        final BigDecimal C9 = params.getGeometry().getTopConcreteCover().setScale(SCALE, RoundingMode.HALF_UP);
        // C10 - d [mm]	Altezza utile
        final BigDecimal C10 = params.getGeometry().getEffectiveDepth().setScale(SCALE, RoundingMode.HALF_UP);
        // C11 - A s1 [mmq]	Armatura tesa (D25 s1 inferiore)
        final BigDecimal C11 = params.getReinforcementBar().getBottom().getArea().setScale(SCALE, RoundingMode.HALF_UP);
        // C12 - A s2 [mmq]	Armatura compressa (D2w s2 superiore)
        final BigDecimal C12 = params.getReinforcementBar().getTop().getArea().setScale(SCALE, RoundingMode.HALF_UP);

        /*
         * MATERIALI ORIGINARI – MATERIALI
         */


        final KnowledgeLevel knowledgeLevel = params.getMaterialProperties().getKnowledgeLevel();
        final MaterialPropertiesReinforcedConcreteClass concreteClass = params.getMaterialProperties().getConcreteClass();
        final MaterialPropertiesSteelGrade steelGrade = params.getMaterialProperties().getSteelGrade();

        // C16 - Rck [Mpa] - Resistenza a compressione cubica caratt. - getCubeCompressiveStrength
        final BigDecimal C16 = concreteClass.getCubeCompressiveStrength().setScale(SCALE, RoundingMode.HALF_UP);
        // C17 - fcd,d = fcm / (FC * γc) [Mpa]	Resistenza a compressione di progetto - meccanismi duttili
        final BigDecimal C17 = concreteClass.getDesignCompressiveStrengthForDuctileMechanisms();
        // C18 - fcm [Mpa]	Resistenza a compressione media (D39)
        final BigDecimal C18 = concreteClass.getAverageCompressiveStrength();
        // C19 - fctm [Mpa]	Resistenza a trazione media (D40)
        final BigDecimal C19 = concreteClass.getAverageTensileStrength();
        // C20 - ε c0 [-]	Dato di progetto
        final BigDecimal C20 = E_C0;
        // C21 - ε cu [-]	Dato di progetto
        final BigDecimal C21 = E_CU;
        // C22 - Ec [MPa]	Modulo elastico calcestruzzo
        final BigDecimal C22 = concreteClass.getElasticModulus();
        // C23 - fyk  [MPa]	Resistenza a snervamento caratt. dal materiale
        final BigDecimal C23 = steelGrade.getYieldStrength();
        // C24 - fyd,d = fyk / (FC * γs) [Mpa] Resistenza a snervamento di progetto - meccanismi duttili
        final BigDecimal C24 = steelGrade.getDesignYieldStrengthForDuctileMechanisms();
        // C26 - Es [MPa] 	Modulo elastico (C26 è necessario per calcolo di C26 e chi ha scritto l'excel non conosce le base delle matematica e come si scrivano i dati)
        final BigDecimal C26 = BigDecimal.valueOf(steelGrade.getElasticModulus());
        // C25 - ε yd [-] =C23/C26
        final BigDecimal C25 = C23.divide(C26, SCALE, RoundingMode.HALF_UP);
        // C27 - FC	Fattore di confidenza
        final BigDecimal C27 = knowledgeLevel.getValue().setScale(SCALE, RoundingMode.HALF_UP);

        /*
         * COMPOSITO – GEOMETRIA
         */

        final ReinforcedConcreteProduct product = input.getProduct();

        final FiberType C32 = product.getFiberType();
        // C34 tf [mm]	Spessore fascia
        final BigDecimal C34 = product.getThickness().setScale(SCALE, RoundingMode.HALF_UP);
        // C35 bf [mm]	Larghezza fascia
        final BigDecimal C35 = BigDecimal.valueOf(input.getStripWidth()).setScale(SCALE, RoundingMode.HALF_UP);
        // C36 nf [-]	Numero di strati
        final BigDecimal C36 = BigDecimal.valueOf(input.getLayersNumber()).setScale(SCALE, RoundingMode.HALF_UP);

        /*
         * COMPOSITO – PROPRIETA'
         */

        final Exposure exposure = params.getGeometry().getExposure();

        // C40 - Ef [Mpa]
        final BigDecimal C40 = product.getElasticModulus();
        // C41 - εf [-]
        final BigDecimal C41 = product.getSystemDeformation();

        // C43 - fattore ambientale
        final BigDecimal C43 = environmentalAndSafetyFactorService.getAmbientFactor(exposure, product)
                .setScale(SCALE, RoundingMode.HALF_UP);
        // C44 - γRd Fatt. parziale a flessione [Tab. 3.2]
        final BigDecimal C44 = BigDecimal.ONE;
        // C45 - γf	Fatt. parziale materiale [Tab. 3.1]
        final BigDecimal C45 = environmentalAndSafetyFactorService.calculateMaterialPartialFactor(product)
                .setScale(SCALE, RoundingMode.HALF_UP);
        // C46 bf/b	Rapporto largh. rinforzo/largh. sezione
        final BigDecimal C46 = this.getReinforcementWidthRatio(C35, C6);
        // C47 kb Coeff. correttivo geometrico [Par. 4.1.3]
        final BigDecimal C47 = this.getGeometricCorrectionCoefficient(C46);
        // C48 fbm [MPa]	Tensione tang. max di aderenza-valore medio [form. 4.2]
        final BigDecimal C48 = this.getAverageBondStress(
                C32,
                C18,
                C19,
                C27
        );
        // C49 fbk [MPa]	Tensione tang. max di aderenza-valore caratteristico [form. 4.10]
        final BigDecimal C49 = this.getCharacteristicBondStress(
                C32,
                C18,
                C19,
                C27
        );

        // C50 ΓFm [N/mm]	Energia specifica di frattura-valore medio [form. 4.3]
        final BigDecimal C50 = C48.multiply(BigDecimal.valueOf(0.125)).setScale(SCALE, RoundingMode.HALF_UP);
        // C51 ΓFk [N/mm]	Energia specifica di frattura-valore caratteristico [form. 4.9]
        final BigDecimal C51 = C49.multiply(BigDecimal.valueOf(0.125)).setScale(SCALE, RoundingMode.HALF_UP);
        // C52 Led [mm]	Lungh. ottimale di ancoraggio di progetto [form. 4.1] (output)
        final BigDecimal C52 = this.getOptimalDesignAnchorageLength(
                C32,
                C48,
                C40,
                C34,
                C36,
                C50
        );
        /*
         * Distacco di estremità (modo 1) di sistemi EBR con lunghezza di ancoraggio L ≥ Led
         * forse non necessario
         */
        final BigDecimal C54;
        final BigDecimal C55;
        final BigDecimal C56;
        /*
         * Distacco intermedio (modo 2) di sistemi EBR
         */
        final BigDecimal C58;
        final BigDecimal C59 = this.calculateMaxReinforcementStress(
                C18,
                C19,
                C27,
                C34,
                C36,
                C40,
                C47
        );
        final BigDecimal C60 = this.calculateMaxReinforcementStrain(C59, C40);

        /*
         * VERIFICA PER DISTACCO INTERMEDIO (modo 2)
         */

        final BigDecimal C64 = this.calculateDesignReinforcementStrain(C43, C41, C45, C60);
        final BigDecimal C65 = this.calculateDesignReinforcementStress(C60, C40);
        final BigDecimal C69 = this.calculateMechanicalTensileRebarRatio(
                C11, C24, C17, C6, C10
        );
        final BigDecimal C67 = this.calculateMechanicalLimitRatio(
                C21, C7, C10, C25, C2, C69, C12, C11
        );
        final BigDecimal C68 = this.calculateMechanicalReinforcementRatio(
                C35, C34, C36, C65, C17, C6, C10
        );
        final int C66 = this.getFailureMode(C67, C68);

        final BigDecimal appliedAxialForce = input.getAppliedAxialForce();


        BigDecimal lower = C9;
        BigDecimal upper = params.getGeometry().getEffectiveDepth();
        FlexuralEquilibriumState state = rootFinder.solve(
                lower, upper,
                x -> FlexuralEquilibriumState.compute(
                        x,
                        C66, C64, C2, C7, C10, C9, C21, C6, C17,
                        C12, C11, C35, C34, C36, C26, C23, C40, appliedAxialForce
                ),
                FlexuralEquilibriumState::getResidual
        );

        final BigDecimal C70 = state.getXNeutralAxis();
        final BigDecimal C71 = state.getResidual();
        final BigDecimal C72 = state.getC72();
        final BigDecimal C74 = state.getN_concrete();
        final BigDecimal C76 = state.getEpsC();    // εc (C76)
        final BigDecimal C73 = calculateC73(C76);
        final BigDecimal C75 = C73.multiply(C70);
        final BigDecimal C77 = state.getEpsS_t();  // εs,t (C77)
        final BigDecimal C78 = state.getEpsS_c();  // εs,c (C78)
        final BigDecimal C79 = state.getEpsFRP();  // εfrp (C79)
        final BigDecimal C80 = this.calculateC80(C76, C22, C17);
        final BigDecimal C81 = state.getSigmaS_t();   // σs,t (C81)
        final BigDecimal C82 = state.getSigmaS_c();   // σs,c (C82)
        final BigDecimal C83 = state.getSigmaFRP();   // σfrp  (C83)
        final BigDecimal C84 = calculateC84(C72, C6, C70, C17, C10, C73, C12, C82, C9, C35, C34, C36, C83, C8, appliedAxialForce, C7);
        final CheckResult result = checkFlexural(C84, input.getBendingMoment());

        final boolean checkResult = result.checkResult(); // true se soddisfatta
        final BigDecimal ratio = result.ratio();       // D61/D58

        return ConcretePillarFlexuralCalculationResult.builder()
                .firstExperimentalData(C40)
                .secondExperimentalData(C41)
                .environmentalConversionFactor(C43)
                .partialBendingFactor(C44)
                .materialPartialFactor(C45)
                .geometricCorrectionCoefficient(C47)
                .optimalDesignAnchorageLength(C52)
                .maxReinforcementStress(C59)
                .maxReinforcementStrain(C60)
                .maxDesignReinforcementStrain(C64)
                .firstAdimensionalCoefficient(C72)
                .secondAdimensionalCoefficient(C73)
                .concreteStrain(C76)
                .tensileSteelStrain(C77)
                .compressedSteelStrain(C78)
                .frpReinforcementStrain(C79)
                .resistantMoment(C84)
                .neutralAxisPosition(C70)
                .equilibrium(C71)
                .checkResult(checkResult)
                .checkValue(ratio)
                .build();
    }


    /**
     * bf/b	Rapporto largh. rinforzo/largh. sezione =SE(C35/C6<0,25;0,25;C35/C6)
     *
     * @param stripWidth C35
     * @param b          C6
     * @return
     */
    protected BigDecimal getReinforcementWidthRatio(
            final BigDecimal stripWidth, // C35
            final BigDecimal b // C6
    ) {
        final BigDecimal safeB = b.abs();
        final BigDecimal min = BigDecimal.valueOf(0.25).setScale(SCALE, RoundingMode.HALF_UP);

        if (safeB.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("Width cannot be zero.");
        }

        final BigDecimal ratio = stripWidth.abs().divide(safeB, SCALE, RoundingMode.HALF_UP);
        return (ratio.compareTo(min) < 0 ? min : ratio).setScale(SCALE, RoundingMode.HALF_UP);
    }


    //
    // =IF(((2-C59)/(1+C59))^0,5>=1;((2-C59)/(1+C59))^0,5;1)

    /**
     * C47 kb Coeff. correttivo geometrico [Par. 4.1.3]
     * =IF(((2-C46)/(1+C46))^0,5>1;((2-C46)/(1+C46))^0,5;1)
     *
     * @param reinforcementWidthRatio C46
     * @return
     */
    protected BigDecimal getGeometricCorrectionCoefficient(
            final BigDecimal reinforcementWidthRatio // C46
    ) {

        final BigDecimal two = BigDecimal.valueOf(2);
        final BigDecimal one = BigDecimal.ONE.setScale(SCALE, RoundingMode.HALF_UP);

        final BigDecimal numerator = two.subtract(reinforcementWidthRatio);
        final BigDecimal denominator = one.add(reinforcementWidthRatio);

        if (denominator.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("Denominator cannot be zero.");
        }

        BigDecimal fraction = numerator.divide(denominator, SCALE, RoundingMode.HALF_UP);
        double sqrtValue = Math.sqrt(fraction.doubleValue());
        BigDecimal result = BigDecimal.valueOf(sqrtValue).setScale(SCALE, RoundingMode.HALF_UP);

        return (result.compareTo(one) >= 0 ? result : one).setScale(SCALE, RoundingMode.HALF_UP);
    }

    /**
     * C48 fbm [MPa]	Tensione tang. max di aderenza-valore medio [form. 4.2]
     * =IF(C32="Carbonio preformato";0,8*((C18*C19)^0,5)/(2*C27);1,25*((C18*C19)^0,5)/(2*C27))
     *
     * @param productType                C32
     * @param averageCompressiveStrength C18
     * @param averageTensileStrength     C19
     * @param confidenceFactor           C27
     * @return
     */
    protected BigDecimal getAverageBondStress(
            FiberType productType, // C32
            BigDecimal averageCompressiveStrength, // C18
            BigDecimal averageTensileStrength,     // C19
            BigDecimal confidenceFactor            // C27
    ) {
        if (confidenceFactor.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("ConfidenceFactor cannot be zero.");
        }
        final BigDecimal factor = FiberType.PREFORMED_CARBON == productType
                ? BigDecimal.valueOf(0.8)
                : BigDecimal.valueOf(1.25);

        final BigDecimal sqrt = BigDecimal.valueOf(
                Math.sqrt(averageCompressiveStrength.multiply(averageTensileStrength).doubleValue())
        );
        final BigDecimal denominator = confidenceFactor.multiply(BigDecimal.valueOf(2));
        return factor.multiply(sqrt).divide(denominator, SCALE, RoundingMode.HALF_UP);
    }

    /**
     * C49 fbk [MPa]	Tensione tang. max di aderenza-valore caratteristico [form. 4.10]
     * =IF(C37="Carbonio preformato";0,35*((C23*C24)^0,5)/(2*C32);0,6*((C23*C24)^0,5)/(2*C32))
     *
     * @param productType                C37
     * @param averageCompressiveStrength C23
     * @param averageTensileStrength     C24
     * @param confidenceFactor           C32
     * @return characteristicBondStress
     */
    protected BigDecimal getCharacteristicBondStress(
            FiberType productType, // C32
            BigDecimal averageCompressiveStrength, // C18
            BigDecimal averageTensileStrength,     // C19
            BigDecimal confidenceFactor            // C27
    ) {
        if (confidenceFactor.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("ConfidenceFactor cannot be zero.");
        }
        final BigDecimal factor = FiberType.PREFORMED_CARBON == productType
                ? BigDecimal.valueOf(0.35)
                : BigDecimal.valueOf(0.6);

        final BigDecimal sqrt = BigDecimal.valueOf(
                Math.sqrt(averageCompressiveStrength.multiply(averageTensileStrength).doubleValue())
        );
        final BigDecimal denominator = confidenceFactor.multiply(BigDecimal.valueOf(2));
        return factor.multiply(sqrt).divide(denominator, SCALE, RoundingMode.HALF_UP);
    }

    /**
     * C52 Led [mm]	Lungh. ottimale di ancoraggio di progetto [form. 4.1]
     * =IF(C32="Carbonio preformato";MAX(250;1,2/C48*(((PI()^2)*C40*C34*C36*C50/2)^0,5));MAX(100;1,2/C48*(((PI()^2)*C40*C34*C36*C50/2)^0,5)))
     * *
     *
     * @param productType           C32
     * @param averageBondStress     C48
     * @param elasticModulus        C40
     * @param thickness             C34
     * @param layersNumber          C36
     * @param averageFractureEnergy C50
     * @return optimalDesignAnchorageLength
     */
    protected BigDecimal getOptimalDesignAnchorageLength(
            FiberType productType,           // C32
            BigDecimal averageBondStress,    // C48
            BigDecimal elasticModulus,       // C40
            BigDecimal thickness,            // C34
            BigDecimal layersNumber,         // C36
            BigDecimal averageFractureEnergy // C50
    ) {
        if (averageBondStress.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("averageBondStress cannot be zero.");
        }

        final double piSquared = Math.PI * Math.PI;
        final BigDecimal base = BigDecimal.valueOf(piSquared)
                .multiply(elasticModulus)
                .multiply(thickness)
                .multiply(layersNumber)
                .multiply(averageFractureEnergy)
                .divide(BigDecimal.valueOf(2), SCALE, RoundingMode.HALF_UP);

        final BigDecimal sqrt = BigDecimal.valueOf(Math.sqrt(base.doubleValue()));
        final BigDecimal value = BigDecimal.valueOf(1.2)
                .divide(averageBondStress, SCALE, RoundingMode.HALF_UP)
                .multiply(sqrt)
                .setScale(SCALE, RoundingMode.HALF_UP);

        final BigDecimal min = FiberType.PREFORMED_CARBON == productType
                ? BigDecimal.valueOf(250)
                : BigDecimal.valueOf(100);

        return value.max(min).setScale(SCALE, RoundingMode.HALF_UP);
    }


    /**
     * C59 ffdd,2 [MPa]	Tensione max rinforzo modo 2 - valore di progetto [form. 4.12]
     * =1,25*C47/1,3*((2*C40*1,6/4/C27*((C18*C19)^0,5)*0,25)/(C34*C36))^0,5
     *
     * @param C18 dato di progetto (es. 48)
     * @param C19 dato di progetto (es. 3.567...)
     * @param C27 fattore di confidenza
     * @param C34 spessore fascia
     * @param C36 numero di strati
     * @param C40 dato sperimentale (es. modulo elastico)
     * @param C47 coefficiente correttivo geometrico [Par. 4.1.3]
     * @return tensione massima di progetto (MPa)
     */
    public BigDecimal calculateMaxReinforcementStress(
            BigDecimal C18,
            BigDecimal C19,
            BigDecimal C27,
            BigDecimal C34,
            BigDecimal C36,
            BigDecimal C40,
            BigDecimal C47
    ) {
        // sqrt(C18 * C19)
        final double sqrtC18C19 = Math.sqrt(C18.multiply(C19, MC).doubleValue());

        // termNum = 2*C40*1.6/4/C27 * sqrt(C18*C19) * 0.25
        final BigDecimal termNum = C40.multiply(BigDecimal.valueOf(2), MC)
                .multiply(BigDecimal.valueOf(1.6), MC)
                .divide(BigDecimal.valueOf(4), MC)
                .divide(C27, MC)
                .multiply(BigDecimal.valueOf(sqrtC18C19), MC)
                .multiply(BigDecimal.valueOf(0.25), MC);

        // termDen = C34 * C36
        final BigDecimal termDen = C34.multiply(C36, MC);

        // term = termNum / termDen
        final BigDecimal term = termNum.divide(termDen, MC);

        // sqrt(term)
        final double sqrtTerm = Math.sqrt(term.doubleValue());

        // result = 1.25 * C47 / 1.3 * sqrtTerm
        final BigDecimal result = BigDecimal.valueOf(1.25)
                .multiply(C47, MC)
                .divide(BigDecimal.valueOf(1.3), MC)
                .multiply(BigDecimal.valueOf(sqrtTerm), MC);

        return result.setScale(SCALE, RoundingMode.HALF_UP);
    }

    /**
     * C60 εfdd,2 [-]	Deformazione max rinforzo modo 2  [form. 4.13]
     *
     * @param maxReinforcementStress risultato di C59 (tensione massima rinforzo)
     * @param C40                    dato sperimentale (es. modulo elastico del rinforzo)
     * @return deformazione massima (adimensionale)
     */
    public BigDecimal calculateMaxReinforcementStrain(
            BigDecimal maxReinforcementStress,
            BigDecimal C40
    ) {
        return maxReinforcementStress.divide(C40, MC);
    }

    /**
     * C64 (form. 4.51).
     * Restituisce la deformazione massima di progetto del rinforzo.
     *
     * @param C43 parametro sperimentale/coefficiente (dal foglio Excel)
     * @param C41 parametro sperimentale/coefficiente (dal foglio Excel)
     * @param C45 parametro sperimentale/coefficiente (dal foglio Excel)
     * @param C60 risultato di C60 (ε_fr,max modo 2)
     * @return deformazione massima di progetto del rinforzo
     */
    public BigDecimal calculateDesignReinforcementStrain(
            BigDecimal C43,
            BigDecimal C41,
            BigDecimal C45,
            BigDecimal C60
    ) {
        BigDecimal value1 = C43.multiply(C41, MC).divide(C45, MC);
        return value1.min(C60);
    }

    /**
     * C65 = C64 * C40
     * "Tensione max di progetto del rinforzo"
     *
     * @param designReinforcementStrain C64 (deformazione max di progetto del rinforzo)
     * @param C40                       dato sperimentale (es. modulo elastico del rinforzo)
     * @return tensione massima di progetto del rinforzo
     */
    public BigDecimal calculateDesignReinforcementStress(
            BigDecimal designReinforcementStrain,
            BigDecimal C40
    ) {
        return designReinforcementStrain.multiply(C40, MC);
    }

    /**
     * dati_Flessione!C66 = IF(C68 <= C67, 1, 2)
     * "Par *******"
     *
     * @param mechanicalLimit C67 = percentuale meccanica limite (form. 13.9 CNR DT R1)
     * @param mechanicalRatio C68 = percentuale meccanica di rinforzo (form. 13.8 CNR DT R1)
     * @return 1 se mechanicalRatio <= mechanicalLimit, altrimenti 2
     */
    public int getFailureMode(BigDecimal mechanicalLimit, BigDecimal mechanicalRatio) {
        return (mechanicalRatio.compareTo(mechanicalLimit) <= 0) ? 1 : 2;
    }

    /**
     * C67
     * "Percentuale meccanica limite [form. 13.9 CNR DT R1]"
     *
     * @param C21 parametro sezione
     * @param C7  parametro geometrico
     * @param C10 parametro geometrico
     * @param C25 parametro sezione
     * @param C2  parametro sezione
     * @param C69 coefficiente correttivo
     * @param C12 parametro materiale
     * @param C11 parametro materiale
     * @return percentuale meccanica limite
     */
    public BigDecimal calculateMechanicalLimitRatio(
            BigDecimal C21,
            BigDecimal C7,
            BigDecimal C10,
            BigDecimal C25,
            BigDecimal C2,
            BigDecimal C69,
            BigDecimal C12,
            BigDecimal C11
    ) {
        final BigDecimal num = BigDecimal.valueOf(0.8).multiply(C21).multiply(C7);
        final BigDecimal frac = num.divide(C10, MC);
        final BigDecimal denom = C21.add(C25, MC).add(C2, MC);

        final BigDecimal term1 = frac.divide(denom, MC);

        final BigDecimal term2 = C69.multiply(
                BigDecimal.ONE.subtract(C12.divide(C11, MC), MC),
                MC
        );

        return term1.subtract(term2, MC);
    }

    /**
     * C68
     * "Percentuale meccanica di rinforzo [form. 13.8 CNR DT R1]"
     *
     * @param C35 parametro materiale
     * @param C34 spessore fascia
     * @param C36 numero di strati
     * @param C65 tensione max di progetto del rinforzo
     * @param C17 parametro geometrico
     * @param C6  parametro geometrico
     * @param C10 parametro geometrico
     * @return percentuale meccanica di rinforzo
     */
    public BigDecimal calculateMechanicalReinforcementRatio(
            BigDecimal C35,
            BigDecimal C34,
            BigDecimal C36,
            BigDecimal C65,
            BigDecimal C17,
            BigDecimal C6,
            BigDecimal C10
    ) {
        BigDecimal num = C35.multiply(C34.multiply(C36, MC), MC).multiply(C65, MC);
        BigDecimal denom = C17.multiply(C6, MC).multiply(C10, MC);
        return num.divide(denom, MC);
    }

    /**
     * dati_Flessione!C69 = C11 * C24 / (C17 * C6 * C10)
     * "Percentuale meccanica armatura tesa [form. 13.10 CNR DT R1]"
     *
     * @param C11 armatura tesa
     * @param C24 dato di progetto
     * @param C17 dato di progetto
     * @param C6  larghezza sezione
     * @param C10 altezza utile
     * @return percentuale meccanica dell'armatura tesa
     */
    public BigDecimal calculateMechanicalTensileRebarRatio(
            BigDecimal C11,
            BigDecimal C24,
            BigDecimal C17,
            BigDecimal C6,
            BigDecimal C10
    ) {
        BigDecimal denominator = C17.multiply(C6, MC).multiply(C10, MC);
        return C11.multiply(C24, MC).divide(denominator, MC);
    }

    /**
     * C73 (Coeff. adimensionale), da Excel:
     * =IF(C76<=0.002, (8-1000*C76)/4/(6-1000*C76),
     * (1000*C76*(3000*C76-4)+2)/(2000*C76*(3000*C76-2)))
     *
     * @param epsC C76 = deformazione di compressione del cls
     * @return C73
     */
    private BigDecimal calculateC73(BigDecimal epsC) {
        BigDecimal threshold = new BigDecimal("0.002");
        if (epsC.compareTo(threshold) <= 0) {
            BigDecimal num = new BigDecimal("8")
                    .subtract(new BigDecimal("1000").multiply(epsC, MC), MC);
            BigDecimal den = new BigDecimal("4")
                    .multiply(new BigDecimal("6")
                            .subtract(new BigDecimal("1000").multiply(epsC, MC), MC), MC);
            return num.divide(den, MC);
        } else {
            BigDecimal num = new BigDecimal("1000").multiply(epsC, MC)
                    .multiply(new BigDecimal("3000").multiply(epsC, MC)
                            .subtract(new BigDecimal("4"), MC), MC)
                    .add(new BigDecimal("2"), MC);
            BigDecimal den = new BigDecimal("2000").multiply(epsC, MC)
                    .multiply(new BigDecimal("3000").multiply(epsC, MC)
                            .subtract(new BigDecimal("2"), MC), MC);
            return num.divide(den, MC);
        }
    }

    private BigDecimal calculateC80(BigDecimal epsC, BigDecimal Ecm, BigDecimal fcd) {
        BigDecimal sigmaC = epsC.multiply(Ecm, MC);
        return sigmaC.compareTo(fcd) < 0 ? sigmaC : fcd;
    }

    /**
     * C84 (Momento resistente [kNm]) – Excel:
     * =((C72*C6*C70*C17*(C10-C73*C70) + C12*C82*(C10-C9) + C35*C34*C36*C83*C8)
     * - (Ned_kN*1000*(C7/2 - C8))) / 1e6
     * <p>
     * N.B.:  /1000/1000 converte N·mm → kNm
     */
    private BigDecimal calculateC84(
            BigDecimal C72, BigDecimal C6, BigDecimal C70, BigDecimal C17,
            BigDecimal C10, BigDecimal C73, BigDecimal C12, BigDecimal C82,
            BigDecimal C9, BigDecimal C35, BigDecimal C34, BigDecimal C36,
            BigDecimal C83, BigDecimal C8, BigDecimal Ned_kN, BigDecimal C7
    ) {
        BigDecimal termConcrete = C72.multiply(C6, MC).multiply(C70, MC).multiply(C17, MC)
                .multiply(C10.subtract(C73.multiply(C70, MC), MC), MC);

        BigDecimal termSteelC = C12.multiply(C82, MC)
                .multiply(C10.subtract(C9, MC), MC);

        BigDecimal termFrp = C35.multiply(C34, MC).multiply(C36, MC)
                .multiply(C83, MC).multiply(C8, MC);

        BigDecimal termN = Ned_kN.multiply(new BigDecimal("1000"), MC)
                .multiply(C7.divide(new BigDecimal("2"), MC).subtract(C8, MC), MC);

        BigDecimal numerator = termConcrete.add(termSteelC, MC).add(termFrp, MC).subtract(termN, MC);
        return numerator.divide(new BigDecimal("1000000"), MC); // kNm
    }

    private CheckResult checkFlexural(BigDecimal resistance, BigDecimal demand) {
        if (demand.signum() == 0) {
            return new CheckResult(false, BigDecimal.ZERO);
        }
        BigDecimal ratio = resistance.divide(demand, MathContext.DECIMAL64);
        boolean checkResult = resistance.compareTo(demand) >= 0;
        return new CheckResult(checkResult, ratio);
    }

    public record CheckResult(boolean checkResult, BigDecimal ratio) {
    }
}
