package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamParams;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.output.FlexuralCalculationResult;

public interface RectangularBeamFlexuralVerify {
    FlexuralCalculationResult execute(final RectangularBeamParams params,
                                      final FlexuralVerifyExecutionInput input);
}
