package it.kimia.atlas.atlasservices.project;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Arrays;

/**
 * Enum representing the types of projects in the system.
 */
public enum ProjectType {
    RECTANGULAR_BEAM("rectangular_beam"),
    T_BEAM("t_beam"),
    MASONRY("masonry"),
    WOOD("wood"),
    PILLAR("pillar"),
    ANTI_OVERTURNING("anti_overturning"),
    UNKNOWN("unknown");

    private final String value;

    ProjectType(String value) {
        this.value = value;
    }

    /**
     * Convert a string value to the corresponding enum value.
     *
     * @param value the string value
     * @return the corresponding enum value, or null if not found
     */
    @JsonCreator
    public static ProjectType fromValue(String value) {
        return Arrays.stream(ProjectType.values())
                .filter(category -> category.value.equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unknown category: " + value));
    }

    @JsonValue
    public String getValue() {
        return value;
    }
}