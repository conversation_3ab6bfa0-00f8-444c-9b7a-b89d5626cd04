package it.kimia.atlas.atlasservices.project.dtos;

import it.kimia.atlas.atlasservices.project.Currency;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

/**
 * DTO for updating an existing project.
 * Fields are optional to allow for partial updates.
 */
@Data
public class ProjectUpdateDTO {

    @Size(max = 500, message = "validation.project.plannedWorkDescription.size")
    private String constructionSiteName;

    @Size(max = 500, message = "validation.project.plannedWorkDescription.size")
    private String plannedWorkDescription;

    private String address;

    @DecimalMin(value = "0.0", inclusive = false, message = "validation.project.baseTenderAmount.decimalmin")
    private BigDecimal baseTenderAmount;

    private Currency baseTenderCurrency;

    private String planner;

    private String company;

    private String processingType;

    @DecimalMin(value = "0.0", inclusive = false, message = "validation.project.machiningSurfaceSize.decimalmin")
    private BigDecimal machiningSurfaceSize;
}
