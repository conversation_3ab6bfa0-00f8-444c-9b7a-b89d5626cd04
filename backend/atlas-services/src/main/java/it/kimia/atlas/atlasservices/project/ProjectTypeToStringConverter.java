package it.kimia.atlas.atlasservices.project;

import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.WritingConverter;
import org.springframework.stereotype.Component;

@Component
@WritingConverter
public class ProjectTypeToStringConverter implements Converter<ProjectType, String> {

    @Override
    public String convert(ProjectType source) {
        return source == null ? null : source.getValue();
    }
}