package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input;

import it.kimia.atlas.atlasservices.calculationmodule.dto.ReinforcementLayout;
import it.kimia.atlas.atlasservices.calculationmodule.dto.product.ConcreteCalculationProductInput;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Input for the SHEAR_VERIFY calculation strategy.
 * Contains parameters specific to shear verification calculations.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RectangularBeamShearVerifyInput {

    /**
     * The product to be used in the calculation. This can be an existing product
     * from the database (specified by ID) or a custom product with user-defined properties.
     */
    @NotNull(message = "validation.shearverify.product.notnull")
    @Valid
    private ConcreteCalculationProductInput product;

    /**
     * The reinforcement layout to be used in the calculation.
     */
    @NotNull(message = "validation.shearverify.reinforcementLayout.notnull")
    private ReinforcementLayout reinforcementLayout;

    /**
     * Clear web height h_w "Altezza libera anima"
     */
    @NotNull(message = "validation.shearverify.webHeight.notnull")
    @Positive(message = "validation.shearverify.webHeight.positive")
    @Valid
    private int webHeight;

    /**
     * The strip width to verify against, in mm.
     */
    @NotNull(message = "validation.shearverify.stripWidth.notnull")
    @Positive(message = "validation.shearverify.stripWidth.positive")
    private int stripWidth;

    /**
     * Strip space along element axis, in mm.
     */
    @NotNull(message = "validation.shearverify.stripSpacingAlongElementAxis.notnull")
    @Positive(message = "validation.shearverify.stripSpacingAlongElementAxis.positive")
    private int stripSpacingAlongElementAxis;

    /**
     * Strip space orthogonal to element axis, in mm.
     */
    @NotNull(message = "validation.shearverify.stripSpacingOrthogonalElementAxis.notnull")
    @Positive(message = "validation.shearverify.stripSpacingOrthogonalElementAxis.positive")
    private int stripSpacingOrthogonalElementAxis;

    /**
     * The number of the layers to apply in calculations.
     */
    @NotNull(message = "validation.shearverify.layersNumber.notnull")
    @Positive(message = "validation.shearverify.layersNumber.positive")
    private int layersNumber;

    /**
     * Strip inclination. β (Inclinazione delle fasce)
     */
    @NotNull(message = "validation.shearverify.stripInclination.notnull")
    @Min(value = 0, message = "validation.shearverify.stripInclination.decimalmin")
    @Max(value = 90, message = "validation.shearverify.stripInclination.decimalmax")
    private int stripInclination;

    /**
     * Concrete strut inclination. θ (Inclinazione bielle cls)
     */
    @NotNull(message = "validation.shearverify.concreteStrutInclination.notnull")
    @Min(value = 22, message = "validation.shearverify.concreteStrutInclination.decimalmin")
    @Max(value = 45, message = "validation.shearverify.concreteStrutInclination.decimalmax")
    private int concreteStrutInclination;

    /**
     * Applied shear force. Vsd [kN] Taglio sollecitante
     */
    @NotNull(message = "validation.shearverify.appliedShearForce.notnull")
    @Positive(message = "validation.shearverify.appliedShearForce.positive")
    private BigDecimal appliedShearForce;


}