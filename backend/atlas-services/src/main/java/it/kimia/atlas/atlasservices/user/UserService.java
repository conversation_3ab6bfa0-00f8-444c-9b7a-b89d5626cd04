package it.kimia.atlas.atlasservices.user;

import it.kimia.atlas.atlasservices.shared.model.CurrentUser;
import org.springframework.security.access.AccessDeniedException;

public interface UserService {

    /**
     * Get the current authenticated user.
     *
     * @return the current user
     * @throws AccessDeniedException if no user is authenticated
     */
    CurrentUser getCurrentUser() throws AccessDeniedException;
}
