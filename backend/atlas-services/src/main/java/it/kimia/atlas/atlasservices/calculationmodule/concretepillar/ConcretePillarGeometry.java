package it.kimia.atlas.atlasservices.calculationmodule.concretepillar;

import it.kimia.atlas.atlasservices.calculationmodule.Exposure;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Geometry parameters for pillar calculations.
 * <p>
 * This class contains the geometric parameters specific to Pillars.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConcretePillarGeometry {
    @NotNull(message = "validation.pillar.geometry.width.notnull")
    private BigDecimal width;

    @NotNull(message = "validation.pillar.geometry.height.notnull")
    private BigDecimal height;

    @NotNull(message = "validation.pillar.geometry.topConcreteCover.notnull")
    private BigDecimal topConcreteCover;

    @NotNull(message = "validation.pillar.geometry.bottomConcreteCover.notnull")
    private BigDecimal bottomConcreteCover;

    @NotNull(message = "validation.pillar.geometry.effectiveDepth.notnull")
    private BigDecimal effectiveDepth;

    @NotNull(message = "validation.pillar.geometry.exposure.notnull")
    private Exposure exposure;

}