package it.kimia.atlas.atlasservices.product.mappers;

import it.kimia.atlas.atlasservices.product.Product;
import it.kimia.atlas.atlasservices.product.dtos.ProductCreateDTO;
import it.kimia.atlas.atlasservices.product.dtos.ProductUpdateDTO;
import org.mapstruct.*;

/**
 * Mapper for the Product entity and its DTOs.
 */
@Mapper(componentModel = "spring")
public interface ProductMapper {

    /**
     * Maps a ProductCreateDTO to a Product entity.
     * Fields managed by the system (id) are ignored.
     *
     * @param dto the source DTO
     * @return the mapped Product entity
     */
    @Mapping(target = "id", ignore = true)
    Product toEntity(ProductCreateDTO dto);

    /**
     * Updates an existing Product entity from a ProductUpdateDTO.
     * Null properties in the DTO will be ignored, preserving existing values in the entity.
     *
     * @param dto     the source DTO with new values
     * @param product the target entity to be updated
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateProductFromDto(ProductUpdateDTO dto, @MappingTarget Product product);
}