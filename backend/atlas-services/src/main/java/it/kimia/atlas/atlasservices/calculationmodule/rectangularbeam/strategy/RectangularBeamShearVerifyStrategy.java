package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy;

import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamModule;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamParams;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.RectangularBeamShearVerifyInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.ShearVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.mapper.ShearVerifyExecutionInputMapper;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.output.ShearCalculationResult;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services.RectangularBeamShearVerify;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationStrategy;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationType;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.StrategyInputValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Strategy implementation for SHEAR_VERIFY calculations on RectangularBeamModule.
 * <p>
 * This strategy performs shear verification calculations on rectangular beam modules.
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RectangularBeamShearVerifyStrategy implements CalculationStrategy<RectangularBeamModule, RectangularBeamShearVerifyInput> {

    private final StrategyInputValidator<RectangularBeamShearVerifyInput> rectangularBeamShearInputValidatorStrategy;
    private final ShearVerifyExecutionInputMapper shearVerifyExecutionInputMapper;
    private final RectangularBeamShearVerify rectangularBeamShearVerify;


    @Override
    public Class<RectangularBeamShearVerifyInput> getInputType() {
        return RectangularBeamShearVerifyInput.class;
    }

    @Override
    public RectangularBeamModule execute(RectangularBeamModule module, RectangularBeamShearVerifyInput input) {
        // Get module parameters
        RectangularBeamParams params = module.getParams();
        if (params == null) {
            throw new IllegalStateException("Module parameters are not set");
        }
        // Validate input
        rectangularBeamShearInputValidatorStrategy.validate(input);
        final ShearVerifyExecutionInput shearVerifyExecutionInput = shearVerifyExecutionInputMapper.map(input);
        final ShearCalculationResult result = rectangularBeamShearVerify.execute(params, shearVerifyExecutionInput);

        module.setShearVerifyExecutionInput(shearVerifyExecutionInput);
        module.setShearCalculationResult(result);

        return module;
    }

    @Override
    public ModuleType getModuleType() {
        return ModuleType.RECTANGULAR_BEAM;
    }

    @Override
    public CalculationType getCalculationType() {
        return CalculationType.SHEAR_VERIFY;
    }


}