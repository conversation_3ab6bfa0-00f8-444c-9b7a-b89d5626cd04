package it.kimia.atlas.atlasservices.calculationmodule;

import lombok.Getter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;


/**
 * Rappresenta la tabella di ricerca per i fattori di conversione ambientali.
 * Questo enum è una traduzione diretta della tabella fornita, mappando
 * un tipo di Esposizione ai fattori corrispondenti per i diversi materiali.
 */
@Getter
public enum AmbientFactorData {
    INTERNAL(Exposure.INTERNAL, "0.75", "0.95", "0.95", "0.95"),
    EXTERNAL(Exposure.EXTERNAL, "0.65", "0.95", "0.80", "0.80"),
    AGGRESSIVE(Exposure.AGGRESSIVE, "0.50", "0.90", "0.75", "0.75");

    private final Exposure exposure;
    private final BigDecimal glassFactor;
    private final BigDecimal preformedCarbonFactor;
    private final BigDecimal carbonFactor;
    private final BigDecimal steelFactor;

    AmbientFactorData(Exposure exposure, String glass, String preformedCarbon, String carbon, String steel) {
        this.exposure = exposure;
        this.glassFactor = new BigDecimal(glass).setScale(SCALE, RoundingMode.HALF_UP);
        this.preformedCarbonFactor = new BigDecimal(preformedCarbon).setScale(SCALE, RoundingMode.HALF_UP);
        this.carbonFactor = new BigDecimal(carbon).setScale(SCALE, RoundingMode.HALF_UP);
        this.steelFactor = new BigDecimal(steel).setScale(SCALE, RoundingMode.HALF_UP);
    }

    public static AmbientFactorData fromExposure(Exposure exposure) {
        return Arrays.stream(values())
                .filter(data -> data.exposure == exposure)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Ambient factor not found for exposure: " + exposure));
    }
}