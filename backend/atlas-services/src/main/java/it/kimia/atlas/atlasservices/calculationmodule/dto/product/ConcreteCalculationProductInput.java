package it.kimia.atlas.atlasservices.calculationmodule.dto.product;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

/**
 * Base class for specifying a product for a calculation.
 * The 'sourceType' property in the JSON input will determine whether
 * to use an existing product from the database or a custom one.
 */
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.PROPERTY,
        property = "sourceType" // This field in the JSON will decide the class
)
@JsonSubTypes({
        @JsonSubTypes.Type(value = ExistingProductInputConcrete.class, name = "DATABASE"),
        @JsonSubTypes.Type(value = CustomProductInputConcrete.class, name = "CUSTOM")
})
public abstract class ConcreteCalculationProductInput {
}