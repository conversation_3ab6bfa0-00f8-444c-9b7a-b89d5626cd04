package it.kimia.atlas.atlasservices.project.dtos;

import it.kimia.atlas.atlasservices.project.Currency;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProjectCreateDTO {
    @NotBlank(message = "validation.project.address.notblank")
    private String address;

    @DecimalMin(value = "0.0", inclusive = false, message = "validation.project.baseTenderAmount.decimalmin")
    private BigDecimal baseTenderAmount;

    private Currency baseTenderCurrency;

    @NotBlank(message = "validation.project.company.notblank")
    private String company;

    @NotBlank(message = "validation.project.constructionSiteName.notblank")
    @Size(max = 100, message = "validation.project.constructionSiteName.size")
    private String constructionSiteName;
    
    @DecimalMin(value = "0.0", inclusive = false, message = "validation.project.machiningSurfaceSize.decimalmin")
    private BigDecimal machiningSurfaceSize;

    @NotBlank(message = "validation.project.planner.notblank")
    private String planner;

    @NotBlank(message = "validation.project.plannedWorkDescription.notblank")
    @Size(max = 500, message = "validation.project.plannedWorkDescription.size")
    private String plannedWorkDescription;

    private String processingType;
}