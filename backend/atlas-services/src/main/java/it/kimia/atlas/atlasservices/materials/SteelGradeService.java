package it.kimia.atlas.atlasservices.materials;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

/**
 * Service interface for managing steel grade materials.
 */
public interface SteelGradeService {

    /**
     * Get all steel grades with pagination and optional filtering.
     *
     * @param pageable pagination information
     * @return a page of steel grades matching the criteria
     */
    Page<SteelGrade> getSteelGrades(Pageable pageable);

    /**
     * Get a steel grade by ID.
     *
     * @param id the steel grade ID
     * @return the steel grade, if found
     */
    Optional<SteelGrade> getSteelGradeById(String id);

}