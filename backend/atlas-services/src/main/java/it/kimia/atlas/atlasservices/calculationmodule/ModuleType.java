package it.kimia.atlas.atlasservices.calculationmodule;

/**
 * Defines the valid types of calculation modules within the system.
 * <p>
 * This enum is used for type safety and for polymorphic deserialization,
 * ensuring that only known and supported module types can be created and processed.
 * The names correspond to the legacy types for mapping purposes.
 */
public enum ModuleType {

    /**
     * Rectangular beam calculation module. (Legacy: 'trave')
     */
    RECTANGULAR_BEAM,

    /**
     * T-shaped beam calculation module. (Legacy: 'travet')
     */
    T_BEAM,

    /**
     * Masonry structure calculation module. (Legacy: 'muratura')
     */
    MASONRY,

    /**
     * Wood structure calculation module. (Legacy: 'legno')
     */
    WOOD,

    /**
     * Pillar calculation module. (Legacy: 'pilastro')
     */
    PILLAR,

    /**
     * Anti-overturning verification module. (Legacy: 'antirib')
     */
    ANTI_OVERTURNING
}