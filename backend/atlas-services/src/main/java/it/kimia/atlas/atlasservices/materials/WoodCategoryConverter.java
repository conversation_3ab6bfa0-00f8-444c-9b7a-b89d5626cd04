package it.kimia.atlas.atlasservices.materials;

import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.ReadingConverter;
import org.springframework.stereotype.Component;

/**
 * Custom converter for WoodCategory enum.
 * Handles unknown category values from MongoDB by returning WoodCategory.UNKNOWN
 * instead of throwing an exception.
 */
@Component
@ReadingConverter
public class WoodCategoryConverter implements Converter<String, WoodCategory> {

    @Override
    public WoodCategory convert(String source) {
        if (source == null) {
            return null;
        }

        try {
            return WoodCategory.valueOf(source);
        } catch (IllegalArgumentException e) {
            return WoodCategory.UNKNOWN;
        }
    }
}