package it.kimia.atlas.atlasservices.project;

import it.kimia.atlas.atlasservices.project.dtos.ProjectCreateDTO;
import it.kimia.atlas.atlasservices.project.dtos.ProjectUpdateDTO;
import it.kimia.atlas.atlasservices.project.mappers.ProjectMapper;
import it.kimia.atlas.atlasservices.shared.model.CurrentUser;
import it.kimia.atlas.atlasservices.user.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Optional;

/**
 * Service for managing projects.
 */
@Service
@RequiredArgsConstructor
public class ProjectServiceMongoDb implements ProjectService {

    private final ProjectRepository projectRepository;
    private final UserService userService;
    private final ProjectMapper projectMapper;

    /**
     * Check if the current user has access to a project.
     *
     * @param project the project to check
     * @throws AccessDeniedException if the user doesn't have access
     */
    private void checkAccess(Project project) {
        CurrentUser user = userService.getCurrentUser();
        if (!user.hasVisibilityTo(project.getUserId())) {
            throw new AccessDeniedException("User does not have access to this project");
        }
    }

    /**
     * Get all projects visible to the current user.
     *
     * @return a list of all projects visible to the current user
     */
    @Override
    public Page<Project> getAllProjects(Pageable pageable) {
        CurrentUser user = userService.getCurrentUser();
        return projectRepository.findByUserIdIn(new ArrayList<>(user.getVisibilityCone()), pageable);
    }

    /**
     * Get a project by ID if it's visible to the current user.
     *
     * @param id the project ID
     * @return the project, if found and visible to the current user
     * @throws AccessDeniedException if the project is not visible to the current user
     */
    @Override
    public Optional<Project> getProjectById(String id) {
        Optional<Project> projectOpt = projectRepository.findById(id);
        projectOpt.ifPresent(this::checkAccess);
        return projectOpt;
    }


    /**
     * Create a new project for the current user.
     *
     * @param projectDTO the project to create
     * @return the created project
     */
    @Override
    public Project createProject(ProjectCreateDTO projectDTO) {
        final CurrentUser user = userService.getCurrentUser();
        // Usa il mapper per convertire il DTO in un'entità
        final Project project = projectMapper.toEntity(projectDTO);
        project.setUserId(user.getId());
        project.setCreatedAt(LocalDateTime.now());
        project.setLastModified(LocalDateTime.now());
        return projectRepository.save(project);
    }

    /**
     * Update an existing project if it's visible to the current user.
     *
     * @param id               the project ID
     * @param projectUpdateDTO the updated project
     * @return the updated project, if found and visible to the current user
     * @throws AccessDeniedException if the project is not visible to the current user
     */
    @Override
    public Optional<Project> updateProject(String id, ProjectUpdateDTO projectUpdateDTO) {
        return getProjectById(id)
                .map(existingProject -> {
                    // Use the mapper to apply partial updates
                    projectMapper.updateProjectFromDto(projectUpdateDTO, existingProject);

                    // Update the modification timestamp
                    existingProject.setLastModified(LocalDateTime.now());

                    // Save the updated entity
                    return projectRepository.save(existingProject);
                });
    }

    /**
     * Delete a project by ID if it's visible to the current user.
     *
     * @param id the project ID
     * @throws AccessDeniedException if the project is not visible to the current user
     */
    @Override
    public void deleteProject(String id) {
        getProjectById(id).ifPresent(project -> projectRepository.deleteById(id));
    }

}
