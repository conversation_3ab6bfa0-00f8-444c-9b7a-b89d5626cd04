package it.kimia.atlas.atlasservices.calculationmodule;

import it.kimia.atlas.atlasservices.calculationmodule.dto.product.ConcreteCalculationProductInput;
import it.kimia.atlas.atlasservices.calculationmodule.dto.product.CustomProductInputConcrete;
import it.kimia.atlas.atlasservices.calculationmodule.dto.product.ExistingProductInputConcrete;
import it.kimia.atlas.atlasservices.product.ProductRepository;
import org.springframework.stereotype.Service;

@Service
public class ReinforcedConcreteProductResolver {

    private final ProductRepository productRepository; // supponendo esista

    public ReinforcedConcreteProductResolver(ProductRepository productRepository) {
        this.productRepository = productRepository;
    }

    public ReinforcedConcreteProduct resolve(ConcreteCalculationProductInput input) {
        if (input instanceof ExistingProductInputConcrete existing) {
            // Carica dal database e adatta a RectangularBeamProduct
            return productRepository.findById(existing.getId()).map(product -> new ReinforcedConcreteProductImpl(
                            product.getId(),
                            product.getName(),
                            product.getThickness(),
                            product.getElasticModulus(),
                            product.getTensileStrength(),
                            product.getFiberType()
                    ))
                    .orElseThrow(() -> new IllegalArgumentException("Product not found: " + existing.getId()));
        } else if (input instanceof CustomProductInputConcrete custom) {
            return new ReinforcedConcreteProductImpl(
                    custom.getId(),
                    custom.getName(),
                    custom.getThickness(),
                    custom.getElasticModulus(),
                    custom.getTensileStrength(),
                    custom.getFiberType()
            );
        } else {
            throw new IllegalArgumentException("Unknown product input type");
        }
    }
}

