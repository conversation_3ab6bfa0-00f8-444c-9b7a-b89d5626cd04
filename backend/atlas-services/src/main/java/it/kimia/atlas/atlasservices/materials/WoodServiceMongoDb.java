package it.kimia.atlas.atlasservices.materials;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Service implementation for managing wood materials using MongoDB.
 */
@Service
@RequiredArgsConstructor
public class WoodServiceMongoDb implements WoodService {

    private final WoodRepository woodRepository;

    /**
     * Get all wood materials with pagination and optional filtering.
     *
     * @param pageable pagination information
     * @return a page of wood materials matching the criteria
     */
    @Override
    public Page<Wood> getWoods(Pageable pageable) {
        return woodRepository.findAll(pageable);
    }

    /**
     * Get a wood material by ID.
     *
     * @param id the wood material ID
     * @return the wood material, if found
     */
    @Override
    public Optional<Wood> getWoodById(String id) {
        return woodRepository.findById(id);
    }

    /**
     * Get a wood material by name.
     *
     * @param name the name of the wood material (e.g., "C14", "D18", "GL20h")
     * @return the wood material, if found
     */
    @Override
    public Optional<Wood> getWoodByName(String name) {
        // First try exact match, then case-insensitive match
        Optional<Wood> exactMatch = woodRepository.findByName(name);
        if (exactMatch.isPresent()) {
            return exactMatch;
        }
        return woodRepository.findByNameIgnoreCase(name);
    }

}