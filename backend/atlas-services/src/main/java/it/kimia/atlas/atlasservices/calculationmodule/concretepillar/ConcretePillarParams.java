package it.kimia.atlas.atlasservices.calculationmodule.concretepillar;

import it.kimia.atlas.atlasservices.calculationmodule.dto.MaterialProperties;
import jakarta.validation.Valid;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Parameters for T-shaped beam calculation module.
 * <p>
 * This class contains the parameters specific to T-shaped beam calculations.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConcretePillarParams implements Serializable {

    @PositiveOrZero(message = "validation.rectangularbeam.initialDeformation.positiveOrZero")
    private BigDecimal initialDeformation;

    @Valid
    private ConcretePillarGeometry geometry;

    @Valid
    private ConcretePillarRebar reinforcementBar;

    @Valid
    private MaterialProperties materialProperties;
}