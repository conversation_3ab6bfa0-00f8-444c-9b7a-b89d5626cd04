package it.kimia.atlas.atlasservices.calculationmodule;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.ConcretePillarModule;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamModule;
import it.kimia.atlas.atlasservices.calculationmodule.tbeam.TBeamModule;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.PROPERTY,
        property = "type"
)
@JsonSubTypes({
        @JsonSubTypes.Type(value = RectangularBeamModule.class, name = "RECTANGULAR_BEAM"),
        @JsonSubTypes.Type(value = ConcretePillarModule.class, name = "PILLAR"),
        @JsonSubTypes.Type(value = TBeamModule.class, name = "T_BEAM"),
})
public abstract class CalculationModule {

    private String id = UUID.randomUUID().toString();
    private String name;
    private String description;
    private LocalDateTime createdAt;
    private LocalDateTime lastModified;

    @JsonIgnore
    public abstract ModuleType getType();
}