package it.kimia.atlas.atlasservices.product;

import it.kimia.atlas.atlasservices.product.dtos.ProductCreateDTO;
import it.kimia.atlas.atlasservices.product.dtos.ProductUpdateDTO;
import it.kimia.atlas.atlasservices.product.mappers.ProductMapper;
import it.kimia.atlas.atlasservices.project.ProjectType;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Service implementation for managing products using MongoDB.
 */
@Service
@RequiredArgsConstructor
public class ProductServiceMongoDb implements ProductService {

    private final ProductRepository productRepository;
    private final ProductMapper productMapper;
    private final MongoTemplate mongoTemplate;

    /**
     * Get all products with pagination and optional filtering.
     *
     * @param name        optional name filter (partial match, case-insensitive)
     * @param category    optional category filter
     * @param productType optional product type filter
     * @param pageable    pagination information
     * @return a page of products matching the criteria
     */
    @Override
    public Page<Product> getProducts(
            String name,
            ProjectType category,
            String productType,
            Pageable pageable) {

        Query query = new Query();
        if (name != null && !name.isEmpty()) {
            query.addCriteria(Criteria.where("name").regex(name, "i"));
        }

        if (category != null) {
            query.addCriteria(Criteria.where("categories").in(category));
        }

        if (productType != null && !productType.isEmpty()) {
            query.addCriteria(Criteria.where("productType").is(productType));
        }

        // Apply pagination
        query.with(pageable);

        return PageableExecutionUtils.getPage(
                mongoTemplate.find(query.with(pageable), Product.class),
                pageable,
                () -> mongoTemplate.count(query, Product.class)
        );
    }

    /**
     * Get a product by ID.
     *
     * @param id the product ID
     * @return the product, if found
     */
    @Override
    public Optional<Product> getProductById(String id) {
        return productRepository.findById(id);
    }

    /**
     * Create a new product.
     *
     * @param productDTO the product to create
     * @return the created product
     */
    @Override
    public Product createProduct(ProductCreateDTO productDTO) {
        Product product = productMapper.toEntity(productDTO);
        return productRepository.save(product);
    }

    /**
     * Update an existing product.
     *
     * @param id               the product ID
     * @param productUpdateDTO the updated product data
     * @return the updated product, if found
     */
    @Override
    public Optional<Product> updateProduct(String id, ProductUpdateDTO productUpdateDTO) {
        return getProductById(id)
                .map(existingProduct -> {
                    // Use the mapper to apply partial updates
                    productMapper.updateProductFromDto(productUpdateDTO, existingProduct);

                    // Save the updated entity
                    return productRepository.save(existingProduct);
                });
    }

    /**
     * Delete a product by ID.
     *
     * @param id the product ID
     */
    @Override
    public void deleteProduct(String id) {
        productRepository.deleteById(id);
    }
}
