package it.kimia.atlas.atlasservices.product.dtos;

import it.kimia.atlas.atlasservices.project.ProjectType;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * DTO for updating an existing product.
 * Fields are optional to allow for partial updates.
 */
@Data
public class ProductUpdateDTO {
    @Size(max = 100, message = "validation.product.name.size")
    private String name;

    private List<ProjectType> categories;

    // Common properties for fiber-based products
    private String fiberType;
    private String orientation;
    private Double thickness;
    private Double tensileStrength;
    private Double elasticModulus;

    // Documentation link
    private String documentationLink;

    // Product type (mesh, connector, mortar)
    private String productType;

    // Properties for mesh
    private Double ultimateStrain;

    // Properties for connectors
    private Double density;
    private Double maxResistance;
    private Double weight;
    private Double crossSectionArea;
    private Double diameter;
    private Double pullOutResistance;

    // Properties for mortar
    private Double designStrength;
}