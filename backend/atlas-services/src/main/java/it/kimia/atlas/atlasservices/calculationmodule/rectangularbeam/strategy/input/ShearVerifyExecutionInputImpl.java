package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input;

import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProduct;
import it.kimia.atlas.atlasservices.calculationmodule.dto.ReinforcementLayout;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;

@Getter
@AllArgsConstructor
public class ShearVerifyExecutionInputImpl implements ShearVerifyExecutionInput {
    private final ReinforcedConcreteProduct product;

    /**
     * Clear web height h_w "Altezza libera anima"
     */
    private final int webHeight;

    /**
     * The strip width to verify against, in mm.
     */
    private final int stripWidth;

    /**
     * Strip space along element axis, in mm.
     */
    private final int stripSpacingAlongElementAxis;

    /**
     * Strip space orthogonal to element axis, in mm.
     */
    private final int stripSpacingOrthogonalElementAxis;

    /**
     * The number of the layers to apply in calculations.
     */
    private final int layersNumber;

    /**
     * Strip inclination. β (Inclinazione delle fasce)
     */
    private final int stripInclination;

    /**
     * Concrete strut inclination. θ (Inclinazione bielle cls)
     */
    private final int concreteStrutInclination;

    /**
     * The reinforcement layout to be used in the calculation.
     */
    private final ReinforcementLayout reinforcementLayout;

    /**
     * Applied shear force. Vsd [kN] Taglio sollecitante
     */
    private final BigDecimal appliedShearForce;
}

