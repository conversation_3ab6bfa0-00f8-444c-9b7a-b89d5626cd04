package it.kimia.atlas.atlasservices.calculationmodule.strategy;

import it.kimia.atlas.atlasservices.calculationmodule.CalculationModule;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.dto.CalculationRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for executing calculations on modules using strategies.
 * <p>
 * This controller provides endpoints for executing calculations on modules
 * using the Strategy pattern approach.
 */
@RestController
@RequestMapping("/api/v2/projects/{projectId}/modules/{moduleId}/calculations")
@PreAuthorize("isAuthenticated()")
@RequiredArgsConstructor
public class CalculationStrategyController {

    private final CalculationService calculationService;

    /**
     * Execute a calculation on a module.
     *
     * @param projectId the project ID
     * @param moduleId  the module ID
     * @param request   the calculation request
     * @return the updated module
     */
    @PostMapping
    public ResponseEntity<CalculationModule> executeCalculation(
            @PathVariable String projectId,
            @PathVariable String moduleId,
            @Valid @RequestBody CalculationRequest request) {
        
        return calculationService.executeCalculation(projectId, moduleId, request)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
}