package it.kimia.atlas.atlasservices.materials;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

/**
 * Service interface for managing reinforced concrete materials.
 */
public interface ReinforcedConcreteService {

    /**
     * Get all reinforced concretes with pagination and optional filtering.
     *
     * @param pageable pagination information
     * @return a page of reinforced concretes matching the criteria
     */
    Page<ReinforcedConcrete> getReinforcedConcretes(
            Pageable pageable);

    /**
     * Get a reinforced concrete by ID.
     *
     * @param id the reinforced concrete ID
     * @return the reinforced concrete, if found
     */
    Optional<ReinforcedConcrete> getReinforcedConcreteById(String id);

}