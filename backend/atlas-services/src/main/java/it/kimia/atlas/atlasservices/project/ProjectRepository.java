package it.kimia.atlas.atlasservices.project;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository for managing Project entities.
 */
@Repository
public interface ProjectRepository extends MongoRepository<Project, String> {

    /**
     * Find projects by user ID in a list of user IDs.
     *
     * @param userIds the list of user IDs to search for
     * @return a list of projects with a user ID in the given list
     */
    Page<Project> findByUserIdIn(List<String> userIds, Pageable pageable);
}