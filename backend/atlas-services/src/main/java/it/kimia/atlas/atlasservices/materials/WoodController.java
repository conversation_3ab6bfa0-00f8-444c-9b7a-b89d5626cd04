package it.kimia.atlas.atlasservices.materials;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST controller for managing wood materials.
 * Protected by Keycloak authentication.
 */
@RestController
@RequestMapping("/api/v2/materials/woods")
@RequiredArgsConstructor
@Tag(name = "Materials - Wood", description = "APIs for managing wood materials")
public class WoodController {

    private final WoodService woodService;

    /**
     * Get all wood materials with pagination and optional filtering.
     *
     * @param pageable pagination information
     * @return a page of wood materials matching the criteria
     */
    @Operation(summary = "Get all wood materials", description = "Retrieves a paginated list of all wood materials.")
    @ApiResponse(responseCode = "200", description = "Successfully retrieved the list of wood materials")
    @GetMapping
    public ResponseEntity<Page<Wood>> getWoods(
            Pageable pageable) {
        return ResponseEntity.ok(woodService.getWoods(pageable));
    }

    /**
     * Get a wood material by ID.
     *
     * @param id the wood material ID
     * @return the wood material, if found
     */
    @Operation(summary = "Get a wood by ID", description = "Retrieves a single wood material by its unique identifier.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the wood material"),
            @ApiResponse(responseCode = "404", description = "Wood material not found")
    })
    @GetMapping("/{id}")
    public ResponseEntity<Wood> getWoodById(
            @Parameter(description = "ID of the wood material to be retrieved") @PathVariable String id) {
        return woodService.getWoodById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Get a wood material by name.
     *
     * @param name the name of the wood material (e.g., "C14", "D18", "GL20h")
     * @return the wood material, if found
     */
    @Operation(summary = "Get a wood by name", description = "Retrieves a single wood material by its name (e.g., 'C14', 'D18', 'GL20h').")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the wood material"),
            @ApiResponse(responseCode = "404", description = "Wood material not found")
    })
    @GetMapping("/search")
    public ResponseEntity<Wood> getWoodByName(
            @Parameter(description = "Name of the wood material to be retrieved (e.g., 'C14', 'D18', 'GL20h')", example = "C14")
            @RequestParam String name) {
        return woodService.getWoodByName(name)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Get all wood material names.
     *
     * @return list of all wood material names
     */
    @Operation(summary = "Get all wood names", description = "Retrieves a list of all available wood material names (e.g., ['C14', 'D18', 'GL20h']).")
    @ApiResponse(responseCode = "200", description = "Successfully retrieved the list of wood material names")
    @GetMapping("/names")
    public ResponseEntity<List<String>> getAllWoodNames() {
        return ResponseEntity.ok(woodService.getAllWoodNames());
    }

}