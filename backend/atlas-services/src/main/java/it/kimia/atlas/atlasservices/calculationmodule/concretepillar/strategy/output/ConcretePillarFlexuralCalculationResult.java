package it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.output;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder(toBuilder = true)
public class ConcretePillarFlexuralCalculationResult {

    /**
     * Ef -> C40
     */
    private BigDecimal firstExperimentalData;

    /**
     * εf -> C41
     */
    private BigDecimal secondExperimentalData;

    /**
     * ηa -> C43
     */
    private BigDecimal environmentalConversionFactor;

    /**
     * γRd -> C44
     */
    private BigDecimal partialBendingFactor;

    /**
     * γf -> C45
     */
    private BigDecimal materialPartialFactor;

    /**
     * kb -> C47
     */
    private BigDecimal geometricCorrectionCoefficient;

    /**
     * Led -> C52
     */
    private BigDecimal optimalDesignAnchorageLength;

    /**
     * ffdd,2 -> C59
     */
    private BigDecimal maxReinforcementStress;

    /**
     * εfdd,2 -> C60
     */
    private BigDecimal maxReinforcementStrain;

    /**
     * εfd -> C64
     */
    private BigDecimal maxDesignReinforcementStrain;

    /**
     * x -> C70
     */
    private BigDecimal neutralAxisPosition;

    /**
     * x -> C71
     */
    private BigDecimal equilibrium;

    /**
     * ψ -> C72
     */
    private BigDecimal firstAdimensionalCoefficient;

    /**
     * λ -> C73
     */
    private BigDecimal secondAdimensionalCoefficient;

    /**
     * εc -> C76
     */
    private BigDecimal concreteStrain;

    /**
     * εs1 -> C77
     */
    private BigDecimal tensileSteelStrain;

    /**
     * εs2 -> C78
     */
    private BigDecimal compressedSteelStrain;

    /**
     * εf -> C79
     */
    private BigDecimal frpReinforcementStrain;
    /**
     * Mrd -> C84 moment capacity
     */
    private BigDecimal resistantMoment;
    /**
     * Mrd / Msd -> resistantMoment / bendingMoment
     */
    private BigDecimal checkValue;

    /**
     * resistantMoment / bendingMoment > 1 -> resistantMoment > bendingMoment
     */
    private boolean checkResult;
}
