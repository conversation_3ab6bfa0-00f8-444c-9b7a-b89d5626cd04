package it.kimia.atlas.atlasservices.user;

import it.kimia.atlas.atlasservices.security.IdpUserConverter;
import it.kimia.atlas.atlasservices.security.JwtToIdpUserConverter;
import it.kimia.atlas.atlasservices.shared.model.CurrentUser;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class JwtUserService implements UserService {

    private final JwtToIdpUserConverter jwtToIdpUserConverter;
    private final IdpUserConverter idpUserConverter;

    /**
     * Get the current authenticated user.
     *
     * @return the current user
     * @throws AccessDeniedException if no user is authenticated
     */
    @Override
    public CurrentUser getCurrentUser() throws AccessDeniedException {
        var idpUser = jwtToIdpUserConverter.extractIdpUser();
        if (idpUser == null) {
            throw new AccessDeniedException("No authenticated user found");
        }
        return idpUserConverter.convertToCurrentUser(idpUser);
    }
}
