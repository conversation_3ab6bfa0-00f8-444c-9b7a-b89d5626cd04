package it.kimia.atlas.atlasservices.calculationmodule.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;

public record TransverseReinforcement(
        @Positive(message = "validation.transverse.diameter.positive")
        int diameter,

        @Positive(message = "validation.transverse.legs.positive")
        int legs,

        @Positive(message = "validation.transverse.stirrupSpacing.positive")
        int stirrupSpacing,

        @Min(message = "validation.transverse.stirrupInclination.min", value = -360L)
        @Max(message = "validation.transverse.stirrupInclination.max", value = +360L)
        int stirrupInclination,

        @Positive(message = "validation.transverse.cornerRadius.positive")
        int cornerRadius,

        @Positive(message = "validation.transverse.area.positive")
        BigDecimal area
) {
}
