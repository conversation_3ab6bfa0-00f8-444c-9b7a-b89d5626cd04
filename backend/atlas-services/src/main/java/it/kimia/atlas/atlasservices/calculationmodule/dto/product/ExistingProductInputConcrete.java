package it.kimia.atlas.atlasservices.calculationmodule.dto.product;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Represents a product to be used in a calculation, fetched from the database via its ID.
 * Use sourceType: "DATABASE" in the JSON payload.
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class ExistingProductInputConcrete extends ConcreteCalculationProductInput {

    /**
     * The ID of the product stored in the database.
     */
    @NotBlank(message = "validation.product.id.notblank")
    private String id;
}