package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.mapper;

import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProductResolver;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.RectangularBeamShearVerifyInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.ShearVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.ShearVerifyExecutionInputImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ShearVerifyExecutionInputMapper {

    private final ReinforcedConcreteProductResolver reinforcedConcreteProductResolver;

    public ShearVerifyExecutionInput map(RectangularBeamShearVerifyInput input) {
        return new ShearVerifyExecutionInputImpl(
                reinforcedConcreteProductResolver.resolve(input.getProduct()),
                input.getWebHeight(),
                input.getStripWidth(),
                input.getStripSpacingAlongElementAxis(),
                input.getStripSpacingOrthogonalElementAxis(),
                input.getLayersNumber(),
                input.getStripInclination(),
                input.getConcreteStrutInclination(),
                input.getReinforcementLayout(),
                input.getAppliedShearForce()
        );
    }
}

