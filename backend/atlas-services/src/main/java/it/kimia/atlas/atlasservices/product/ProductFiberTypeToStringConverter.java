package it.kimia.atlas.atlasservices.product;

import it.kimia.atlas.atlasservices.project.ProjectType;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.WritingConverter;
import org.springframework.stereotype.Component;

@Component
@WritingConverter
public class ProductFiberTypeToStringConverter implements Converter<FiberType, String> {

    @Override
    public String convert(FiberType source) {
        return source == null ? null : source.getValue();
    }
}