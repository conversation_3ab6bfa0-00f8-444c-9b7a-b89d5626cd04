// infra/BrentRootFinderAdapter.java
package it.kimia.atlas.atlasservices.calculationmodule.utility;

import java.math.BigDecimal;
import java.util.function.Function;

public class BrentRootFinderAdapter implements RootFinder {

    private final BrentRootFinder.Options opt;

    public BrentRootFinderAdapter(
            double absTol,
            double relTol,
            double fTol,
            int maxEval
    ) {
        this.opt = new BrentRootFinder.Options(absTol, relTol, fTol, maxEval);
    }

    @Override
    public <T> T solve(BigDecimal lower, BigDecimal upper,
                       Function<BigDecimal, T> eval,
                       Function<T, BigDecimal> residual) {
        return BrentRootFinder.solve(lower, upper, opt, eval, residual);
    }
}