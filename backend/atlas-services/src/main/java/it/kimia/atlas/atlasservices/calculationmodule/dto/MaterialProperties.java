package it.kimia.atlas.atlasservices.calculationmodule.dto;

import it.kimia.atlas.atlasservices.calculationmodule.KnowledgeLevel;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MaterialProperties {
    @NotNull(message = "validation.materialProperties.knowledgeLevel.notnull")
    private KnowledgeLevel knowledgeLevel;

    @NotNull(message = "validation.materialProperties.concreteClass.notnull")
    @Valid
    private MaterialPropertiesReinforcedConcreteClass concreteClass;

    @NotNull(message = "validation.materialProperties.steelGrade.notnull")
    @Valid
    private MaterialPropertiesSteelGrade steelGrade;
}
