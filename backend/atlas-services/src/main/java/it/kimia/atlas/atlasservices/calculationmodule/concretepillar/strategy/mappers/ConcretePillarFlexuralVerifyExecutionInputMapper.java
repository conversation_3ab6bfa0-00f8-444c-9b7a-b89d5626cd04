package it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.mappers;

import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProductResolver;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input.ConcretePillarFlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input.ConcretePillarFlexuralVerifyExecutionInputImpl;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input.ConcretePillarFlexuralVerifyInput;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ConcretePillarFlexuralVerifyExecutionInputMapper {

    private final ReinforcedConcreteProductResolver reinforcedConcreteProductResolver;

    public ConcretePillarFlexuralVerifyExecutionInput map(ConcretePillarFlexuralVerifyInput input) {
        return new ConcretePillarFlexuralVerifyExecutionInputImpl(
                input.getAppliedAxialForce(),
                input.getStripWidth(),
                input.getLayersNumber(),
                input.getBendingMoment(),
                reinforcedConcreteProductResolver.resolve(input.getProduct())
        );
    }
}

