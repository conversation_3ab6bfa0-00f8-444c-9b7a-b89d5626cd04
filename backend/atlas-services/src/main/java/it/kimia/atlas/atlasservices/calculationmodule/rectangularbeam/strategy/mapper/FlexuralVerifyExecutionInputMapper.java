package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.mapper;

import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProductResolver;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInputImpl;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.RectangularBeamFlexuralVerifyInput;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class FlexuralVerifyExecutionInputMapper {

    private final ReinforcedConcreteProductResolver reinforcedConcreteProductResolver;

    public FlexuralVerifyExecutionInput map(RectangularBeamFlexuralVerifyInput input) {
        return new FlexuralVerifyExecutionInputImpl(
                input.getStripWidth(),
                input.getLayersNumber(),
                input.getBendingMoment(),
                reinforcedConcreteProductResolver.resolve(input.getProduct())
        );
    }
}

