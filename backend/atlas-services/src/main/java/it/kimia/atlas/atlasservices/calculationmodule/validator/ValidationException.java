package it.kimia.atlas.atlasservices.calculationmodule.validator;

import it.kimia.atlas.atlasservices.shared.model.FieldErrorDTO;
import lombok.Getter;

import java.util.List;

/**
 * Exception thrown when validation fails with structured field errors.
 * This allows validation errors from custom validators to be handled
 * in the same way as Spring validation errors.
 */
@Getter
public class ValidationException extends RuntimeException {
    private final List<FieldErrorDTO> fieldErrors;

    /**
     * Creates a new ValidationException with a message and field errors.
     *
     * @param message the error message
     * @param fieldErrors the list of field errors
     */
    public ValidationException(String message, List<FieldErrorDTO> fieldErrors) {
        super(message);
        this.fieldErrors = fieldErrors;
    }
}