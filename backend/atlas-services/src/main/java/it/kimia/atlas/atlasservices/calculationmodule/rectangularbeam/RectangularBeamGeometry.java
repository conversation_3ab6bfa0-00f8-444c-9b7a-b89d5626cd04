package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam;

import it.kimia.atlas.atlasservices.calculationmodule.Exposure;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RectangularBeamGeometry {
    @NotNull(message = "validation.rectangularbeam.geometry.width.notnull")
    private BigDecimal width;

    @NotNull(message = "validation.rectangularbeam.geometry.height.notnull")
    private BigDecimal height;

    @NotNull(message = "validation.rectangularbeam.geometry.topConcreteCover.notnull")
    private BigDecimal topConcreteCover;

    @NotNull(message = "validation.rectangularbeam.geometry.bottomConcreteCover.notnull")
    private BigDecimal bottomConcreteCover;

    @NotNull(message = "validation.rectangularbeam.geometry.effectiveDepth.notnull")
    private BigDecimal effectiveDepth;

    @NotNull(message = "validation.rectangularbeam.geometry.exposure.notnull")
    private Exposure exposure;
}
