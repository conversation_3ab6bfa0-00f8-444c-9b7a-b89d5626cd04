package it.kimia.atlas.atlasservices.security;

import it.kimia.atlas.atlasservices.config.KeycloakConfig;
import it.kimia.atlas.atlasservices.security.model.Group;
import it.kimia.atlas.atlasservices.security.model.IdpUser;
import it.kimia.atlas.atlasservices.security.model.Role;
import it.kimia.atlas.atlasservices.security.model.User;
import lombok.RequiredArgsConstructor;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.GroupsResource;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.representations.idm.GroupRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class JwtToIdpUserConverter {

    private final KeycloakConfig keycloakConfig;

    /**
     * Extracts an IdpUser from the current authentication context.
     *
     * @return The IdpUser or null if no authentication is present
     */
    public IdpUser extractIdpUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication instanceof JwtAuthenticationToken auth) {
            return convertJwtToIdpUser(auth.getToken());
        }
        return null;
    }

    /**
     * Converts a JWT token to an IdpUser.
     *
     * @param jwt The JWT token
     * @return The IdpUser
     */
    private IdpUser convertJwtToIdpUser(Jwt jwt) {
        // Extract user representation
        User userRepresentation = User.builder()
                .id(jwt.getSubject())
                .username(jwt.getClaimAsString("preferred_username"))
                .firstName(jwt.getClaimAsString("given_name"))
                .lastName(jwt.getClaimAsString("family_name"))
                .email(jwt.getClaimAsString("email"))
                .emailVerified(jwt.getClaimAsBoolean("email_verified"))
                .enabled(true)
                .build();

        // Extract roles
        List<Role> roles = new ArrayList<>();
        List<String> realmRoles = extractRealmRoles(jwt);
        for (String role : realmRoles) {
            try {
                roles.add(Role.valueOf(role.toUpperCase()));
            } catch (IllegalArgumentException e) {
                // Ignore roles that don't match our enum
            }
        }
        List<Group> subGroups = extractSubGroups(jwt);

        return IdpUser.builder()
                .user(userRepresentation)
                .roles(roles)
                .groups(subGroups)
                .build();
    }

    /**
     * Extracts realm roles from the JWT token.
     *
     * @param jwt The JWT token
     * @return A list of realm roles
     */
    @SuppressWarnings("unchecked")
    private List<String> extractRealmRoles(Jwt jwt) {
        Map<String, Object> realmAccess = jwt.getClaimAsMap("realm_access");
        if (realmAccess != null) {
            List<String> roles = (List<String>) realmAccess.get("roles");
            if (roles != null) {
                return roles;
            }
        }
        return Collections.emptyList();
    }

    /**
     * Extracts subgroups from the JWT token by making a call to Keycloak.
     *
     * @param jwt The JWT token
     * @return A list of subgroups
     */
    private List<Group> extractSubGroups(Jwt jwt) {
        try {
            String userId = jwt.getSubject();
            String issuer = jwt.getClaimAsString("iss");
            String realm = issuer.substring(issuer.lastIndexOf("/") + 1);

            Keycloak keycloak = keycloakConfig.getKeycloak();
            if (keycloak == null) {
                return new ArrayList<>();
            }

            // Get user by ID
            UserResource userResource = keycloak.realm(realm).users().get(userId);
            if (userResource == null) {
                return new ArrayList<>();
            }

            // Get user's groups
            GroupsResource keycloakGroupsByRealm = keycloak.realm(realm).groups();
            List<GroupRepresentation> userGroupList = userResource.groups().stream().toList();

            // Get subgroups and their members
            Map<String, List<UserRepresentation>> subGroupsMap =
                    getUserGroupsFromRealmBySubGroup(userGroupList, keycloakGroupsByRealm);

            // Convert to SubGroupsRepresentation
            return subGroupsMap.entrySet().stream()
                    .map(entry -> Group.builder()
                            .name(entry.getKey())
                            .users(convertKeycloakUsers(entry.getValue()))
                            .build())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            // Log the error
            System.err.println("Error extracting subgroups: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Gets user groups from realm by subgroup.
     *
     * @param userGroupList         The list of user groups
     * @param keycloakGroupsByRealm The groups resource
     * @return A map of subgroup name to list of users
     */
    private Map<String, List<org.keycloak.representations.idm.UserRepresentation>> getUserGroupsFromRealmBySubGroup(
            List<GroupRepresentation> userGroupList, GroupsResource keycloakGroupsByRealm) {
        return userGroupList.stream()
                .flatMap(group -> {
                    String groupId = group.getId();
                    Integer subGroupCount = keycloakGroupsByRealm
                            .group(groupId)
                            .toRepresentation()
                            .getSubGroupCount()
                            .intValue();
                    return keycloakGroupsByRealm
                            .group(groupId)
                            .getSubGroups(0, subGroupCount, false)
                            .stream();
                })
                .collect(Collectors.toMap(
                        GroupRepresentation::getName,
                        subGroup -> keycloakGroupsByRealm
                                .group(subGroup.getId())
                                .members()
                ));
    }

    /**
     * Converts Keycloak UserRepresentation to atlas-services UserRepresentation.
     *
     * @param keycloakUsers The list of Keycloak users
     * @return The list of atlas-services users
     */
    private List<User> convertKeycloakUsers(List<org.keycloak.representations.idm.UserRepresentation> keycloakUsers) {
        return keycloakUsers.stream()
                .map(keycloakUser -> User.builder()
                        .id(keycloakUser.getId())
                        .username(keycloakUser.getUsername())
                        .firstName(keycloakUser.getFirstName())
                        .lastName(keycloakUser.getLastName())
                        .email(keycloakUser.getEmail())
                        .emailVerified(keycloakUser.isEmailVerified())
                        .createdTimestamp(keycloakUser.getCreatedTimestamp())
                        .enabled(keycloakUser.isEnabled())
                        .build())
                .collect(Collectors.toList());
    }
}
