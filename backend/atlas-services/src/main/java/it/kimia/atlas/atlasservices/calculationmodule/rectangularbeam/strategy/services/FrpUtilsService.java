package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProduct;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.ShearVerifyExecutionInput;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;


@Service
public class FrpUtilsService {

    // Area FRP = tf * bf * nf  (C34 * C35 * C36)
    public BigDecimal computeFrpArea(final FlexuralVerifyExecutionInput input) {
        final ReinforcedConcreteProduct product = input.getProduct();
        return (product.getThickness()
                .multiply(BigDecimal.valueOf(input.getStripWidth()))
                .multiply(BigDecimal.valueOf(input.getLayersNumber()))).setScale(SCALE, RoundingMode.HALF_UP);
    }

    // Shear C46 = 2*C44*C39*C41
    public BigDecimal computeFrpAreaForShearResistance(final ShearVerifyExecutionInput input) {
        final ReinforcedConcreteProduct product = input.getProduct();
        // Shear C39
        final BigDecimal thickness = product.getThickness();
        // Shear C41
        final BigDecimal stripWidth = BigDecimal.valueOf(input.getStripWidth());
        // Shear C44
        final BigDecimal layersNumber = BigDecimal.valueOf(input.getLayersNumber());

        return thickness.multiply(stripWidth).multiply(layersNumber).multiply(BigDecimal.valueOf(2)).setScale(SCALE, RoundingMode.HALF_UP);
    }
}