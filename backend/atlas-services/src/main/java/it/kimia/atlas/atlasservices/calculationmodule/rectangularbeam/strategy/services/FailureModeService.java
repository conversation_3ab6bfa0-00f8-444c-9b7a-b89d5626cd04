// File: FailureModeService.java
package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamParams;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInput;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
@RequiredArgsConstructor
public class FailureModeService {

    private final ReinforcementRatioService reinforcementRatioService;

    public int getFailureMode(final RectangularBeamParams params,
                              final FlexuralVerifyExecutionInput input,
                              final BigDecimal maxStress) {
        // C67 μf [-]	Mechanical reinforcement ratio [eq. 13.8 CNR DT R1]
        final BigDecimal mechanicalReinforcementRatio = reinforcementRatioService.calculateMechanicalReinforcementRatio(
                params,
                input,
                maxStress
        );

        // C66 μf1-2 [-] Limit mechanical reinforcement ratio [eq. 13.9 CNR DT R1]
        final BigDecimal limitMechanicalReinforcementRatio = reinforcementRatioService.calculateLimitMechanicalReinforcementRatio(params);

        // IF(μf <= μf1-2, 1, 2)
        return mechanicalReinforcementRatio.compareTo(limitMechanicalReinforcementRatio) <= 0
                ? 1 // Ductile
                : 2; // Brittle
    }
}