package it.kimia.atlas.atlasservices.calculationmodule.strategy;

import it.kimia.atlas.atlasservices.calculationmodule.CalculationModule;
import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.factory.CalculationStrategyFactory;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.List;
import java.util.Optional;

/**
 * Registry for calculation strategy factories.
 * <p>
 * This class holds a reference to all available {@link CalculationStrategyFactory} instances,
 * indexed by the {@link ModuleType} they handle.
 */
@Component
public class StrategyFactoryRegistry {

    private final EnumMap<ModuleType, CalculationStrategyFactory<?>> factories = new EnumMap<>(ModuleType.class);

    /**
     * @param factoryList The list of all available strategy factories, injected by Spring.
     */
    public StrategyFactoryRegistry(List<CalculationStrategyFactory<?>> factoryList) {
        factoryList.forEach(factory -> factories.put(factory.getModuleType(), factory));
    }

    /**
     * Finds the appropriate factory for the given module.
     *
     * @param module the module for which to find a factory.
     * @return an Optional containing the factory if found, or empty otherwise.
     */
    public Optional<CalculationStrategyFactory<?>> findFactory(CalculationModule module) {
        return Optional.ofNullable(factories.get(module.getType()));
    }
}