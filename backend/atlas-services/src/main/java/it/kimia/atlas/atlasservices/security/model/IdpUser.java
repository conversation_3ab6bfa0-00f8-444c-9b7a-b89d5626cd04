package it.kimia.atlas.atlasservices.security.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IdpUser {
    private User user;
    private List<Role> roles;
    private List<Group> groups;

    /**
     * Gets all user IDs in the visibility cone of this user.
     * This includes the user's own ID and the IDs of all users in the user's subgroups.
     *
     * @return A set of user IDs in the visibility cone
     */
    public Set<String> getVisibilityCone() {
        Set<String> userIds = groups.stream()
                .flatMap(subGroup -> subGroup.getUsers().stream())
                .map(User::getId)
                .collect(Collectors.toSet());
        
        // Add the current user's ID
        userIds.add(user.getId());
        
        return userIds;
    }
    
    /**
     * Checks if the user has visibility to a project with the given user ID.
     *
     * @param projectUserId The user ID of the project owner
     * @return true if the user has visibility, false otherwise
     */
    public boolean hasVisibilityTo(String projectUserId) {
        // User has visibility if they are the owner or if the owner is in their subgroups
        if (projectUserId.equals(user.getId())) {
            return true;
        }
        
        return groups.stream()
                .flatMap(subGroup -> subGroup.getUsers().stream())
                .anyMatch(user -> projectUserId.equals(user.getId()));
    }
}