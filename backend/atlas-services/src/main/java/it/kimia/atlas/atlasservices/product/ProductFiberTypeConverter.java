package it.kimia.atlas.atlasservices.product;

import it.kimia.atlas.atlasservices.project.ProjectType;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.ReadingConverter;
import org.springframework.stereotype.Component;

/**
 * Custom converter for WoodCategory enum.
 * Handles unknown category values from MongoDB by returning WoodCategory.UNKNOWN
 * instead of throwing an exception.
 */
@Component
@ReadingConverter
public class ProductFiberTypeConverter implements Converter<String, FiberType> {

    @Override
    public FiberType convert(String source) {
        if (source == null) {
            return null;
        }

        try {
            return FiberType.fromValue(source);
        } catch (IllegalArgumentException e) {
            // If the source string doesn't match any enum value,
            // return UNKNOWN instead of throwing an exception
            return FiberType.UNKNOWN;
        }
    }
}