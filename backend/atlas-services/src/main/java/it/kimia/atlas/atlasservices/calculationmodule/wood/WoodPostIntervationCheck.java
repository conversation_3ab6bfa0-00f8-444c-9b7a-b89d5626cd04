package it.kimia.atlas.atlasservices.calculationmodule.wood;

import jakarta.validation.Valid;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WoodPostIntervationCheck {

    @PositiveOrZero(message = "validation.rectangularbeam.initialDeformation.positiveOrZero")
    private BigDecimal initialDeformation;

    @Valid
    private WoodProperties materialProperties;

    @Valid
    private WoodGeometry geometry;

    @Valid
    private WoodCompositeGeometry compositeGeometry;

    @Valid
    private WoodCompositeProperties compositeProperties;

    @Valid
    private WoodResultOfPostIntervationCheck resultOfPostIntervationCheck;

}
