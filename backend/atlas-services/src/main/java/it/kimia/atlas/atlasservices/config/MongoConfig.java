package it.kimia.atlas.atlasservices.config;

import it.kimia.atlas.atlasservices.materials.WoodCategoryConverter;
import it.kimia.atlas.atlasservices.product.ProductFiberTypeConverter;
import it.kimia.atlas.atlasservices.product.ProductFiberTypeToStringConverter;
import it.kimia.atlas.atlasservices.project.ProjectTypeConverter;
import it.kimia.atlas.atlasservices.project.ProjectTypeToStringConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;

import java.util.Arrays;

/**
 * MongoDB configuration class.
 * Registers custom converters for MongoDB.
 */
@Configuration
public class MongoConfig {

    /**
     * Configures custom conversions for MongoDB.
     * Registers the WoodCategoryConverter to handle unknown wood category values.
     * Registers the ProjectTypeConverter to handle unknown wood category values.
     *
     * @param woodCategoryConverter the custom converter for WoodCategory
     * @param projectTypeConverter  the custom converter for ProjectType
     * @return the custom conversions configuration
     */
    @Bean
    public MongoCustomConversions customConverter(
            WoodCategoryConverter woodCategoryConverter,
            ProjectTypeConverter projectTypeConverter,
            ProjectTypeToStringConverter projectTypeToStringConverter,
            ProductFiberTypeConverter productFiberTypeConverter,
            ProductFiberTypeToStringConverter productFiberTypeToStringConverter
    ) {
        return new MongoCustomConversions(Arrays.asList(
                woodCategoryConverter,
                projectTypeConverter,
                projectTypeToStringConverter,
                productFiberTypeConverter,
                productFiberTypeToStringConverter
        ));
    }
}