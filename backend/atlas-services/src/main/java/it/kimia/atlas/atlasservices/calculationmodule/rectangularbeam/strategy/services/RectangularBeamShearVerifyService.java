package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.dto.ReinforcementLayout;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamParams;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.ShearVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.output.ShearCalculationResult;
import it.kimia.atlas.atlasservices.product.FiberType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;

@Service
@RequiredArgsConstructor
@Slf4j
public class RectangularBeamShearVerifyService implements RectangularBeamShearVerify {

    // C25 ε_c0: Concrete strain at peak stress (parabolic part)
    public static final BigDecimal CONCRETE_PEAK_STRAIN = BigDecimal.valueOf(0.00175);
    // C26 ε_cu: Ultimate compressive strain in concrete
    public static final BigDecimal CONCRETE_ULTIMATE_STRAIN = BigDecimal.valueOf(0.00350);

    private final ConcreteCoverService concreteCoverService;
    private final EnvironmentalAndSafetyFactorService environmentalAndSafetyFactorService;
    private final FrpUtilsService frpUtilsService;
    private final MaterialStrengthService materialStrengthService;


    public ShearCalculationResult execute(final RectangularBeamParams params,
                                          final ShearVerifyExecutionInput shearVerifyExecutionInput) {

        // C2 ε 0 [-]	Deformazione iniziale
        final BigDecimal initialDeformation = params.getInitialDeformation().setScale(SCALE, RoundingMode.HALF_UP);

        /*
         * MATERIALI ORIGINARI – GEOMETRIA
         */

        // C6 b [mm]	Larghezza sezione
        final BigDecimal width = params.getGeometry().getWidth().setScale(SCALE, RoundingMode.HALF_UP);
        // C7 h [mm]	Altezza sezione
        final BigDecimal height = params.getGeometry().getHeight().setScale(SCALE, RoundingMode.HALF_UP);
        // C8 hw [mm]	Altezza libera anima
        final BigDecimal freeHeight = BigDecimal.valueOf(shearVerifyExecutionInput.getWebHeight()).setScale(SCALE, RoundingMode.HALF_UP);
        // C9 c1 [mm]	M+ -> Copriferro inferiore | M- -> Copriferro Superiore
        final BigDecimal compressedCover = concreteCoverService.getCompressedCover(params).setScale(SCALE, RoundingMode.HALF_UP);
        // C10 c2 [mm]	M+ -> Copriferro superiore | M- -> Copriferro inferiore
        final BigDecimal tensionCover = concreteCoverService.getTensionCover(params).setScale(SCALE, RoundingMode.HALF_UP);
        // C11 d [mm]	Altezza utile
        final BigDecimal effectiveDepth = params.getGeometry().getEffectiveDepth().setScale(SCALE, RoundingMode.HALF_UP);
        // C12 A s1 [mmq] Armatura inferiore tesa M+: As1 (inferiore) è teso; M-: As1 (superiore) è teso
        final BigDecimal tensileSteelArea = concreteCoverService.getTensileSteelArea(params).setScale(SCALE, RoundingMode.HALF_UP);
        // C13 A s2 [mmq] Armatura superiore compressa
        final BigDecimal compressiveSteelArea = concreteCoverService.getCompressiveSteelArea(params).setScale(SCALE, RoundingMode.HALF_UP);
        // C14 A sw [mmq]	Area staffe
        final BigDecimal transverseArea = concreteCoverService.getTransverseArea(params).setScale(SCALE, RoundingMode.HALF_UP);
        // C15 s [mm]	Passo staffe
        final BigDecimal stirrupSpacing = BigDecimal.valueOf(params.getReinforcementBar().getTransverse().stirrupSpacing()).setScale(SCALE, RoundingMode.HALF_UP);
        // C16 α [°]	Inclinazione staffe
        final BigDecimal stirrupInclination = BigDecimal.valueOf(params.getReinforcementBar().getTransverse().stirrupInclination()).setScale(SCALE, RoundingMode.HALF_UP);
        // C17 rc [mm]	Raggio curvatura spigolo
        final BigDecimal cornerRadius = BigDecimal.valueOf(params.getReinforcementBar().getTransverse().cornerRadius()).setScale(SCALE, RoundingMode.HALF_UP);

        /*
         * MATERIALI ORIGINARI – Proprietà
         */
        // C21 Rck [Mpa] Resistenza a compressione cubica caratt.
        final BigDecimal cubeCompressiveStrength = params.getMaterialProperties().getConcreteClass().getCubeCompressiveStrength().setScale(SCALE, RoundingMode.HALF_UP);
        // C22 fcd,f = fcm / (FC * γc,f ) [Mpa]	Resistenza a compressione di progetto - meccanismi fragili
        final BigDecimal designCompressiveStrengthForBrittleMechanisms = getDesignCompressiveStrengthForBrittleMechanisms(params);
        // C23 f cm [Mpa]	Resistenza a compressione media
        final BigDecimal averageCompressiveStrength = params.getMaterialProperties().getConcreteClass().getAverageCompressiveStrength().setScale(SCALE, RoundingMode.HALF_UP);
        // C24 f ctm [Mpa]	Resistenza a trazione media
        final BigDecimal averageTensileStrength = params.getMaterialProperties().getConcreteClass().getAverageTensileStrength().setScale(SCALE, RoundingMode.HALF_UP);
        // C25 ε c0 [-]	Dato di progetto
        final BigDecimal peakConcreteStrain = CONCRETE_PEAK_STRAIN;
        // C26 ε cu [-]	Dato di progetto
        final BigDecimal ultimateConcreteStrain = CONCRETE_ULTIMATE_STRAIN;
        // C27 Ec [Mpa]	Modulo elastico
        final BigDecimal elasticModulus = params.getMaterialProperties().getConcreteClass().getElasticModulus().setScale(SCALE, RoundingMode.HALF_UP);
        // C28 fyk [Mpa] Resistenza a snervamento caratt.
        final BigDecimal yieldStrength = params.getMaterialProperties().getSteelGrade().getYieldStrength().setScale(SCALE, RoundingMode.HALF_UP);
        // C29 fyd,f [Mpa] Resistenza a snervamento di progetto - meccanismi fragili
        final BigDecimal designYieldStrengthForBrittleMechanisms = getDesignYieldStrengthForBrittleMechanisms(params);
        // C31 Es [Mpa]	Dato di progetto
        final BigDecimal elasticModulusOfSteel = BigDecimal.valueOf(params.getMaterialProperties().getSteelGrade().getElasticModulus()).setScale(SCALE, RoundingMode.HALF_UP);
        // C30 ε yd [-]	Dato di progetto =C29/C31
        final BigDecimal yieldStrainInSteel = designYieldStrengthForBrittleMechanisms.divide(elasticModulusOfSteel, SCALE, RoundingMode.HALF_UP);
        // C32 FC	Fattore di confidenza
        final BigDecimal confidenceFactor = params.getMaterialProperties().getKnowledgeLevel().getValue();

        /**
         * COMPOSITO – GEOMETRIA
         */
        // C39 tf [mm]	Spessore fascia
        final BigDecimal equivalentThcikness = shearVerifyExecutionInput.getProduct().getThickness();
        // C40 Disposizione rinforzo a taglio	1=ad U; 2=in avvolgimento
        final ReinforcementLayout reinforcementLayout = shearVerifyExecutionInput.getReinforcementLayout();
        // C41 bf [mm]	Larghezza fascia
        final BigDecimal stripWidth = BigDecimal.valueOf(shearVerifyExecutionInput.getStripWidth()).setScale(SCALE, RoundingMode.HALF_UP);
        // C42 pf* [mm]	Passo delle fasce (misurato lungo l'asse dell'elemento)
        final BigDecimal stripSpacingAlongElementAxis = BigDecimal.valueOf(shearVerifyExecutionInput.getStripSpacingAlongElementAxis()).setScale(SCALE, RoundingMode.HALF_UP);
        // C43 pf [mm]	Passo delle fasce (misurato ortogonalmente alle fibre)
        final BigDecimal stripSpacingOrthogonalElementAxis = BigDecimal.valueOf(shearVerifyExecutionInput.getStripSpacingOrthogonalElementAxis()).setScale(SCALE, RoundingMode.HALF_UP);
        // C44 nf [-]	Numero di strati
        final BigDecimal layersNumber = BigDecimal.valueOf(shearVerifyExecutionInput.getLayersNumber()).setScale(SCALE, RoundingMode.HALF_UP);
        // C45 β [°]	Inclinazione delle fasce
        final BigDecimal stripInclination = BigDecimal.valueOf(shearVerifyExecutionInput.getStripInclination()).setScale(SCALE, RoundingMode.HALF_UP);
        // C46 Afv [mm2]	Area della staffa in FRP resistente a taglio [form. 4.81]
        final BigDecimal frpArea = frpUtilsService.computeFrpAreaForShearResistance(shearVerifyExecutionInput);

        /*
         * COMPOSITO – PROPRIETA'
         */

        // C50 Ef [Mpa]	Dato sperimentale -> modulo elastico prodotto
        final BigDecimal firstExperimentalData = shearVerifyExecutionInput.getProduct().getElasticModulus().setScale(SCALE, RoundingMode.HALF_UP);

        // C51 εf [-]	Dato sperimentale -> deformazione del sistema
        final BigDecimal secondExperimentalData = shearVerifyExecutionInput.getProduct().getSystemDeformation().setScale(SCALE, RoundingMode.HALF_UP);

        // C53 ηa	Fatt. conversione ambientale [Tab. 3.3]
        final BigDecimal ambientFactor = environmentalAndSafetyFactorService.getAmbientFactor(params.getGeometry().getExposure(), shearVerifyExecutionInput.getProduct())
                .setScale(SCALE, RoundingMode.HALF_UP);

        // C54 γRd	Fatt. parziale a taglio [Tab. 3.2]
        final BigDecimal shearPartialFactor = BigDecimal.valueOf(1.2).setScale(SCALE, RoundingMode.HALF_UP);

        // C55 γf	Fatt. parziale materiale [Tab. 3.1]
        final BigDecimal materialPartialFactor = environmentalAndSafetyFactorService.calculateMaterialPartialFactor(shearVerifyExecutionInput.getProduct())
                .setScale(SCALE, RoundingMode.HALF_UP);

        // C56 εfd [-]	Deformazione di progetto del rinforzo [Par. ******* (3)]
        // =C53*C51/C55
        final BigDecimal designReinforcementStrain = ambientFactor.multiply(secondExperimentalData).divide(materialPartialFactor, SCALE, RoundingMode.HALF_UP);

        // C57 ffd [MPa]	Tensione di progetto FRP [par. ******* (3)]
        // =C56*C50
        final BigDecimal projectTensionFRP = designReinforcementStrain.multiply(firstExperimentalData).setScale(SCALE, RoundingMode.HALF_UP);

        // C58 b Larghezza sezione da usare per il calcolo di kb [Par. ******* (4)]
        // =IF(C41=C43;C41;C43)
        final BigDecimal b = stripWidth.equals(stripSpacingOrthogonalElementAxis) ? stripWidth : stripSpacingOrthogonalElementAxis;

        // C59 bf/b	Rapporto largh. rinforzo/largh. sezione
        // =IF(C41/C58<0,25;0,25;C41/C58)
        final BigDecimal reinforcementWidthRatio = this.getReinforcementWidthRatio(stripWidth, b);

        // C60 kb Coeff. correttivo geometrico [Par. 4.1.3]
        final BigDecimal geometricCorrectionCoefficient = this.getGeometricCorrectionCoefficient(reinforcementWidthRatio);

        // C61 fbm [MPa]	Tensione tang. max di aderenza-valore medio [form. 4.2]
        final BigDecimal averageBondStress = this.getAverageBondStress(shearVerifyExecutionInput.getProduct().getFiberType(), averageCompressiveStrength, averageTensileStrength, confidenceFactor);

        // C62 fbk [MPa]	Tensione tang. max di aderenza-valore caratteristico [form. 4.10]
        final BigDecimal characteristicBondStress = this.getCharacteristicBondStress(shearVerifyExecutionInput.getProduct().getFiberType(), averageCompressiveStrength, averageTensileStrength, confidenceFactor);

        // C63 ΓFm [N/mm]	Energia specifica di frattura-valore medio [form. 4.3]
        // =1/2*C61*0.25
        final BigDecimal averageFractureEnergy = averageBondStress.multiply(BigDecimal.valueOf(0.125)).setScale(SCALE, RoundingMode.HALF_UP);

        // C64 ΓFk [N/mm]	Energia specifica di frattura-valore caratteristico [form. 4.9]
        // =1/2*C62*0.25
        final BigDecimal characteristicAverageFractureEnergy = characteristicBondStress.multiply(BigDecimal.valueOf(0.125)).setScale(SCALE, RoundingMode.HALF_UP);

        // C65 Led [mm]	Lungh. ottimale di ancoraggio di progetto [form. 4.1]
        final BigDecimal optimalDesignAnchorageLength = this.getOptimalDesignAnchorageLength(
                shearVerifyExecutionInput.getProduct().getFiberType(),
                averageBondStress,
                firstExperimentalData,
                equivalentThcikness,
                layersNumber,
                averageFractureEnergy
        );

        // C66 rc/b	Rapporto raggio curvatura/larghezza anima sezione [form. 4.85]
        final BigDecimal curvatureToWebWidthRatio = this.getCurvatureToWebWidthRatio(cornerRadius, width);

        // C67 ФR	Parametro [form. 4.85]
        final BigDecimal phiR = this.getPhiR(curvatureToWebWidthRatio);

        // C68 f_fdd [Mpa]	Tensione max rinforzo modo 1 - valore di progetto [form. 4.7]
        final BigDecimal maxReinforcementStress = this.getMaxReinforcementStress(geometricCorrectionCoefficient,
                firstExperimentalData, characteristicAverageFractureEnergy, layersNumber, equivalentThcikness);
        // C69 f_fed [MPa]	Tensione efficace di calcolo FRP [form. 4.83/4.84]
        final BigDecimal effectiveDesignStressFRP = this.getEffectiveDesignStressFRP(
                reinforcementLayout,
                maxReinforcementStress,
                optimalDesignAnchorageLength,
                stripInclination,
                effectiveDepth,
                freeHeight,
                phiR,
                projectTensionFRP
        );

        /*
         * VERIFICA
         */
        // C73 θ [°]	Inclinazione bielle cls
        final BigDecimal concreteStrutInclination = BigDecimal.valueOf(shearVerifyExecutionInput.getConcreteStrutInclination()).setScale(SCALE, RoundingMode.HALF_UP);
        // C74 Vrd,f [kN]	Contributo FRP a taglio [form 4.81]
        final BigDecimal frpShearContribution = this.getFrpShearContribution(
                shearPartialFactor,
                effectiveDesignStressFRP,
                frpArea,
                effectiveDepth,
                stripSpacingOrthogonalElementAxis,
                concreteStrutInclination,
                stripInclination
        );
        // C75 Vrd,s [kN]	Contributo staffe a taglio [form. 4.1.27 NTC2018]
        final BigDecimal stirrupShearContribution = this.getStirrupShearContribution(
                effectiveDepth,
                transverseArea,
                stirrupSpacing,
                designYieldStrengthForBrittleMechanisms,
                stirrupInclination,
                concreteStrutInclination
        );
        // C76 Vrd,c [kN]	Contributo cls a taglio [form 4.1.28 NTC2018]
        final BigDecimal concreteShearContribution = this.getConcreteShearContribution(
                effectiveDepth,
                width,
                designCompressiveStrengthForBrittleMechanisms,
                stirrupInclination,
                concreteStrutInclination
        );
        // C77  Vrd [kN]	Vrd = min (Vrd,s+Vrd,f ; Vrd,c)
        final BigDecimal shearCapacity = this.getShearCapacity(frpShearContribution, stirrupShearContribution, concreteShearContribution);

        // D74 Vsd Taglio sollecitante
        final BigDecimal appliedShearForce = shearVerifyExecutionInput.getAppliedShearForce();

        // =SE(C77>D74,"Verifica soddisfatta","Verifica non soddisfatta")
        final boolean checkResult = shearCapacity.compareTo(appliedShearForce) >= 0;

        return ShearCalculationResult.builder()
                .frpShearContribution(frpShearContribution)
                .checkResult(checkResult)
                .firstExperimentalData(firstExperimentalData)
                .secondExperimentalData(secondExperimentalData)
                .environmentalConversionFactor(ambientFactor)
                .partialShearFactor(shearPartialFactor)
                .materialPartialFactor(materialPartialFactor)
                .designReinforcementStrain(designReinforcementStrain)
                .geometricCorrectionCoefficient(geometricCorrectionCoefficient)
                .optimalDesignAnchorageLength(optimalDesignAnchorageLength)
                .effectiveDesignStressFrp(effectiveDesignStressFRP)
                .stirrupShearContribution(stirrupShearContribution)
                .concreteShearContribution(concreteShearContribution)
                .shearCapacity(shearCapacity)
                .build();
    }


    // fcd,f = fcm / (FC * γc,f ) [Mpa]	Design compressive strength - brittle mechanisms
    // =D39/D35/1.5
    // EXTRACTED
    protected BigDecimal getDesignCompressiveStrengthForBrittleMechanisms(final RectangularBeamParams params) {
        return materialStrengthService.getDesignCompressiveStrengthForBrittleMechanisms(params.getMaterialProperties());
    }

    // fyd,f = fyk / (FC * γs,f ) [Mpa]	Design yield strength - brittle mechanisms
    // =D45/D35/1.15
    // EXTRACTED
    protected BigDecimal getDesignYieldStrengthForBrittleMechanisms(final RectangularBeamParams params) {
        return materialStrengthService.getDesignYieldStrengthForBrittleMechanisms(params.getMaterialProperties());
    }

    // C59 bf/b	Rapporto largh. rinforzo/largh. sezione
    // =IF(C41/C58<0,25;0,25;C41/C58)
    protected BigDecimal getReinforcementWidthRatio(
            final BigDecimal stripWidth, // C41
            final BigDecimal b // C58
    ) {
        final BigDecimal safeB = b.abs();
        final BigDecimal min = BigDecimal.valueOf(0.25).setScale(SCALE, RoundingMode.HALF_UP);

        if (safeB.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("Width cannot be zero.");
        }

        final BigDecimal ratio = stripWidth.abs().divide(safeB, SCALE, RoundingMode.HALF_UP);
        return (ratio.compareTo(min) < 0 ? min : ratio).setScale(SCALE, RoundingMode.HALF_UP);
    }

    // C60 kb Coeff. correttivo geometrico [Par. 4.1.3]
    // =IF(((2-C59)/(1+C59))^0,5>=1;((2-C59)/(1+C59))^0,5;1)
    protected BigDecimal getGeometricCorrectionCoefficient(
            final BigDecimal reinforcementWidthRatio // C59
    ) {

        final BigDecimal two = BigDecimal.valueOf(2);
        final BigDecimal one = BigDecimal.ONE.setScale(SCALE, RoundingMode.HALF_UP);

        BigDecimal numerator = two.subtract(reinforcementWidthRatio);
        BigDecimal denominator = one.add(reinforcementWidthRatio);

        if (denominator.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("Denominator cannot be zero.");
        }

        BigDecimal fraction = numerator.divide(denominator, SCALE, RoundingMode.HALF_UP);
        double sqrtValue = Math.sqrt(fraction.doubleValue());
        BigDecimal result = BigDecimal.valueOf(sqrtValue).setScale(SCALE, RoundingMode.HALF_UP);

        return (result.compareTo(one) >= 0 ? result : one).setScale(SCALE, RoundingMode.HALF_UP);
    }

    /**
     * C61 fbm [MPa]	Tensione tang. max di aderenza-valore medio [form. 4.2]
     * =IF(C37="Carbonio preformato";0,8*((C23*C24)^0,5)/(2*C32);1,25*((C23*C24)^0,5)/(2*C32))
     *
     * @param productType                C37
     * @param averageCompressiveStrength C23
     * @param averageTensileStrength     C24
     * @param confidenceFactor           C32
     * @return
     */
    protected BigDecimal getAverageBondStress(
            FiberType productType, // C37
            BigDecimal averageCompressiveStrength, // C23
            BigDecimal averageTensileStrength,     // C24
            BigDecimal confidenceFactor            // C32
    ) {
        if (confidenceFactor.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("ConfidenceFactor cannot be zero.");
        }
        final BigDecimal factor = FiberType.PREFORMED_CARBON == productType
                ? BigDecimal.valueOf(0.8)
                : BigDecimal.valueOf(1.25);

        final BigDecimal sqrt = BigDecimal.valueOf(
                Math.sqrt(averageCompressiveStrength.multiply(averageTensileStrength).doubleValue())
        );
        final BigDecimal denominator = confidenceFactor.multiply(BigDecimal.valueOf(2));
        return factor.multiply(sqrt).divide(denominator, SCALE, RoundingMode.HALF_UP);
    }

    /**
     * C62 fbk [MPa]	Tensione tang. max di aderenza-valore caratteristico [form. 4.10]
     * =IF(C37="Carbonio preformato";0,35*((C23*C24)^0,5)/(2*C32);0,6*((C23*C24)^0,5)/(2*C32))
     *
     * @param productType                C37
     * @param averageCompressiveStrength C23
     * @param averageTensileStrength     C24
     * @param confidenceFactor           C32
     * @return characteristicBondStress
     */
    protected BigDecimal getCharacteristicBondStress(
            FiberType productType, // C37
            BigDecimal averageCompressiveStrength, // C23
            BigDecimal averageTensileStrength,     // C24
            BigDecimal confidenceFactor            // C32
    ) {
        if (confidenceFactor.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("ConfidenceFactor cannot be zero.");
        }
        final BigDecimal factor = FiberType.PREFORMED_CARBON == productType
                ? BigDecimal.valueOf(0.35)
                : BigDecimal.valueOf(0.6);

        final BigDecimal sqrt = BigDecimal.valueOf(
                Math.sqrt(averageCompressiveStrength.multiply(averageTensileStrength).doubleValue())
        );
        final BigDecimal denominator = confidenceFactor.multiply(BigDecimal.valueOf(2));
        return factor.multiply(sqrt).divide(denominator, SCALE, RoundingMode.HALF_UP);
    }

    /**
     * C65 Led [mm]	Lungh. ottimale di ancoraggio di progetto [form. 4.1]
     * =SE(C37="Carbonio preformato",MAX(250,1.2/C61*(((PI.GRECO()^2)*C50*C39*C44*C63/2)^0.5)),MAX(100,1.2/C61*(((PI.GRECO()^2)*C50*C39*C44*C63/2)^0.5)))
     *
     * @param productType           C37
     * @param averageBondStress     C61
     * @param elasticModulus        C50
     * @param thickness             C39
     * @param layersNumber          C44
     * @param averageFractureEnergy C63
     * @return optimalDesignAnchorageLength
     */
    protected BigDecimal getOptimalDesignAnchorageLength(
            FiberType productType,           // C37
            BigDecimal averageBondStress,    // C61
            BigDecimal elasticModulus,       // C50
            BigDecimal thickness,            // C39
            BigDecimal layersNumber,         // C44
            BigDecimal averageFractureEnergy // C63
    ) {
        if (averageBondStress.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("averageBondStress cannot be zero.");
        }

        final double piSquared = Math.PI * Math.PI;
        final BigDecimal base = BigDecimal.valueOf(piSquared)
                .multiply(elasticModulus)
                .multiply(thickness)
                .multiply(layersNumber)
                .multiply(averageFractureEnergy)
                .divide(BigDecimal.valueOf(2), SCALE, RoundingMode.HALF_UP);

        final BigDecimal sqrt = BigDecimal.valueOf(Math.sqrt(base.doubleValue()));
        final BigDecimal value = BigDecimal.valueOf(1.2)
                .divide(averageBondStress, SCALE, RoundingMode.HALF_UP)
                .multiply(sqrt)
                .setScale(SCALE, RoundingMode.HALF_UP);

        final BigDecimal min = FiberType.PREFORMED_CARBON == productType
                ? BigDecimal.valueOf(250)
                : BigDecimal.valueOf(100);

        return value.max(min).setScale(SCALE, RoundingMode.HALF_UP);
    }


    /**
     * C66 rc/b	Rapporto raggio curvatura/larghezza anima sezione [form. 4.85]
     * =SE(C17/C6<0,0,SE(C17/C6>0.5,0.5,C17/C6))
     *
     * @param cornerRadius C17
     * @param width        C6
     * @return curvatureToWebWidthRatio
     */
    protected BigDecimal getCurvatureToWebWidthRatio(
            BigDecimal cornerRadius, // C17
            BigDecimal width // C6
    ) {
        if (width.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("width cannot be zero.");
        }
        final BigDecimal ratio = cornerRadius.divide(width, SCALE, RoundingMode.HALF_UP);
        if (ratio.compareTo(BigDecimal.ZERO) < 0) {
            return BigDecimal.ZERO.setScale(SCALE, RoundingMode.HALF_UP);
        }
        if (ratio.compareTo(BigDecimal.valueOf(0.5)) > 0) {
            return BigDecimal.valueOf(0.5).setScale(SCALE, RoundingMode.HALF_UP);
        }
        return ratio.setScale(SCALE, RoundingMode.HALF_UP);
    }

    /**
     * C67 ФR	Parametro [form. 4.85]
     * =0.2+1.6*C66
     *
     * @param curvatureToWebWidthRatio C66
     * @return PhiR
     */
    protected BigDecimal getPhiR(
            BigDecimal curvatureToWebWidthRatio // C66
    ) {
        return curvatureToWebWidthRatio.multiply(BigDecimal.valueOf(1.6)).add(BigDecimal.valueOf(0.2)).setScale(SCALE, RoundingMode.HALF_UP);
    }

    // =C60/1.3*(2*C50*C64/(C44*C39))^0.5

    /**
     *
     * @param geometricCorrectionCoefficient      C60
     * @param elasticModulus                      C50
     * @param characteristicAverageFractureEnergy C64
     * @param layersNumber                        C44
     * @param thickness                           C39
     * @return
     */
    protected BigDecimal getMaxReinforcementStress(
            BigDecimal geometricCorrectionCoefficient, // C60
            BigDecimal elasticModulus,                 // C50
            BigDecimal characteristicAverageFractureEnergy, // C64
            BigDecimal layersNumber,                   // C44
            BigDecimal thickness                       // C39
    ) {
        if (layersNumber.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("layersNumber cannot be zero.");
        }
        if (thickness.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("thickness cannot be zero.");
        }

        final BigDecimal numerator = BigDecimal.valueOf(2)
                .multiply(elasticModulus)
                .multiply(characteristicAverageFractureEnergy);
        final BigDecimal denominator = layersNumber.multiply(thickness);

        final BigDecimal fraction = numerator.divide(denominator, SCALE, RoundingMode.HALF_UP);
        double sqrt = Math.sqrt(fraction.doubleValue());

        return geometricCorrectionCoefficient
                .divide(BigDecimal.valueOf(1.3), SCALE, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(sqrt))
                .setScale(SCALE, RoundingMode.HALF_UP);
    }


    /**
     * C69 f_fed [MPa]	Tensione efficace di calcolo FRP [form. 4.83/4.84]
     * =SE(C40=1,C68*(1-1/3*(C65*SEN(C45*PI.GRECO()/180))/(MIN(0.9*C11,C8))),SE(C40=2,SE(1/2*(C67*C57-C68)*(1-(C65*SEN(C45*PI.GRECO()/180))/(MIN(0.9*C11,C8)))>=0,C68*(1-((C65*SEN(C45*PI.GRECO()/180))/(6*MIN(0.9*C11,C8))))+1/2*(C67*C57-C68)*(1-(C65*SEN(C45*PI.GRECO()/180))/(MIN(0.9*C11,C8)))),C68*(1-((C65*SEN(C45*PI.GRECO()/180))/(6*MIN(0.9*C11,C8))))))
     * NB il caso fallback è stato rimosso perchè nell excel è possibile selezionare solo caso 1 e 2, non servono altri casi
     *
     * @param reinforcementLayout          C40
     * @param maxReinforcementStress       C68
     * @param optimalDesignAnchorageLength C65
     * @param stripInclination             C45 [°]
     * @param effectiveDepth               C11
     * @param freeHeight                   C8
     * @param phiR                         C67
     * @param projectTensionFRP            C57
     * @return effectiveDesignStressFRP
     */
    protected BigDecimal getEffectiveDesignStressFRP(
            ReinforcementLayout reinforcementLayout, // C40
            BigDecimal maxReinforcementStress,      // C68
            BigDecimal optimalDesignAnchorageLength, // C65
            BigDecimal stripInclination,            // C45 [°]
            BigDecimal effectiveDepth,              // C11
            BigDecimal freeHeight,                  // C8
            BigDecimal phiR,                        // C67
            BigDecimal projectTensionFRP            // C57
    ) {
        // Calcolo min(0.9*C11, C8)
        final BigDecimal minDepth = BigDecimal.valueOf(0.9)
                .multiply(effectiveDepth)
                .min(freeHeight)
                .setScale(SCALE, RoundingMode.HALF_UP);

        // Calcolo C65*SEN(C45*PI.GRECO()/180)
        double angleRad = stripInclination.doubleValue() * Math.PI / 180.0;
        BigDecimal anchorageSin = optimalDesignAnchorageLength
                .multiply(BigDecimal.valueOf(Math.sin(angleRad)))
                .setScale(SCALE, RoundingMode.HALF_UP);

        return switch (reinforcementLayout) {
            case OPEN_STIRRUP: {
                // C68*(1-1/3*(C65*SEN(C45*PI.GRECO()/180))/(MIN(0.9*C11,C8)))
                final BigDecimal term = anchorageSin
                        .divide(minDepth, SCALE, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(1.0 / 3.0));
                yield maxReinforcementStress
                        .multiply(BigDecimal.ONE.subtract(term))
                        .setScale(SCALE, RoundingMode.HALF_UP);
            }
            case CLOSED_STIRRUP: {
                // 1/2*(C67*C57-C68)*(1-(C65*SEN(C45*PI.GRECO()/180))/(MIN(0.9*C11,C8)))
                BigDecimal oneMinus = BigDecimal.ONE.subtract(
                        anchorageSin.divide(minDepth, SCALE, RoundingMode.HALF_UP)
                );
                BigDecimal half = BigDecimal.valueOf(0.5);
                BigDecimal diff = phiR.multiply(projectTensionFRP).subtract(maxReinforcementStress);
                BigDecimal cond = half.multiply(diff).multiply(oneMinus);

                if (cond.compareTo(BigDecimal.ZERO) >= 0) {
                    // C68*(1-((C65*SEN(C45*PI.GRECO()/180))/(6*MIN(0.9*C11,C8)))) + cond
                    BigDecimal denom = minDepth.multiply(BigDecimal.valueOf(6));
                    BigDecimal term = anchorageSin.divide(denom, SCALE, RoundingMode.HALF_UP);
                    BigDecimal first = maxReinforcementStress.multiply(BigDecimal.ONE.subtract(term));
                    yield first.add(cond).setScale(SCALE, RoundingMode.HALF_UP);
                } else {
                    // C68*(1-((C65*SEN(C45*PI.GRECO()/180))/(6*MIN(0.9*C11,C8))))
                    BigDecimal denom = minDepth.multiply(BigDecimal.valueOf(6));
                    BigDecimal term = anchorageSin.divide(denom, SCALE, RoundingMode.HALF_UP);
                    yield maxReinforcementStress
                            .multiply(BigDecimal.ONE.subtract(term))
                            .setScale(SCALE, RoundingMode.HALF_UP);
                }
            }
        };
    }

    /**
     * C74 Vrd,f [kN]	Contributo FRP a taglio [form 4.81]
     * =(1/C54*C69*C46*0.9*C11/C43*(COT(C73*PI.GRECO()/180)+COT(C45*PI.GRECO()/180))*(SEN(C45*PI.GRECO()/180)^2))/1000
     *
     * @param shearPartialFactor       C54
     * @param effectiveDesignStressFRP C69
     * @param frpArea                  C46
     * @param effectiveDepth           C11
     * @param stripSpacingOrthogonal   C43
     * @param concreteStrutInclination C73 [°]
     * @param stripInclination         C45 [°]
     * @return frpShearContribution
     */
    protected BigDecimal getFrpShearContribution(
            BigDecimal shearPartialFactor,         // C54
            BigDecimal effectiveDesignStressFRP,   // C69
            BigDecimal frpArea,                    // C46
            BigDecimal effectiveDepth,             // C11
            BigDecimal stripSpacingOrthogonal,     // C43
            BigDecimal concreteStrutInclination,   // C73 [°]
            BigDecimal stripInclination            // C45 [°]
    ) {
        // 1 / C54
        final BigDecimal oneOverShearPartial = BigDecimal.ONE.divide(shearPartialFactor, SCALE, RoundingMode.HALF_UP);

        // 0.9 * C11
        final BigDecimal depth09 = BigDecimal.valueOf(0.9).multiply(effectiveDepth);

        // COT(C73*PI.GRECO()/180)
        double theta = concreteStrutInclination.doubleValue();
        double cotTheta = (Math.abs(theta % 180) == 90) ? 0.0 : 1.0 / Math.tan(Math.toRadians(theta));

        // COT(C45*PI.GRECO()/180)
        double beta = stripInclination.doubleValue();
        double cotBeta = (Math.abs(beta % 180) == 90) ? 0.0 : 1.0 / Math.tan(Math.toRadians(beta));

        // SEN(C45*PI.GRECO()/180)^2
        double sinBeta = Math.sin(Math.toRadians(beta));
        double sinBetaSquared = sinBeta * sinBeta;

        // Prodotto dei termini
        final BigDecimal result = oneOverShearPartial
                .multiply(effectiveDesignStressFRP)
                .multiply(frpArea)
                .multiply(depth09)
                .divide(stripSpacingOrthogonal, SCALE, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(cotTheta + cotBeta))
                .multiply(BigDecimal.valueOf(sinBetaSquared))
                .divide(BigDecimal.valueOf(1000), SCALE, RoundingMode.HALF_UP);

        return result.setScale(SCALE, RoundingMode.HALF_UP);
    }


    /**
     * C75 Vrd,s [kN]	Contributo staffe a taglio [form. 4.1.27 NTC2018]
     * =(0.9*C11*C14/C15*C29*(COT(C16*PI.GRECO()/180)+COT(C73*PI.GRECO()/180))*SEN(C16*PI.GRECO()/180))/1000
     *
     * @param effectiveDepth           C11
     * @param transverseArea           C14
     * @param stirrupSpacing           C15
     * @param designYieldStrength      C29
     * @param stirrupInclination       C16 [°]
     * @param concreteStrutInclination C73 [°]
     * @return
     */
    protected BigDecimal getStirrupShearContribution(
            BigDecimal effectiveDepth,           // C11
            BigDecimal transverseArea,           // C14
            BigDecimal stirrupSpacing,           // C15
            BigDecimal designYieldStrength,      // C29
            BigDecimal stirrupInclination,       // C16 [°]
            BigDecimal concreteStrutInclination  // C73 [°]
    ) {
        // 0.9 * C11
        final BigDecimal depth09 = BigDecimal.valueOf(0.9).multiply(effectiveDepth);

        // COT(C16*PI.GRECO()/180)
        double alpha = stirrupInclination.doubleValue();
        double cotAlpha = (Math.abs(alpha % 180) == 90) ? 0.0 : 1.0 / Math.tan(Math.toRadians(alpha));

        // COT(C73*PI.GRECO()/180)
        double theta = concreteStrutInclination.doubleValue();
        double cotTheta = (Math.abs(theta % 180) == 90) ? 0.0 : 1.0 / Math.tan(Math.toRadians(theta));

        // SEN(C16*PI.GRECO()/180)
        double sinAlpha = Math.sin(Math.toRadians(alpha));

        // Prodotto dei termini
        final BigDecimal result = depth09
                .multiply(transverseArea)
                .divide(stirrupSpacing, SCALE, RoundingMode.HALF_UP)
                .multiply(designYieldStrength)
                .multiply(BigDecimal.valueOf(cotAlpha + cotTheta))
                .multiply(BigDecimal.valueOf(sinAlpha))
                .divide(BigDecimal.valueOf(1000), SCALE, RoundingMode.HALF_UP);

        return result.setScale(SCALE, RoundingMode.HALF_UP);
    }

    // C76 Vrd,c [kN]	Contributo cls a taglio [form 4.1.28 NTC2018]
    // =(0.9*C11*C6*1*0.5*C22*(COT(C16*PI.GRECO()/180)+COT(C73*PI.GRECO()/180))/(1+(COT(C73*PI.GRECO()/180)^2)))/1000

    /**
     * C76 Vrd,c [kN]	Contributo cls a taglio [form 4.1.28 NTC2018]
     * =(0.9*C11*C6*1*0.5*C22*(COT(C16*PI.GRECO()/180)+COT(C73*PI.GRECO()/180))/(1+(COT(C73*PI.GRECO()/180)^2)))/1000
     *
     * @param effectiveDepth            C11
     * @param width                     C6
     * @param designCompressiveStrength C22
     * @param stirrupInclination        C16 [°]
     * @param concreteStrutInclination  C73 [°]
     * @return
     */
    protected BigDecimal getConcreteShearContribution(
            BigDecimal effectiveDepth,           // C11
            BigDecimal width,                    // C6
            BigDecimal designCompressiveStrength,// C22
            BigDecimal stirrupInclination,       // C16 [°]
            BigDecimal concreteStrutInclination  // C73 [°]
    ) {
        // 0.9 * C11
        final BigDecimal depth09 = BigDecimal.valueOf(0.9).multiply(effectiveDepth);

        // COT(C16*PI.GRECO()/180)
        double alpha = stirrupInclination.doubleValue();
        double cotAlpha = (Math.abs(alpha % 180) == 90) ? 0.0 : 1.0 / Math.tan(Math.toRadians(alpha));

        // COT(C73*PI.GRECO()/180)
        double theta = concreteStrutInclination.doubleValue();
        double cotTheta = (Math.abs(theta % 180) == 90) ? 0.0 : 1.0 / Math.tan(Math.toRadians(theta));

        // Denominatore: 1 + (COT(C73*PI.GRECO()/180))^2
        double denominator = 1.0 + (cotTheta * cotTheta);

        // Prodotto dei termini
        final BigDecimal result = depth09
                .multiply(width)
                .multiply(BigDecimal.valueOf(0.5))
                .multiply(designCompressiveStrength)
                .multiply(BigDecimal.valueOf(cotAlpha + cotTheta))
                .divide(BigDecimal.valueOf(denominator), SCALE, RoundingMode.HALF_UP)
                .divide(BigDecimal.valueOf(1000), SCALE, RoundingMode.HALF_UP);

        return result.setScale(SCALE, RoundingMode.HALF_UP);
    }


    /**
     * C77 Vrd [kN]	Vrd = min (Vrd,s+Vrd,f ; Vrd,c)
     * =MIN(C75+C74,C76)
     *
     * @param frpShearContribution      C74
     * @param stirrupShearContribution  C75
     * @param concreteShearContribution C76
     * @return
     */
    protected BigDecimal getShearCapacity(
            BigDecimal frpShearContribution,     // C74
            BigDecimal stirrupShearContribution, // C75
            BigDecimal concreteShearContribution // C76
    ) {
        final BigDecimal sum = stirrupShearContribution.add(frpShearContribution);
        return sum.min(concreteShearContribution).setScale(SCALE, RoundingMode.HALF_UP);
    }
}
