package it.kimia.atlas.atlasservices.calculationmodule;

import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.List;
import java.util.Optional;

@Component
public class DefaultParamApplierFactory implements ParamApplierFactory {

    EnumMap<ModuleType, ParamApplier> paramAppliers = new EnumMap<>(ModuleType.class);

    DefaultParamApplierFactory(List<ParamApplier> appliers) {
        appliers.forEach(applier -> paramAppliers.put(applier.getModuleType(), applier));
    }

    public ParamApplier get(CalculationModule calculationModule) {
        return Optional.ofNullable(paramAppliers.get(calculationModule.getType())).orElseThrow(() -> new IllegalArgumentException("Unsupported module type: " + calculationModule.getType()));
    }
}
