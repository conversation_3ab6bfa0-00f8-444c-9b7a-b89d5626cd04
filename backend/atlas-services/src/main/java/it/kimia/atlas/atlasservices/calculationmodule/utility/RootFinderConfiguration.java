package it.kimia.atlas.atlasservices.calculationmodule.utility;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.math.BigDecimal;

@Configuration
public class RootFinderConfiguration {

    @Bean("brent")
    public RootFinder brentRootFinder(
            @Value("${rootfinder.brent.absTol:1e-12}") double absTol,
            @Value("${rootfinder.brent.relTol:1e-12}") double relTol,
            @Value("${rootfinder.brent.fTol:1e-12}") double fTol,
            @Value("${rootfinder.brent.maxEval:1000}") int maxEval
    ) {
        return new BrentRootFinderAdapter(absTol, relTol, fTol, maxEval);
    }

    @Bean("bisection")
    public RootFinder bisectRootFinder(
            @Value("${rootfinder.bisection.tol:1e-12}") BigDecimal tol,
            @Value("${rootfinder.bisection.max-iter:1000}") int maxIter
    ) {
        return new BisectionRootFinder(tol, maxIter);
    }

    @Primary
    @Bean
    RootFinder rootFinder(
            @Qualifier("bisection") RootFinder bisection,
            @Qualifier("brent") RootFinder brent,
            @Value("${rootfinder.qualifier:bisection}") String which
    ) {
        return "brent".equalsIgnoreCase(which) ? brent : bisection;
    }
}