package it.kimia.atlas.atlasservices.calculationmodule.strategy;

import com.fasterxml.jackson.databind.ObjectMapper;
import it.kimia.atlas.atlasservices.calculationmodule.CalculationModule;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.dto.CalculationRequest;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.factory.CalculationStrategyFactory;
import it.kimia.atlas.atlasservices.calculationmodule.validator.ValidationException;
import it.kimia.atlas.atlasservices.project.Project;
import it.kimia.atlas.atlasservices.project.ProjectRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service for executing calculations on modules using strategies.
 * <p>
 * This service provides methods to execute calculations on modules using the appropriate
 * strategy based on the module type and calculation type.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CalculationService {

    private final ProjectRepository projectRepository;
    private final StrategyFactoryRegistry strategyFactoryRegistry;
    private final ObjectMapper objectMapper;

    /**
     * Executes a calculation on a module.
     * <p>
     * This method orchestrates the process: finding the project and module,
     * selecting the correct strategy, and delegating the execution.
     * The flow is intentionally explicit for better readability and debugging.
     *
     * @param projectId the ID of the project containing the module
     * @param moduleId  the ID of the module to execute the calculation on
     * @param request   the calculation request containing the calculation type and input
     * @return an Optional containing the updated module if successful, otherwise empty.
     */
    @Transactional
    public Optional<CalculationModule> executeCalculation(String projectId, String moduleId, CalculationRequest request) {

        Optional<Project> projectOpt = projectRepository.findById(projectId);
        if (projectOpt.isEmpty()) {
            log.warn("Project with ID '{}' not found. Calculation aborted.", projectId);
            return Optional.empty();
        }
        Project project = projectOpt.get();

        Optional<CalculationModule> moduleOpt = findModuleInProject(project, moduleId);
        if (moduleOpt.isEmpty()) {
            log.warn("Module with ID '{}' not found in project '{}'. Calculation aborted.", moduleId, projectId);
            return Optional.empty();
        }

        CalculationModule module = moduleOpt.get();
        Optional<CalculationStrategyFactory<?>> factoryOpt = strategyFactoryRegistry.findFactory(module);
        if (factoryOpt.isEmpty()) {
            log.warn("No strategy factory found for module type '{}'. Calculation aborted.", module.getType());
            return Optional.empty();
        }

        CalculationStrategyFactory<?> factory = factoryOpt.get();
        return createAndExecuteStrategy(project, factory, module, request);
    }

    private <T extends CalculationModule> Optional<CalculationModule> createAndExecuteStrategy(
            Project project, CalculationStrategyFactory<T> factory, CalculationModule module, CalculationRequest request) {
        try {
            CalculationModule updatedModule = factory.createStrategy(request.getCalculationType())
                    .map(strategy -> performCalculation(strategy, module, request.getInput()))
                    .orElseThrow(() -> new IllegalArgumentException("No strategy found for calculation type: " + request.getCalculationType()));

            updateModuleInProject(project, module, updatedModule);
            projectRepository.save(project);

            return Optional.of(updatedModule);
        } catch (IllegalArgumentException e) {
            log.warn("Validation error during calculation for module '{}' in project '{}': {}", module.getId(), project.getId(), e.getMessage());
            throw e;
        } catch (ValidationException e) {
            log.error("Validation error for module {}: {}", module.getId(), e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("An unexpected error occurred during calculation for module '{}' in project '{}'", module.getId(), project.getId(), e);
            return Optional.empty();
        }
    }

    /**
     * Performs the actual calculation, updates the module, and saves the project.
     * This method contains the core logic that was previously inside the confusing flatMap.
     */
    private <T extends CalculationModule, I> T performCalculation(CalculationStrategy<T, I> strategy, CalculationModule module, Object rawInput) {
        try {
            I typedInput = objectMapper.convertValue(rawInput, strategy.getInputType());
            T updatedModule = strategy.execute((T) module, typedInput);
            updatedModule.setLastModified(LocalDateTime.now());
            return updatedModule;

        } catch (IllegalArgumentException e) {
            throw e;
        } catch (ClassCastException e) {
            log.error("Type mismatch: Cannot cast module {} to the type expected by the strategy. This is a configuration error.", module.getId(), e);
            throw new IllegalStateException("Configuration error: Mismatch between module and strategy types.", e);
        } catch (ValidationException e) {
            log.error("Validation error for module {}: {}", module.getId(), e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Calculation failed for module {}: {}", module.getId(), e.getMessage(), e);
            throw new RuntimeException("Failed to execute calculation", e);
        }
    }

    private Optional<CalculationModule> findModuleInProject(Project project, String moduleId) {
        return project.getModules().stream()
                .filter(m -> m.getId().equals(moduleId))
                .findFirst();
    }

    /**
     * Replaces an old module instance with the updated one in the project's module list.
     */
    private void updateModuleInProject(Project project, CalculationModule oldModule, CalculationModule newModule) {
        List<CalculationModule> modules = project.getModules();
        List<CalculationModule> updatedModules = modules.stream()
                .map(module -> module.equals(oldModule) ? newModule : module)
                .toList();
        if (updatedModules.size() != modules.size()) {
            log.warn("Module list size changed unexpectedly during update. Project: {}, Module: {}", project.getId(), oldModule.getId());
        }
        project.setModules(updatedModules);
    }
}