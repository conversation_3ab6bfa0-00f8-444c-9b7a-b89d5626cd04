package it.kimia.atlas.atlasservices.materials;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Represents a wood material in the system.
 */
@Document(collection = "wood_materials")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Wood {

    @Id
    private String id;

    /**
     * The name of the wood material (e.g., "C14", "D18", "GL20h").
     */
    private String name;

    /**
     * The category of the wood material.
     */
    private WoodCategory category;
    
    /**
     * The flexural strength in MPa.
     */
    private Double flexuralStrength;
    
    /**
     * The tensile strength parallel to grain in MPa.
     */
    private Double tensileStrengthParallel;

    /**
     * The tensile strength perpendicular to grain in MPa.
     */
    private Double tensileStrengthPerpendicular;
    
    /**
     * The compressive strength parallel to grain in MPa.
     */
    private Double compressiveStrengthParallel;

    /**
     * The compressive strength perpendicular to grain in MPa.
     */
    private Double compressiveStrengthPerpendicular;
    
    /**
     * The shear strength in MPa.
     */
    private Double shearStrength;
    
    /**
     * The mean elastic modulus parallel to grain in MPa.
     */
    private Double elasticModulusMeanParallel;
    
    /**
     * The characteristic elastic modulus parallel to grain in MPa.
     */
    private Double elasticModulusCharacteristicParallel;

    /**
     * The mean elastic modulus perpendicular to grain in MPa.
     */
    private Double elasticModulusMeanPerpendicular;
    
    /**
     * The mean shear modulus in MPa.
     */
    private Double shearModulusMean;
    
    /**
     * The characteristic density in kg/m³.
     */
    private Double characteristicDensity;
    
    /**
     * The mean density in kg/m³.
     */
    private Double meanDensity;
}