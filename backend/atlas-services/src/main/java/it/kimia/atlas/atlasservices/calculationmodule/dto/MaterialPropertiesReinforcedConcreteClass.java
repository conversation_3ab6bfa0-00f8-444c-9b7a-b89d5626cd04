package it.kimia.atlas.atlasservices.calculationmodule.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MaterialPropertiesReinforcedConcreteClass {
    @NotNull(message = "validation.rectangularbeam.concreteClass.id.notnull")
    private String id;

    @NotNull(message = "validation.rectangularbeam.concreteClass.name.notnull")
    private String name;

    @NotNull(message = "validation.rectangularbeam.concreteClass.cubeCompressiveStrength.notnull")
    @Positive(message = "validation.rectangularbeam.concreteClass.cubeCompressiveStrength.positive")
    private BigDecimal cubeCompressiveStrength;

    @NotNull(message = "validation.rectangularbeam.concreteClass.cylinderCompressiveStrength.notnull")
    @Positive(message = "validation.rectangularbeam.concreteClass.cylinderCompressiveStrength.positive")
    private BigDecimal cylinderCompressiveStrength;

    @NotNull(message = "validation.rectangularbeam.concreteClass.averageCompressiveStrength.notnull")
    @Positive(message = "validation.rectangularbeam.concreteClass.averageCompressiveStrength.positive")
    private BigDecimal averageCompressiveStrength;

    @NotNull(message = "validation.rectangularbeam.concreteClass.averageTensileStrength.notnull")
    @Positive(message = "validation.rectangularbeam.concreteClass.averageTensileStrength.positive")
    private BigDecimal averageTensileStrength;

    @NotNull(message = "validation.rectangularbeam.concreteClass.elasticModulus.notnull")
    @Positive(message = "validation.rectangularbeam.concreteClass.elasticModulus.positive")
    private BigDecimal elasticModulus;

    @NotNull(message = "validation.rectangularbeam.concreteClass.designCompressiveStrengthForBrittleMechanisms.notnull")
    @Positive(message = "validation.rectangularbeam.concreteClass.designCompressiveStrengthForBrittleMechanisms.positive")
    private BigDecimal designCompressiveStrengthForBrittleMechanisms;

    @NotNull(message = "validation.rectangularbeam.concreteClass.designCompressiveStrengthForDuctileMechanisms.notnull")
    @Positive(message = "validation.rectangularbeam.concreteClass.designCompressiveStrengthForDuctileMechanisms.positive")
    private BigDecimal designCompressiveStrengthForDuctileMechanisms;
}
