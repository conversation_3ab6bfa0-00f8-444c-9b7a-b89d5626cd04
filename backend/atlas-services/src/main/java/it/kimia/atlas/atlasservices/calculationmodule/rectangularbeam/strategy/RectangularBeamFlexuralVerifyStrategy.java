package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy;

import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamModule;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamParams;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.RectangularBeamFlexuralVerifyInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.mapper.FlexuralVerifyExecutionInputMapper;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.output.FlexuralCalculationResult;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services.RectangularBeamFlexuralVerify;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationStrategy;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationType;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.StrategyInputValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Strategy implementation for FLEXURAL_VERIFY calculations on RectangularBeamModule.
 * <p>
 * This strategy performs flexural verification calculations on rectangular beam modules.
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RectangularBeamFlexuralVerifyStrategy implements CalculationStrategy<RectangularBeamModule, RectangularBeamFlexuralVerifyInput> {

    private final StrategyInputValidator<RectangularBeamFlexuralVerifyInput> verifyService;
    private final RectangularBeamFlexuralVerify calculationService;
    private final FlexuralVerifyExecutionInputMapper flexuralVerifyExecutionInputMapper;


    @Override
    public Class<RectangularBeamFlexuralVerifyInput> getInputType() {
        return RectangularBeamFlexuralVerifyInput.class;
    }

    @Override
    public RectangularBeamModule execute(RectangularBeamModule module, RectangularBeamFlexuralVerifyInput input) {
        // Validate input
        final RectangularBeamFlexuralVerifyInput verifiedInput = verifyService.validate(input);

        // Get module parameters
        final RectangularBeamParams params = module.getParams();
        if (params == null) {
            throw new IllegalStateException("Module parameters are not set");
        }

        FlexuralVerifyExecutionInput rectagulareBeamFlexuralVerifyExecutionInput =
                flexuralVerifyExecutionInputMapper.map(verifiedInput);

        FlexuralCalculationResult result = calculationService.execute(params, rectagulareBeamFlexuralVerifyExecutionInput);

        log.info("FLEXURAL_VERIFY calculation completed for module {} with bending moment {} kNm, strip width {} mm, and layers number {}",
                module.getId(), input.getBendingMoment(), input.getStripWidth(), input.getLayersNumber());

        module.setFlexuralVerifyExecutionInput(rectagulareBeamFlexuralVerifyExecutionInput);
        module.setFlexuralCalculationResult(result);

        return module;
    }

    @Override
    public ModuleType getModuleType() {
        return ModuleType.RECTANGULAR_BEAM;
    }

    @Override
    public CalculationType getCalculationType() {
        return CalculationType.FLEXURAL_VERIFY;
    }

    private FlexuralCalculationResult executeFlexuralVerify(RectangularBeamParams params, RectangularBeamFlexuralVerifyInput input) {
        return FlexuralCalculationResult.builder().build();
    }
}