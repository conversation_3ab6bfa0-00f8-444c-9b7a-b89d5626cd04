package it.kimia.atlas.atlasservices.calculationmodule.strategy;

import it.kimia.atlas.atlasservices.calculationmodule.validator.ValidationException;
import it.kimia.atlas.atlasservices.calculationmodule.validator.model.ValidationResult;
import it.kimia.atlas.atlasservices.shared.model.FieldErrorDTO;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class StrategyInputValidator<T> {

    private static final ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
    private static final Validator validator = factory.getValidator();

    public T validate(T input) {
        ValidationResult result = validateInput(input);
        if (!result.isValid()) {
            throw new ValidationException(result.getErrorMessage(), result.getFieldErrors());
        }
        return input;
    }

    private ValidationResult validateInput(T input) {
        if (input == null) {
            return ValidationResult.invalid("Shear Verify Input Validation Failed", List.of(
                    new FieldErrorDTO("input", "notnull", "Input cannot be null")
            ));
        }

        Set<ConstraintViolation<T>> violations = validator.validate(input);
        if (!violations.isEmpty()) {
            List<FieldErrorDTO> errors = violations.stream()
                    .map(v -> new FieldErrorDTO(
                            v.getPropertyPath().toString(),
                            v.getMessage(),
                            "Validation failed for field: " + v.getPropertyPath()
                    ))
                    .collect(Collectors.toList());
            return ValidationResult.invalid("Verify Input Validation Failed", errors);
        }
        return ValidationResult.valid();
    }
}