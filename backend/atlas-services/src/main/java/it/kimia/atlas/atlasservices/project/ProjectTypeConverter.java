package it.kimia.atlas.atlasservices.project;

import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.ReadingConverter;
import org.springframework.stereotype.Component;

/**
 * Custom converter for WoodCategory enum.
 * Handles unknown category values from MongoDB by returning WoodCategory.UNKNOWN
 * instead of throwing an exception.
 */
@Component
@ReadingConverter
public class ProjectTypeConverter implements Converter<String, ProjectType> {

    @Override
    public ProjectType convert(String source) {
        if (source == null) {
            return null;
        }

        try {
            return ProjectType.fromValue(source);
        } catch (IllegalArgumentException e) {
            // If the source string doesn't match any enum value,
            // return UNKNOWN instead of throwing an exception
            return ProjectType.UNKNOWN;
        }
    }
}