package it.kimia.atlas.atlasservices.project.mappers;

import it.kimia.atlas.atlasservices.project.Project;
import it.kimia.atlas.atlasservices.project.dtos.ProjectCreateDTO;
import it.kimia.atlas.atlasservices.project.dtos.ProjectUpdateDTO;
import org.mapstruct.*;

/**
 * Mapper for the Project entity and its DTOs.
 */
@Mapper(componentModel = "spring")
public interface ProjectMapper {

    /**
     * Maps a ProjectCreateDTO to a Project entity.
     * Fields managed by the system (id, userId, createdAt, lastModified) are ignored.
     *
     * @param dto the source DTO
     * @return the mapped Project entity
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "userId", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModified", ignore = true)
    Project toEntity(ProjectCreateDTO dto);


    /**
     * Updates an existing Project entity from a ProjectUpdateDTO.
     * Null properties in the DTO will be ignored, preserving existing values in the entity.
     *
     * @param dto     the source DTO with new values
     * @param project the target entity to be updated
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateProjectFromDto(ProjectUpdateDTO dto, @MappingTarget Project project);

}