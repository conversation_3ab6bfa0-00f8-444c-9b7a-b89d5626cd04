package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.Polarity;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamParams;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class ConcreteCoverService {

    public BigDecimal getCompressedCover(final RectangularBeamParams params) {
        // M+: lembo compresso = superiore -> top cover; M-: lembo compresso = inferiore -> bottom cover
        return isPositive(params) ? params.getGeometry().getTopConcreteCover()
                : params.getGeometry().getBottomConcreteCover();
    }

    public BigDecimal getTensionCover(final RectangularBeamParams params) {
        // M+: teso = intradosso (inferiore) -> bottom cover; M-: teso = estradosso (superiore) -> top cover
        return isPositive(params) ? params.getGeometry().getBottomConcreteCover()
                : params.getGeometry().getTopConcreteCover();
    }

    public BigDecimal getCompressiveSteelArea(final RectangularBeamParams params) {
        // M+: As2 (superiore) è compresso; M-: As2 (inferiore) è compresso
        return isPositive(params) ? params.getReinforcementBar().getTop().getArea()
                : params.getReinforcementBar().getBottom().getArea();
    }

    public BigDecimal getTensileSteelArea(final RectangularBeamParams params) {
        // M+: As1 (inferiore) è teso; M-: As1 (superiore) è teso
        return isPositive(params) ? params.getReinforcementBar().getBottom().getArea()
                : params.getReinforcementBar().getTop().getArea();
    }

    public BigDecimal getTransverseArea(final RectangularBeamParams params) {
        return params.getReinforcementBar().getTransverse().area();
    }

    private boolean isPositive(final RectangularBeamParams params) {
        return params.getPolarity() == Polarity.POSITIVE;
    }
}