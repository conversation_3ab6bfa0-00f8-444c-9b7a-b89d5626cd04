package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.dto.MaterialProperties;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Optional;

@Primary
@Service
public class MaterialStrengthFromParamsService implements MaterialStrengthService {

    final MaterialStrengthService materialStrengthService;

    public MaterialStrengthFromParamsService(
            @Qualifier("materialStrengthCalculatorService")
            MaterialStrengthService materialStrengthService) {
        this.materialStrengthService = materialStrengthService;
    }

    public BigDecimal getDesignYieldStrengthForDuctileMechanisms(final MaterialProperties materialProperties) {
        return Optional.ofNullable(materialProperties.getSteelGrade().getDesignYieldStrengthForDuctileMechanisms())
                .orElseGet(() -> materialStrengthService.getDesignYieldStrengthForDuctileMechanisms(materialProperties));
    }

    // fyd,f = fyk / (FC * γs,f ) [Mpa]	Design yield strength - brittle mechanisms
    public BigDecimal getDesignYieldStrengthForBrittleMechanisms(final MaterialProperties materialProperties) {
        return Optional.ofNullable(materialProperties.getSteelGrade().getDesignYieldStrengthForBrittleMechanisms())
                .orElseGet(() -> materialStrengthService.getDesignCompressiveStrengthForBrittleMechanisms(materialProperties));
    }

    // fcd,d = fcm / (FC * γc,d ) [Mpa]	Design compressive strength - ductile mechanisms
    public BigDecimal getDesignCompressiveStrengthForDuctileMechanisms(final MaterialProperties materialProperties) {
        return Optional.ofNullable(materialProperties.getConcreteClass().getDesignCompressiveStrengthForDuctileMechanisms())
                .orElseGet(() -> materialStrengthService.getDesignYieldStrengthForBrittleMechanisms(materialProperties));
    }

    // fcd,d = fcm / (FC * γc,d ) [Mpa]	Design compressive strength - brittle mechanisms
    public BigDecimal getDesignCompressiveStrengthForBrittleMechanisms(final MaterialProperties materialProperties) {
        return Optional.ofNullable(materialProperties.getConcreteClass().getDesignCompressiveStrengthForBrittleMechanisms())
                .orElseGet(() -> materialStrengthService.getDesignCompressiveStrengthForBrittleMechanisms(materialProperties));
    }
}