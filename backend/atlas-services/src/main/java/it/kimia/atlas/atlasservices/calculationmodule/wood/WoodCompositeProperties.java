package it.kimia.atlas.atlasservices.calculationmodule.wood;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WoodCompositeProperties {

    private BigDecimal frpElasticityModulus;
    private BigDecimal frpDesignMaximumStrain;
    private BigDecimal frpCharacteristicStrain;
    private BigDecimal frpPartialFactorInUls;
    private BigDecimal frpMaximumStrainForDebonding;
    private BigDecimal loadConditionFactor;
    private BigDecimal frpPartialFactorInUlsForDebonding;
    private BigDecimal reinforcementToSectionWidthRatio;
    private BigDecimal reinforcementToSectionWidthRatioUsefull;
    private BigDecimal geometricCorrectionFactor;
    private BigDecimal geometricCorrectionFactorUsefull;
    private BigDecimal experimentalCorrectionFactor;
    private BigDecimal confidenceFactor;
    private BigDecimal sectionModulus;
    private BigDecimal momentOfInertiaAboutY;

}
