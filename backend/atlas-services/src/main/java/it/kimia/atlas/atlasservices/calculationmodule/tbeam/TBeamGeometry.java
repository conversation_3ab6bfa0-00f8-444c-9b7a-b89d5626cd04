package it.kimia.atlas.atlasservices.calculationmodule.tbeam;

import it.kimia.atlas.atlasservices.calculationmodule.Exposure;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Geometry parameters for T-shaped beam.
 * <p>
 * This class contains the geometric parameters specific to T-shaped beams.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TBeamGeometry {
    @Positive(message = "validation.tbeam.geometry.webHeight.positive")
    @NotNull(message = "validation.tbeam.geometry.webHeight.notnull")
    private BigDecimal webHeight;

    @Positive(message = "validation.tbeam.geometry.webWidth.positive")
    @NotNull(message = "validation.tbeam.geometry.webWidth.notnull")
    private BigDecimal webWidth;

    @Positive(message = "validation.tbeam.geometry.flangeWidth.positive")
    @NotNull(message = "validation.tbeam.geometry.flangeWidth.notnull")
    private BigDecimal flangeWidth;

    @Positive(message = "validation.tbeam.geometry.flangeThickness.positive")
    @NotNull(message = "validation.tbeam.geometry.flangeThickness.notnull")
    private BigDecimal flangeThickness;

    @Positive(message = "validation.tbeam.geometry.totalWidth.positive")
    @NotNull(message = "validation.tbeam.geometry.totalHeight.notnull")
    private BigDecimal totalHeight;

    @Positive(message = "validation.tbeam.geometry.totalWidth.positive")
    @NotNull(message = "validation.tbeam.geometry.topConcreteCover.notnull")
    private BigDecimal topConcreteCover;

    @Positive(message = "validation.tbeam.geometry.totalWidth.positive")
    @NotNull(message = "validation.tbeam.geometry.bottomConcreteCover.notnull")
    private BigDecimal bottomConcreteCover;

    @Positive(message = "validation.tbeam.geometry.effectiveDepth.positive")
    @NotNull(message = "validation.tbeam.geometry.effectiveDepth.notnull")
    private BigDecimal effectiveDepth;

    @NotNull(message = "validation.tbeam.geometry.exposure.notnull")
    private Exposure exposure;

}