package it.kimia.atlas.atlasservices;

import it.kimia.atlas.atlasservices.calculationmodule.validator.ValidationException;
import it.kimia.atlas.atlasservices.shared.model.ErrorResponseDTO;
import it.kimia.atlas.atlasservices.shared.model.FieldErrorDTO;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.util.List;
import java.util.stream.Collectors;

@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponseDTO> handleValidationExceptions(MethodArgumentNotValidException ex) {

        List<FieldErrorDTO> errors = ex.getBindingResult().getAllErrors().stream()
                .map(error -> {
                    String fieldName = ((FieldError) error).getField();
                    String errorCode = error.getDefaultMessage();
                    return new FieldErrorDTO(fieldName, errorCode, "Validation failed for field: " + fieldName);
                })
                .collect(Collectors.toList());

        ErrorResponseDTO errorResponse = new ErrorResponseDTO("Validation Failed", errors);

        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handles validation exceptions thrown by custom validators.
     * Returns a response in the same format as Spring validation errors.
     *
     * @param ex the ValidationException
     * @return a response entity with the validation errors
     */
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ErrorResponseDTO> handleValidationException(ValidationException ex) {
        ErrorResponseDTO errorResponse = new ErrorResponseDTO(ex.getMessage(), ex.getFieldErrors());
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }
}