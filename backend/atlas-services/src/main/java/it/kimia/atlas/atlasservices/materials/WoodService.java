package it.kimia.atlas.atlasservices.materials;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

/**
 * Service interface for managing wood materials.
 */
public interface WoodService {

    /**
     * Get all wood materials with pagination and optional filtering.
     *
     * @param pageable pagination information
     * @return a page of wood materials matching the criteria
     */
    Page<Wood> getWoods(Pageable pageable);

    /**
     * Get a wood material by ID.
     *
     * @param id the wood material ID
     * @return the wood material, if found
     */
    Optional<Wood> getWoodById(String id);

    /**
     * Get a wood material by name.
     *
     * @param name the name of the wood material (e.g., "C14", "D18", "GL20h")
     * @return the wood material, if found
     */
    Optional<Wood> getWoodByName(String name);

}