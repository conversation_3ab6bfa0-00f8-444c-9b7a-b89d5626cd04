package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam;

import it.kimia.atlas.atlasservices.calculationmodule.CalculationModule;
import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.ShearVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.output.FlexuralCalculationResult;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.output.ShearCalculationResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class RectangularBeamModule extends CalculationModule {

    public final static ModuleType moduleType = ModuleType.RECTANGULAR_BEAM;

    private RectangularBeamParams params;

    private FlexuralVerifyExecutionInput flexuralVerifyExecutionInput;
    private FlexuralCalculationResult flexuralCalculationResult;

    private ShearVerifyExecutionInput shearVerifyExecutionInput;
    private ShearCalculationResult shearCalculationResult;


    @Override
    public ModuleType getType() {
        return moduleType;
    }
}