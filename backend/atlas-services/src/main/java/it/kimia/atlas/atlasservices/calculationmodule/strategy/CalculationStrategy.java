package it.kimia.atlas.atlasservices.calculationmodule.strategy;

import it.kimia.atlas.atlasservices.calculationmodule.CalculationModule;
import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;

/**
 * Strategy interface for performing calculations on modules.
 * <p>
 * This interface defines the contract for all calculation strategies.
 * Each strategy is responsible for a specific type of calculation on a specific type of module.
 */
public interface CalculationStrategy<T extends CalculationModule, I> {

    /**
     * Executes the calculation strategy on the given module with the given input.
     *
     * @param module the module to execute the calculation on
     * @param input  the input for the calculation
     * @return the updated module after the calculation
     */
    T execute(T module, I input);

    /**
     * Returns the module type that this strategy can operate on.
     *
     * @return the module type
     */
    ModuleType getModuleType();

    /**
     * Returns the calculation type that this strategy performs.
     *
     * @return the calculation type
     */
    CalculationType getCalculationType();

    /**
     * Returns the class of the input object required by this strategy.
     * This is used for deserialization and type conversion.
     *
     * @return The class of the input type.
     */
    Class<I> getInputType();

    /**
     * Checks if this strategy can execute on the given module.
     *
     * @param module the module to check
     * @return true if this strategy can execute on the given module, false otherwise
     */
    default boolean canExecuteOn(CalculationModule module) {
        return module != null && module.getType() == getModuleType();
    }
}