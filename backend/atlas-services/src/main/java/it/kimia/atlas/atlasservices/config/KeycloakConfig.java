package it.kimia.atlas.atlasservices.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.admin.client.Keycloak;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "keycloak")
@Data
@Slf4j
public class KeycloakConfig {

    private String serverUrl;
    private String realm;
    private String clientId;
    private String username;
    private String password;
    private List<String> roles;

    private volatile Keycloak keycloak;

    public Keycloak getKeycloak() {
        if (keycloak == null) {
            synchronized (this) {
                if (keycloak == null) {
                    keycloak = Keycloak.getInstance(
                            serverUrl,
                            realm,
                            username,
                            password,
                            clientId
                    );
                    log.info("Keycloak admin client initialized for realm '{}'", realm);
                }
            }
        }
        return keycloak;
    }

}
