package it.kimia.atlas.atlasservices.materials;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;

/**
 * Represents a reinforced concrete material in the system.
 */
@Document(collection = "reinforced_concretes")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReinforcedConcrete {

    private static int AVERAGE_COMPRESSIVE_STRENGTH_CONST = 8;
    private static int ELASTIC_MODULUS_CONST = 22000;

    @Id
    private String id;

    /**
     * The name of the reinforced concrete (e.g., "C8/10", "C12/15").
     */
    private String name;

    /**
     * The characteristic cube compressive strength in MPa. (Rck)
     */
    private int cubeCompressiveStrength;

    /**
     * The characteristic cylinder compressive strength in MPa. (fck)
     */
    private int cylinderCompressiveStrength;

    /**
     * The average compressive strength in MPa. (fcm)
     */
    @JsonProperty("averageCompressiveStrength")
    public int getAverageCompressiveStrength() {
        return this.cylinderCompressiveStrength + AVERAGE_COMPRESSIVE_STRENGTH_CONST;
    }

    /**
     * The average tensile strength in MPa. (fctm)
     */
    @JsonProperty("averageTensileStrength")
    public BigDecimal getAverageTensileStrength() {
        var value = BigDecimal.valueOf(0.3 * Math.pow((this.getAverageCompressiveStrength() - 7), 2.0 / 3.0));
        return value.setScale(SCALE, RoundingMode.HALF_UP);
    }

    /**
     * The characteristic elastic modulus in MPa. (Ec)
     */
    @JsonProperty("elasticModulus")
    public BigInteger getElasticModulus() {
        BigDecimal value = BigDecimal.valueOf(ELASTIC_MODULUS_CONST * Math.pow(this.getAverageCompressiveStrength() / 10.0, 0.3));
        return value.toBigInteger();
    }
}