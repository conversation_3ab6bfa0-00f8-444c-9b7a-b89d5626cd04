package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam;

import com.fasterxml.jackson.databind.ObjectMapper;
import it.kimia.atlas.atlasservices.calculationmodule.CalculationModule;
import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import it.kimia.atlas.atlasservices.calculationmodule.ParamApplier;
import it.kimia.atlas.atlasservices.calculationmodule.validator.ValidationException;
import it.kimia.atlas.atlasservices.calculationmodule.validator.model.ValidationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class RectangularBeamParamApplier implements ParamApplier {

    private final RectangularBeamParamsValidator rectangularBeamParamsValidator;
    private final ObjectMapper objectMapper;
    private final RectangularBeamParamsMapper rectangularBeamParamsMapper;

    public ModuleType getModuleType() {
        return ModuleType.RECTANGULAR_BEAM;
    }

    public void apply(CalculationModule calculationModule, Object params) {

        if (calculationModule instanceof RectangularBeamModule rectangularBeamModule) {

            final RectangularBeamParams parsedParams = objectMapper.convertValue(params, RectangularBeamParams.class);

            ValidationResult validationResult = rectangularBeamParamsValidator.validate(parsedParams);
            if (!validationResult.isValid()) {
                throw new ValidationException("Validation Failed", validationResult.getFieldErrors());
            }

            final RectangularBeamParams mergedParams = rectangularBeamParamsMapper.merge(rectangularBeamModule.getParams(), parsedParams);
            rectangularBeamModule.setParams(mergedParams);
            rectangularBeamModule.setFlexuralCalculationResult(null);
            rectangularBeamModule.setShearCalculationResult(null);
        } else {
            throw new IllegalArgumentException("Invalid module type: " + calculationModule.getType());
        }
    }
}
