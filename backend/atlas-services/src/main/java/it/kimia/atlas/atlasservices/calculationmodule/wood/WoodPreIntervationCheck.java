package it.kimia.atlas.atlasservices.calculationmodule.wood;

import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WoodPreIntervationCheck {

    @PositiveOrZero(message = "validation.wood.preIntervationCheck.maximumBendingMoment.positiveOrZero")
    private BigDecimal maximumBendingMoment;

    @PositiveOrZero(message = "validation.wood.preIntervationCheck.maximumShearForce.positiveOrZero")
    private BigDecimal maximumShearForce;

    @PositiveOrZero(message = "validation.wood.preIntervationCheck.designBendingStress.positiveOrZero")
    private BigDecimal designBendingStress;

    @Positive(message = "validation.wood.preIntervationCheck.designBendingStrength.positive")
    private BigDecimal designBendingStrength;

    @PositiveOrZero(message = "validation.wood.preIntervationCheck.bendingCheck.positiveOrZero")
    private BigDecimal bendingCheck;

    @PositiveOrZero(message = "validation.wood.preIntervationCheck.designShearStress.positiveOrZero")
    private BigDecimal designShearStress;

    @Positive(message = "validation.wood.preIntervationCheck.designShearStrength.positive")
    private BigDecimal designShearStrength;

    @PositiveOrZero(message = "validation.wood.preIntervationCheck.shearCheck.positiveOrZero")
    private BigDecimal shearCheck;

    @PositiveOrZero(message = "validation.wood.preIntervationCheck.permanentLoadPerLinearMeter.positiveOrZero")
    private BigDecimal permanentLoadPerLinearMeter;

    @PositiveOrZero(message = "validation.wood.preIntervationCheck.imposedLoadPerLinearMeter.positiveOrZero")
    private BigDecimal imposedLoadPerLinearMeter;

    @PositiveOrZero(message = "validation.wood.preIntervationCheck.instantaneousDeflectionPermanentLoad.positiveOrZero")
    private BigDecimal instantaneousDeflectionPermanentLoad;

    @PositiveOrZero(message = "validation.wood.preIntervationCheck.instantaneousDeflectionImposedLoad.positiveOrZero")
    private BigDecimal instantaneousDeflectionImposedLoad;

    @PositiveOrZero(message = "validation.wood.preIntervationCheck.instantaneousDeflectionTotalLoads.positiveOrZero")
    private BigDecimal instantaneousDeflectionTotalLoads;

    @PositiveOrZero(message = "validation.wood.preIntervationCheck.deformabilityCheck.positiveOrZero")
    private BigDecimal deformabilityCheck;

    @PositiveOrZero(message = "validation.wood.preIntervationCheck.combinationFactor.positiveOrZero")
    private BigDecimal combinationFactor;

    @PositiveOrZero(message = "validation.wood.preIntervationCheck.finalDeflectionTotalLoads.positiveOrZero")
    private BigDecimal finalDeflectionTotalLoads;

    @PositiveOrZero(message = "validation.wood.preIntervationCheck.finalCheckResult.positiveOrZero")
    private BigDecimal finalCheckResult;

}
