package it.kimia.atlas.atlasservices.calculationmodule.wood;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WoodPreIntervationCheck {

    private BigDecimal maximumBendingMoment;

    private BigDecimal maximumShearForce;

    private BigDecimal designBendingStress;

    private BigDecimal designBendingStrength;

    private BigDecimal bendingCheck;

    private BigDecimal designShearStress;

    private BigDecimal designShearStrength;

    private BigDecimal shearCheck;

    private BigDecimal permanentLoadPerLinearMeter;

    private BigDecimal imposedLoadPerLinearMeter;

    private BigDecimal instantaneousDeflectionPermanentLoad;

    private BigDecimal instantaneousDeflectionImposedLoad;

    private BigDecimal instantaneousDeflectionTotalLoads;

    private BigDecimal deformabilityCheck;

    private BigDecimal combinationFactor;

    private BigDecimal finalDeflectionTotalLoads;

    private BigDecimal finalCheckResult;

}
