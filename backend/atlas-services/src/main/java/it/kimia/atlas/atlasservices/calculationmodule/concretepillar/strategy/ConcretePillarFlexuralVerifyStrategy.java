package it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy;

import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.ConcretePillarModule;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.ConcretePillarParams;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input.ConcretePillarFlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input.ConcretePillarFlexuralVerifyInput;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.mappers.ConcretePillarFlexuralVerifyExecutionInputMapper;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.output.ConcretePillarFlexuralCalculationResult;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.strategy.ConcretePillarFlexuralVerify;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationStrategy;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationType;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.StrategyInputValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Strategy implementation for FLEXURAL_VERIFY calculations on TBeamModule.
 * <p>
 * This strategy performs flexural verification calculations on Pillar beam modules.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ConcretePillarFlexuralVerifyStrategy implements CalculationStrategy<ConcretePillarModule, ConcretePillarFlexuralVerifyInput> {

    private final StrategyInputValidator<ConcretePillarFlexuralVerifyInput> concretePillarFlexuralInputValidatorStrategy;
    private final ConcretePillarFlexuralVerifyExecutionInputMapper flexuralVerifyExecutionInputMapper;
    private final ConcretePillarFlexuralVerify concretePillarFlexuralVerify;

    @Override
    public Class<ConcretePillarFlexuralVerifyInput> getInputType() {
        return ConcretePillarFlexuralVerifyInput.class;
    }

    @Override
    public ConcretePillarModule execute(ConcretePillarModule module, ConcretePillarFlexuralVerifyInput input) {

        // Get module parameters
        final ConcretePillarParams params = module.getParams();
        if (params == null) {
            throw new IllegalStateException("Module parameters are not set");
        }

        final ConcretePillarFlexuralVerifyInput verifiedInput = concretePillarFlexuralInputValidatorStrategy.validate(input);
        
        final ConcretePillarFlexuralVerifyExecutionInput flexuralVerifyExecutionInput =
                flexuralVerifyExecutionInputMapper.map(verifiedInput);

        final ConcretePillarFlexuralCalculationResult result = concretePillarFlexuralVerify.execute(params, flexuralVerifyExecutionInput);

        module.setFlexuralVerifyExecutionInput(flexuralVerifyExecutionInput);
        module.setFlexuralCalculationResult(result);

        return module;
    }


    @Override
    public ModuleType getModuleType() {
        return ModuleType.PILLAR;
    }

    @Override
    public CalculationType getCalculationType() {
        return CalculationType.FLEXURAL_VERIFY;
    }

}