package it.kimia.atlas.atlasservices.project;

import it.kimia.atlas.atlasservices.calculationmodule.CalculationModule;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Represents a project in the system.
 */
@Document(collection = "projects")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Project {

    private String address;
    private Currency baseTenderCurrency;
    private BigDecimal baseTenderAmount;
    private String company;
    private String constructionSiteName;
    private LocalDateTime createdAt;
    @Id
    private String id;
    private LocalDateTime lastModified;
    private String machiningSurfaceSize;
    @Builder.Default
    private List<CalculationModule> modules = List.of();
    private String plannedWorkDescription;
    private String planner;
    private String processingType;
    private String userId;

}
