package it.kimia.atlas.atlasservices.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.OAuthFlow;
import io.swagger.v3.oas.models.security.OAuthFlows;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for OpenAPI/Swagger documentation.
 */
@Configuration
public class OpenApiConfig {

    @Value("${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String issuerUri;

    @Value("${spring.application.name}")
    private String applicationName;

    /**
     * Configures the OpenAPI documentation for the application.
     *
     * @return the OpenAPI configuration
     */
    @Bean
    public OpenAPI openAPI() {
        final String securitySchemeName = "oauth2";
        
        return new OpenAPI()
                .info(new Info()
                        .title("Atlas Services API")
                        .description("REST API for Atlas Services")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Kimia")
                                .url("https://www.kimia.it")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("Proprietary")
                                .url("https://www.kimia.it")))
                .addSecurityItem(new SecurityRequirement().addList(securitySchemeName))
                .components(new Components()
                        .addSecuritySchemes(securitySchemeName, new SecurityScheme()
                                .type(SecurityScheme.Type.OAUTH2)
                                .description("OAuth2 authentication with Keycloak")
                                .flows(new OAuthFlows()
                                        .implicit(new OAuthFlow()
                                                .authorizationUrl(issuerUri + "/protocol/openid-connect/auth")
                                                .refreshUrl(issuerUri + "/protocol/openid-connect/token")
                                                .tokenUrl(issuerUri + "/protocol/openid-connect/token")))));
    }
}