package it.kimia.atlas.atlasservices.calculationmodule.utility;

import org.apache.commons.math3.analysis.UnivariateFunction;
import org.apache.commons.math3.analysis.solvers.BrentSolver;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.function.Function;

public final class BrentRootFinder {

    private BrentRootFinder() {
    }

    /**
     * Trova la radice con Brent partendo da un intervallo [lower, upper] con cambio di segno.
     * Rivaluta eval(rootBD) (BigDecimal) prima di ritornare, per allinearsi alla legacy.
     */
    public static <T> T solve(
            BigDecimal lower,
            BigDecimal upper,
            Options opt,
            Function<BigDecimal, T> eval,
            Function<T, BigDecimal> residualGetter
    ) {
        Objects.requireNonNull(lower);
        Objects.requireNonNull(upper);
        Objects.requireNonNull(opt);
        Objects.requireNonNull(eval);
        Objects.requireNonNull(residualGetter);

        // Controllo cambio di segno sui capi (con BigDecimal)
        T lRes = eval.apply(lower);
        BigDecimal fLower = residualGetter.apply(lRes);
        if (fLower.signum() == 0) return eval.apply(lower);

        T uRes = eval.apply(upper);
        BigDecimal fUpper = residualGetter.apply(uRes);
        if (fUpper.signum() == 0) return eval.apply(upper);

        if (fLower.multiply(fUpper).signum() > 0) {
            throw new IllegalStateException("Brent: nessun cambio di segno su [" + lower + ", " + upper + "].");
        }

        // Adattatore a double per Apache Commons
        UnivariateFunction f = (double x) -> {
            T r = eval.apply(BigDecimal.valueOf(x));
            return residualGetter.apply(r).doubleValue();
        };

        // BrentSolver con le tolleranze specificate
        BrentSolver solver = new BrentSolver(opt.absTolerance, opt.relTolerance, opt.fTolerance);

        // solve richiede maxEval, funzione, bounds (double)
        double root = solver.solve(opt.maxEvaluations, f, lower.doubleValue(), upper.doubleValue());

        // Riporta la radice a BigDecimal e rivaluta con BigDecimal (come legacy)
        BigDecimal rootBD = BigDecimal.valueOf(root);
        return eval.apply(rootBD);
    }

    public static final class Options {
        /**
         * tolleranza assoluta sulla x
         */
        public final double absTolerance;          // es. 1e-12
        /**
         * tolleranza relativa sulla x
         */
        public final double relTolerance;          // es. 1e-12 o 1e-9
        /**
         * tolleranza sul valore di funzione f(x)
         */
        public final double fTolerance;            // es. 1e-12
        /**
         * massimo numero di valutazioni funzione
         */
        public final int maxEvaluations;           // es. 1000

        public Options(double absTolerance, double relTolerance, double fTolerance, int maxEvaluations) {
            this.absTolerance = absTolerance;
            this.relTolerance = relTolerance;
            this.fTolerance = fTolerance;
            this.maxEvaluations = maxEvaluations;
        }

        public static Options strictLegacy() {
            return new Options(1e-12, 1e-12, 1e-12, 1000);
        }
    }
}