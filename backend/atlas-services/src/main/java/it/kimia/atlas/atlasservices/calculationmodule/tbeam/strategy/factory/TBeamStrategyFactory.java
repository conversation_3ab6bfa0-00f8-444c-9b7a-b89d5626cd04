package it.kimia.atlas.atlasservices.calculationmodule.tbeam.strategy.factory;

import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationStrategy;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationType;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.factory.CalculationStrategyFactory;
import it.kimia.atlas.atlasservices.calculationmodule.tbeam.TBeamModule;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class TBeamStrategyFactory implements CalculationStrategyFactory<TBeamModule> {

    private final Map<CalculationType, CalculationStrategy<TBeamModule, ?>> strategies;

    public TBeamStrategyFactory(List<CalculationStrategy<TBeamModule, ?>> rectangularBeamStrategies) {
        this.strategies = rectangularBeamStrategies.stream()
                .collect(Collectors.toMap(CalculationStrategy::getCalculationType, strategy -> strategy));
    }

    @Override
    public Optional<CalculationStrategy<TBeamModule, ?>> createStrategy(CalculationType calculationType) {
        return Optional.ofNullable(strategies.get(calculationType));
    }

    @Override
    public ModuleType getModuleType() {
        return ModuleType.T_BEAM;
    }
}