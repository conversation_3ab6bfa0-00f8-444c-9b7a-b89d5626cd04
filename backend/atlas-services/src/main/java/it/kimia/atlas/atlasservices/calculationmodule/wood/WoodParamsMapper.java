package it.kimia.atlas.atlasservices.calculationmodule.wood;

import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * Mapper for WoodParams objects.
 * <p>
 * This mapper handles merging of WoodParams objects, allowing for partial updates
 * where only non-null values from the source are applied to the target.
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface WoodParamsMapper {

    /**
     * Merges two WoodParams instances.
     * - If 'existing' is null, creates a complete copy of 'updates'.
     * - If 'existing' is not null, updates 'existing' with non-null values from 'updates'.
     *
     * @param existing The object to update (can be null).
     * @param updates  The object with new data.
     * @return The merged object.
     */
    default WoodParams merge(WoodParams existing, WoodParams updates) {
        if (updates == null) {
            return existing;
        }
        if (existing == null) {
            return clone(updates);
        }
        return update(existing, updates);
    }

    /**
     * Creates a deep copy of the source WoodParams.
     *
     * @param source The source object to clone.
     * @return A deep copy of the source object.
     */
    WoodParams clone(WoodParams source);

    /**
     * Updates the target WoodParams with non-null values from the source.
     *
     * @param target The target object to update.
     * @param source The source object with new values.
     * @return The updated target object.
     */
    WoodParams update(@MappingTarget WoodParams target, WoodParams source);

    /**
     * Creates a deep copy of WoodGeometry.
     *
     * @param source The source geometry to clone.
     * @return A deep copy of the source geometry.
     */
    WoodGeometry clone(WoodGeometry source);

    /**
     * Updates the target WoodGeometry with non-null values from the source.
     *
     * @param target The target geometry to update.
     * @param source The source geometry with new values.
     * @return The updated target geometry.
     */
    WoodGeometry update(@MappingTarget WoodGeometry target, WoodGeometry source);

    /**
     * Creates a deep copy of WoodProperties.
     *
     * @param source The source properties to clone.
     * @return A deep copy of the source properties.
     */
    WoodProperties clone(WoodProperties source);

    /**
     * Updates the target WoodProperties with non-null values from the source.
     *
     * @param target The target properties to update.
     * @param source The source properties with new values.
     * @return The updated target properties.
     */
    WoodProperties update(@MappingTarget WoodProperties target, WoodProperties source);

    /**
     * Creates a deep copy of WoodPreIntervationCheck.
     *
     * @param source The source check to clone.
     * @return A deep copy of the source check.
     */
    WoodPreIntervationCheck clone(WoodPreIntervationCheck source);

    /**
     * Updates the target WoodPreIntervationCheck with non-null values from the source.
     *
     * @param target The target check to update.
     * @param source The source check with new values.
     * @return The updated target check.
     */
    WoodPreIntervationCheck update(@MappingTarget WoodPreIntervationCheck target, WoodPreIntervationCheck source);
}
