package it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input;

import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProduct;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;

@Getter
@AllArgsConstructor
public class ConcretePillarFlexuralVerifyExecutionInputImpl implements ConcretePillarFlexuralVerifyExecutionInput {
    private final BigDecimal appliedAxialForce;
    private final int stripWidth;
    private final int layersNumber;
    private final BigDecimal bendingMoment;
    private final ReinforcedConcreteProduct product;
}

