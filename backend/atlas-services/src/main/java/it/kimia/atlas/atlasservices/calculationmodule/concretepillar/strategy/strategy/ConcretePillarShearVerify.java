package it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.strategy;

import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.ConcretePillarParams;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input.ConcretePillarShearVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.output.ConcretePillarShearCalculationResult;

public interface ConcretePillarShearVerify {
    ConcretePillarShearCalculationResult execute(final ConcretePillarParams params,
                                                 final ConcretePillarShearVerifyExecutionInput input);
}
