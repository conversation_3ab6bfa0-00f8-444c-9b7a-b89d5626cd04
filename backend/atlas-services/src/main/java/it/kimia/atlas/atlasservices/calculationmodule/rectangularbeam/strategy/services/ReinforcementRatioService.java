package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamParams;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInput;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;


@Service
@RequiredArgsConstructor
public class ReinforcementRatioService {

    private final ConcreteCoverService concreteCoverService;
    private final MaterialStrengthService materialStrengthService;
    private final FrpUtilsService frpUtilsService;

    // C68 μs [-] Tensile steel mechanical ratio [eq. 13.10 CNR DT R1]
    // =C11*C24/(C17*C6*C10)
    public BigDecimal calculateTensileSteelMechanicalRatio(final RectangularBeamParams params) {
        // As1 area di armatura tesa (dipende dalla polarità)
        final BigDecimal bottomReinforcementArea = concreteCoverService.getTensileSteelArea(params);
        //C24
        final BigDecimal designYieldStrengthForDuctileMechanisms = materialStrengthService.getDesignYieldStrengthForDuctileMechanisms(params.getMaterialProperties());
        //C17
        final BigDecimal designCompressiveStrengthForDuctileMechanisms = materialStrengthService.getDesignCompressiveStrengthForDuctileMechanisms(params.getMaterialProperties());
        //C6
        final BigDecimal width = params.getGeometry().getWidth();
        //C10
        final BigDecimal effectiveDepth = params.getGeometry().getEffectiveDepth();

        final BigDecimal numerator = bottomReinforcementArea.multiply(designYieldStrengthForDuctileMechanisms);
        final BigDecimal denominator = designCompressiveStrengthForDuctileMechanisms.multiply(width).multiply(effectiveDepth);
        return numerator.divide(denominator, SCALE, RoundingMode.HALF_UP);
    }

    // C66 μf1-2 [-] Limit mechanical reinforcement ratio [eq. 13.9 CNR DT R1]
    // =(0.8*C21*C7/C10)/(C21+C25+C2)-C68*(1-C12/C11)
    public BigDecimal calculateLimitMechanicalReinforcementRatio(final RectangularBeamParams params) {
        // C11 As1 (tesa), C12 As2 (compressa) -> dipende dalla polarità
        final BigDecimal tensileArea = concreteCoverService.getTensileSteelArea(params);      // C11
        final BigDecimal compressiveArea = concreteCoverService.getCompressiveSteelArea(params); // C12
        final BigDecimal sectionHeight = params.getGeometry().getHeight();                    // C7
        final BigDecimal effectiveDepth = params.getGeometry().getEffectiveDepth();           // C10
        final BigDecimal initialDeformation = params.getInitialDeformation();                 // C2
        //C24
        final BigDecimal designYieldStrengthForDuctileMechanisms = materialStrengthService.getDesignYieldStrengthForDuctileMechanisms(params.getMaterialProperties());

        final BigDecimal concreteUltimateStrain = RectangularBeamFlexuralVerifyService.CONCRETE_ULTIMATE_STRAIN; // C21

        // C25
        final BigDecimal steelYieldStrain = designYieldStrengthForDuctileMechanisms.divide(
                BigDecimal.valueOf(params.getMaterialProperties().getSteelGrade().getElasticModulus()), SCALE, RoundingMode.HALF_UP);

        final BigDecimal tensileSteelMechanicalRatio = calculateTensileSteelMechanicalRatio(params); // C68

        // Primo termine
        final BigDecimal firstTermNumerator = BigDecimal.valueOf(0.8).multiply(concreteUltimateStrain).multiply(sectionHeight);
        final BigDecimal firstTermDenominator = effectiveDepth.multiply(concreteUltimateStrain.add(steelYieldStrain).add(initialDeformation));
        final BigDecimal firstTerm = firstTermNumerator.divide(firstTermDenominator, SCALE, RoundingMode.HALF_UP);

        // Secondo termine
        BigDecimal secondTerm = BigDecimal.ZERO;
        if (tensileArea.compareTo(BigDecimal.ZERO) != 0) {
            secondTerm = tensileSteelMechanicalRatio.multiply(
                    BigDecimal.ONE.subtract(compressiveArea.divide(tensileArea, SCALE, RoundingMode.HALF_UP))
            ).setScale(SCALE, RoundingMode.HALF_UP);
        }

        return firstTerm.subtract(secondTerm).setScale(SCALE, RoundingMode.HALF_UP);
    }


    // C67 μf [-] Mechanical reinforcement ratio [eq. 13.8 CNR DT R1]
    // =(C35*(C34*C36)*C59)/(C17*C6*C10)
    public BigDecimal calculateMechanicalReinforcementRatio(final RectangularBeamParams params,
                                                            final FlexuralVerifyExecutionInput input,
                                                            final BigDecimal maxStress) {
        final BigDecimal frpArea = frpUtilsService.computeFrpArea(input);
        final BigDecimal fcd = materialStrengthService.getDesignCompressiveStrengthForDuctileMechanisms(params.getMaterialProperties());      // C17
        final BigDecimal width = params.getGeometry().getWidth();                            // C6
        final BigDecimal d = params.getGeometry().getEffectiveDepth();                       // C10

        return frpArea.multiply(maxStress)
                .divide(fcd.multiply(width).multiply(d), SCALE, RoundingMode.HALF_UP);
    }
}