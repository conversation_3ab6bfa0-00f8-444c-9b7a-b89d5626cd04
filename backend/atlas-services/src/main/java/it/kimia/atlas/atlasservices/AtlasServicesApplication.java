package it.kimia.atlas.atlasservices;

import org.apache.coyote.ProtocolHandler;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.boot.tomcat.servlet.TomcatServletWebServerFactory;
import org.springframework.context.annotation.Bean;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Main application class for Atlas Services.
 * <p>
 * This application is structured with the following components:
 * - Project: Manages project information
 * - User: Manages user information
 * - Security: Handles authentication and authorization
 * - Shared: Contains shared utilities and models
 */
@SpringBootApplication
@ConfigurationPropertiesScan
public class AtlasServicesApplication {

    public static void main(String[] args) {
        SpringApplication.run(AtlasServicesApplication.class, args);
    }

    /**
     * Configure Tomcat to use virtual threads for improved performance.
     */
    @Bean
    public TomcatServletWebServerFactory tomcatFactory() {
        ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();
        TomcatServletWebServerFactory factory = new TomcatServletWebServerFactory();
        factory.addConnectorCustomizers(connector -> {
            ProtocolHandler handler = connector.getProtocolHandler();
            try {
                handler.getClass().getMethod("setExecutor", java.util.concurrent.Executor.class)
                        .invoke(handler, executor);
            } catch (Exception ignored) {
            }
        });
        return factory;
    }

}
