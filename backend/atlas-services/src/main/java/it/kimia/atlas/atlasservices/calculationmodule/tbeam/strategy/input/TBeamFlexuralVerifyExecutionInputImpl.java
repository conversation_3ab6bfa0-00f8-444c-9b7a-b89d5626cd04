package it.kimia.atlas.atlasservices.calculationmodule.tbeam.strategy.input;

import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProduct;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class TBeamFlexuralVerifyExecutionInputImpl implements TBeamFlexuralVerifyExecutionInput {
    private final int stripWidth;
    private final int layersNumber;
    private final int bendingMoment;
    private final ReinforcedConcreteProduct product;
}

