package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProduct;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamParams;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.output.FlexuralCalculationResult;
import it.kimia.atlas.atlasservices.calculationmodule.utility.BisectionSolver;
import it.kimia.atlas.atlasservices.calculationmodule.utility.BrentRootFinder;
import it.kimia.atlas.atlasservices.calculationmodule.utility.RootFinder;
import it.kimia.atlas.atlasservices.product.FiberType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;

@Service
@RequiredArgsConstructor
@Slf4j
public class RectangularBeamFlexuralVerifyService implements RectangularBeamFlexuralVerify {

    // C20 ε_c0: Concrete strain at peak stress (parabolic part)
    public static final BigDecimal CONCRETE_PEAK_STRAIN = BigDecimal.valueOf(0.00175);
    // C21 ε_cu: Ultimate compressive strain in concrete
    public static final BigDecimal CONCRETE_ULTIMATE_STRAIN = BigDecimal.valueOf(0.00350);

    private final ConcreteCoverService concreteCoverService;
    private final EnvironmentalAndSafetyFactorService environmentalAndSafetyFactorService;
    private final FailureModeService failureModeService;
    private final FrpUtilsService frpUtilsService;
    private final GeometricCoefficientService geometricCoefficientService;
    private final MaterialStrengthService materialStrengthService;
    private final RootFinder rootFinder;

    public FlexuralCalculationResult execute(final RectangularBeamParams params,
                                             final FlexuralVerifyExecutionInput input) {
        final FlexuralCalculationResult init = FlexuralCalculationResult.builder().build();
        final BigDecimal firstExperimentalData = input.getProduct().getElasticModulus();
        // Msd (kNm) come in Excel: confrontiamo il valore in modulo (magnitudine), indipendente dal segno/polarità
        final BigDecimal msd = BigDecimal.valueOf(Math.abs((long) input.getBendingMoment()));
        final FlexuralCalculationResult withPartialBendingFactor = this.calculatePartialBendingFactor(init);
        // C83 Mrd: Design moment capacity of the section (kNm)
        FlexuralCalculationResult result = this.calculateMomentCapacity(params, input, withPartialBendingFactor);
        result = this.calculateOptimalDesignAnchorageLength(params, input, result);
        final boolean checkResult = result.getMomentCapacity().compareTo(msd) > 0;
        return result.toBuilder()
                .checkResult(checkResult)
                .firstExperimentalData(firstExperimentalData)
                .build();
    }

    // C83 Mrd [kNm] - Momento resistente [form 4.57]
    protected FlexuralCalculationResult calculateMomentCapacity(final RectangularBeamParams params,
                                                                final FlexuralVerifyExecutionInput input,
                                                                FlexuralCalculationResult previous) {
        // ===== Geometria e grandezze di base =====
        final var geometry = params.getGeometry();

        // C6  = b (larghezza sezione)
        final BigDecimal width = geometry.getWidth();
        // C10 = d (altezza utile)
        final BigDecimal effectiveDepth = geometry.getEffectiveDepth();
        // Copriferro al lembo compresso (C8 sup per M+, C9 inf per M-)
        final BigDecimal compressedCover = concreteCoverService.getCompressedCover(params);

        // C69 = x (posizione asse neutro dal lembo compresso)
        previous = calculateNeutralAxisPosition(params, input, previous);
        final BigDecimal neutralAxis = previous.getNeutralAxisPosition();

        // C71 = ψ (coefficiente adimensionale della risultante in cls)
        previous = calculateFirstAdimensionalCoefficient(params, input, neutralAxis, previous);
        final BigDecimal psi = previous.getFirstAdimensionalCoefficient();

        // C72 = λ (posizione risultante compressione in cls)
        previous = calculateSecondDimensionlessCoefficient(params, input, neutralAxis, previous);
        final BigDecimal lambda = previous.getSecondAdimensionalCoefficient();

        // C17 = fcd,d (resistenza di progetto a compressione del cls - meccanismi duttili)
        final BigDecimal fcd = getDesignCompressiveStrengthForDuctileMechanisms(params);

        // ===== Contributo calcestruzzo =====
        // Forza di compressione nel calcestruzzo: ψ·b·x·fcd  = C71*C6*C69*C17
        final BigDecimal concreteCompression = psi
                .multiply(width)
                .multiply(neutralAxis)
                .multiply(fcd);

        // Braccio della risultante nel cls: (d − λ·x) = (C10 − C72*C69)
        final BigDecimal concreteLeverArm = effectiveDepth.subtract(lambda.multiply(neutralAxis));

        // Momento del calcestruzzo (N·mm)
        final BigDecimal mConcrete = concreteCompression.multiply(concreteLeverArm);

        // ===== Contributo acciaio compresso (As2) =====
        // C12 = As2 (area armatura compressa, superiore o inferiore a seconda della polarità)
        final BigDecimal compressiveSteelArea = concreteCoverService.getCompressiveSteelArea(params);
        // C81 = σs2 (tensione acciaio compresso)
        previous = calculateSteelCompressedTension(params, input, neutralAxis, previous);
        final BigDecimal compressiveSteelStress = previous.getSteelCompressedTension();

        // Braccio comune rispetto al lembo compresso: (d − cover_compresso)
        final BigDecimal leverArmTop = effectiveDepth.subtract(compressedCover);

        // Momento acciaio compresso (N·mm) = C12*C81*(C10 − cover_compresso)
        final BigDecimal mSteel = compressiveSteelArea.multiply(compressiveSteelStress).multiply(leverArmTop);

        // ===== Contributo FRP =====
        final ReinforcedConcreteProduct product = input.getProduct();
        // Area FRP = tf·bf·nf = C34*C35*C36
        final BigDecimal frpArea = computeFrpArea(input);
        // C82 = σf (tensione nel FRP)
        previous = calculateFrpStress(params, input, neutralAxis, previous);
        final BigDecimal frpStress = previous.getFrpStress();

        // Momento FRP (N·mm) = C34*C35*C36*C82*(C10 − cover_compresso)
        final BigDecimal mFrp = frpArea.multiply(frpStress).multiply(compressedCover);

        // ===== Somma dei contributi =====
        // Formula Excel Mrd (N·mm):
        // = C71*C6*C69*C17*(C10 - C72*C69)   // calcestruzzo
        // + C12*C81*(C10 - cover_compresso)  // acciaio compresso (C9 per M+, C8 per M-)
        // + C34*C35*C36*C82*C8 // FRP (C9 per M+, C8 per M-)
        final BigDecimal mrdNmm = mConcrete.add(mSteel).add(mFrp);

        // Conversione N·mm → kN·m : ÷1_000_000
        final BigDecimal momentCapacity = mrdNmm.divide(BigDecimal.valueOf(1_000_000), SCALE, RoundingMode.HALF_UP);

        return previous.toBuilder().momentCapacity(momentCapacity).build();
    }


    /**
     * C69
     *
     */
    protected FlexuralCalculationResult calculateNeutralAxisPositionEnhanced(final RectangularBeamParams params,
                                                                             final FlexuralVerifyExecutionInput input,
                                                                             final FlexuralCalculationResult previous) {
        BigDecimal lower = concreteCoverService.getCompressedCover(params);
        BigDecimal upper = params.getGeometry().getEffectiveDepth();
        BigDecimal tol = new BigDecimal("1e-12");
        int maxIter = 1000;

        return BisectionSolver.solveByBisection(
                lower,
                upper,
                tol,
                maxIter,
                x -> equilibrium(params, input, x, previous),
                FlexuralCalculationResult::getEquilibrium
        );
    }

    /**
     * C69
     *
     */
    protected FlexuralCalculationResult calculateNeutralAxisPositionBrent(
            final RectangularBeamParams params,
            final FlexuralVerifyExecutionInput input,
            final FlexuralCalculationResult previous
    ) {
        BigDecimal lower = concreteCoverService.getCompressedCover(params);
        BigDecimal upper = params.getGeometry().getEffectiveDepth();

        BrentRootFinder.Options opt = BrentRootFinder.Options.strictLegacy();

        return BrentRootFinder.solve(
                lower, upper, opt,
                x -> equilibrium(params, input, x, previous),
                FlexuralCalculationResult::getEquilibrium
        );
    }

    /**
     * C69
     */
    protected FlexuralCalculationResult calculateNeutralAxisPosition(
            final RectangularBeamParams params,
            final FlexuralVerifyExecutionInput input,
            final FlexuralCalculationResult previous
    ) {
        BigDecimal lower = concreteCoverService.getCompressedCover(params);
        BigDecimal upper = params.getGeometry().getEffectiveDepth();

        return rootFinder.solve(
                lower, upper,
                x -> equilibrium(params, input, x, previous),
                FlexuralCalculationResult::getEquilibrium
        );
    }

    //C77 εs2 [-]	Deformazione acciaio compresso
    // =SE(C65=1,(C64+C2)*(C69-C9)/(C7-C69),C21*(C69-C9)/C69)
    protected FlexuralCalculationResult calculateCompressedSteelStrain(
            final RectangularBeamParams params,
            final FlexuralVerifyExecutionInput input,
            // C69
            final BigDecimal neutralAxisPosition,
            final FlexuralCalculationResult previous
    ) {
        // C65
        final FlexuralCalculationResult resultWithFailureMode = this.getFailureMode(params, input, previous);
        var failureMode = resultWithFailureMode.getFailureMode();
        // Copriferro al lembo compresso (C9 per M+, C8 per M-)
        final BigDecimal compressedCover = concreteCoverService.getCompressedCover(params);

        if (failureMode == 1) {
            // C64
            final FlexuralCalculationResult resultWithMaxDesignReinforcementStrain = getMaxDesignReinforcementStrain(params, input, resultWithFailureMode);
            final BigDecimal maxDesignReinforcementStrain = resultWithMaxDesignReinforcementStrain.getMaxDesignReinforcementStrain();
            // C2
            final BigDecimal initialDeformation = params.getInitialDeformation();

            // C7
            final BigDecimal sectionHeight = params.getGeometry().getHeight();

            //(C64+C2)*(C69-cover_compresso)/(C7-C69)
            final BigDecimal compressedSteelStrain = (maxDesignReinforcementStrain.add(initialDeformation))
                    .multiply(neutralAxisPosition.subtract(compressedCover))
                    .divide(sectionHeight.subtract(neutralAxisPosition), SCALE, RoundingMode.HALF_UP);
            return resultWithMaxDesignReinforcementStrain.toBuilder().compressedSteelStrain(compressedSteelStrain).build();
        } else {
            //C21*(C69-cover_compresso)/C69
            final BigDecimal compressedSteelStrain = (CONCRETE_ULTIMATE_STRAIN.multiply(neutralAxisPosition.subtract(compressedCover)))
                    .divide(neutralAxisPosition, SCALE, RoundingMode.HALF_UP);
            return resultWithFailureMode.toBuilder().compressedSteelStrain(compressedSteelStrain).build();
        }
    }

    // C81 σs2 [MPa]	Tensione acciaio compresso =SE(C77*C26<C24,C77*C26,C24)
    protected FlexuralCalculationResult calculateSteelCompressedTension(final RectangularBeamParams params,
                                                                        final FlexuralVerifyExecutionInput input,
                                                                        final BigDecimal neutralAxisPosition,
                                                                        final FlexuralCalculationResult previous) {
        // C77
        final FlexuralCalculationResult resultWithCompressedSteelStrain = this.calculateCompressedSteelStrain(
                params,
                input,
                neutralAxisPosition,
                previous
        );
        final BigDecimal compressedSteelStrain = resultWithCompressedSteelStrain.getCompressedSteelStrain();
        // C26
        final BigDecimal steelElasticModulus =
                BigDecimal.valueOf(params.getMaterialProperties().getSteelGrade().getElasticModulus());
        // C24
        final BigDecimal designYieldStrengthForDuctileMechanisms = getDesignYieldStrengthForDuctileMechanisms(params);

        final BigDecimal steelCompressedTension;
        if (compressedSteelStrain.multiply(steelElasticModulus).compareTo(designYieldStrengthForDuctileMechanisms) < 0) {
            steelCompressedTension = compressedSteelStrain.multiply(steelElasticModulus);
        } else {
            steelCompressedTension = designYieldStrengthForDuctileMechanisms;
        }
        return resultWithCompressedSteelStrain.toBuilder().steelCompressedTension(steelCompressedTension).build();
    }

    //C76 εs1 [-]	Deformazione acciaio teso
    protected FlexuralCalculationResult calculateTensileSteelStrain(final RectangularBeamParams params,
                                                                    final FlexuralVerifyExecutionInput input,
                                                                    final BigDecimal neutralAxisPosition,
                                                                    FlexuralCalculationResult previous) {
        previous = this.getFailureMode(params, input, previous);
        final int failureMode = previous.getFailureMode();
        // C10
        final BigDecimal effectiveDepth = params.getGeometry().getEffectiveDepth();
        // C7
        final BigDecimal sectionHeight = params.getGeometry().getHeight();

        //C64  εfd  e deformazione iniziale ε0 (C2)
        previous = getMaxDesignReinforcementStrain(params, input, previous);
        final BigDecimal maxDesignReinforcementStrain = previous.getMaxDesignReinforcementStrain();
        // C2
        final BigDecimal initialDeformation = params.getInitialDeformation();


        final BigDecimal tensileSteelStrain;
        if (failureMode == 1) {
            // (C64 + C2)·(C10 − C69)/(C7 − C69)
            tensileSteelStrain = (maxDesignReinforcementStrain.add(initialDeformation))
                    .multiply(effectiveDepth.subtract(neutralAxisPosition))
                    .divide(sectionHeight.subtract(neutralAxisPosition), SCALE, RoundingMode.HALF_UP);
        } else {
            // εcu·(C10 − C69)/C69
            tensileSteelStrain = CONCRETE_ULTIMATE_STRAIN
                    .multiply(effectiveDepth.subtract(neutralAxisPosition))
                    .divide(neutralAxisPosition, SCALE, RoundingMode.HALF_UP);
        }

        return previous.toBuilder().tensileSteelStrain(tensileSteelStrain).build();
    }

    // C80 σs1 [MPa] Tensione acciaio teso
    protected FlexuralCalculationResult calculateSteelTensileStress(final RectangularBeamParams params,
                                                                    final FlexuralVerifyExecutionInput input,
                                                                    final BigDecimal neutralAxisPosition,
                                                                    FlexuralCalculationResult previous) {
        // C76
        previous = this.calculateTensileSteelStrain(params, input, neutralAxisPosition, previous);
        final BigDecimal tensileStrain = previous.getTensileSteelStrain();

        // C26 modulo elastico dell’acciaio
        final BigDecimal steelElasticModulus = BigDecimal.valueOf(
                params.getMaterialProperties().getSteelGrade().getElasticModulus()
        );
        // σs1 provvisorio
        final BigDecimal stressCandidate = tensileStrain.multiply(steelElasticModulus);
        // C24 fyd,d
        final BigDecimal designYieldStrength = getDesignYieldStrengthForDuctileMechanisms(params);

        // σs1 = min(εs1·Es, fyd,d)
        final BigDecimal tensileSteelStress = stressCandidate.compareTo(designYieldStrength) < 0
                ? stressCandidate
                : designYieldStrength;
        return previous.toBuilder().tensileSteelStress(tensileSteelStress).build();
    }

    //C78 εf [-]	Deformazione rinforzo FRP
    // =SE(C65=1,C64,C21/C69*(C7-C69)-C2)
    protected FlexuralCalculationResult getShearReinforcedFRP(final RectangularBeamParams params,
                                                              final FlexuralVerifyExecutionInput input,
                                                              final BigDecimal neutralAxisPosition,
                                                              FlexuralCalculationResult previous) {
        final BigDecimal shearReinforcedFRP;
        // C65
        previous = this.getFailureMode(params, input, previous);
        final int failureMode = previous.getFailureMode();
        if (failureMode == 1) {
            //C64  εfd  e deformazione iniziale ε0 (C2)
            previous = getMaxDesignReinforcementStrain(params, input, previous);
            shearReinforcedFRP = previous.getMaxDesignReinforcementStrain();
        } else {
            // C7
            final BigDecimal sectionHeight = params.getGeometry().getHeight();
            // C2
            final BigDecimal initialDeformation = params.getInitialDeformation();

            shearReinforcedFRP = CONCRETE_ULTIMATE_STRAIN.divide(neutralAxisPosition, SCALE, RoundingMode.HALF_UP)
                    .multiply(sectionHeight.subtract(neutralAxisPosition)).subtract(initialDeformation);
        }
        return previous.toBuilder().frpReinforcementStrain(shearReinforcedFRP).build();
    }

    // C82 σf [MPa] Tensione nel rinforzo FRP
    // =C78*C40
    protected FlexuralCalculationResult calculateFrpStress(final RectangularBeamParams params,
                                                           final FlexuralVerifyExecutionInput input,
                                                           final BigDecimal neutralAxisPosition,
                                                           final FlexuralCalculationResult previous) {

        //C78 εf [-]	Deformazione rinforzo FRP
        final FlexuralCalculationResult resultWithFRP = getShearReinforcedFRP(params, input, neutralAxisPosition, previous);
        final BigDecimal shearReinforcedFRP = resultWithFRP.getFrpReinforcementStrain();

        final ReinforcedConcreteProduct product = input.getProduct();

        // C40
        final BigDecimal elasticModulus = product.getElasticModulus();

        final BigDecimal frpStress = shearReinforcedFRP.multiply(elasticModulus);
        return resultWithFRP.toBuilder().frpStress(frpStress).build();
    }

    // =C71*C6*C69*C17+C12*C81-C11*C80-C82*C35*C34*C36
    protected FlexuralCalculationResult equilibrium(final RectangularBeamParams params,
                                                    final FlexuralVerifyExecutionInput input,
                                                    final BigDecimal neutralAxisPosition,
                                                    FlexuralCalculationResult previous) {
        //C71
        previous = calculateFirstAdimensionalCoefficient(
                params,
                input,
                neutralAxisPosition,
                previous
        );
        final BigDecimal firstDimensionlessCoefficient = previous.getFirstAdimensionalCoefficient();

        //C6
        final BigDecimal geometryWidth = params.getGeometry().getWidth();

        //C17 fcd
        final BigDecimal designCompressiveStrengthForDuctileMechanisms = getDesignCompressiveStrengthForDuctileMechanisms(params);

        // Forza di compressione nel calcestruzzo
        final BigDecimal concreteForce = firstDimensionlessCoefficient.setScale(SCALE, RoundingMode.HALF_UP)
                .multiply(geometryWidth)
                .multiply(neutralAxisPosition)
                .multiply(designCompressiveStrengthForDuctileMechanisms);

        // C12 As,sup: Area armatura compressa (superiore o inferiore a seconda della polarità)
        final BigDecimal compressiveSteelArea = concreteCoverService.getCompressiveSteelArea(params);
        // C81 σs2: Tensione nell'armatura compressa
        previous = calculateSteelCompressedTension(
                params,
                input,
                neutralAxisPosition,
                previous
        );
        final BigDecimal steelCompressedTension = previous.getSteelCompressedTension();
        // Forza di compressione nell'acciaio 12*81
        final BigDecimal compressiveSteelForce = compressiveSteelArea.multiply(steelCompressedTension);

        // C11 As,inf: Area armatura tesa (inferiore o superiore a seconda della polarità)
        final BigDecimal tensileSteelArea = concreteCoverService.getTensileSteelArea(params);
        // C80 σs1: Tensione nell'armatura tesa
        previous = calculateSteelTensileStress(params, input, neutralAxisPosition, previous);
        final BigDecimal tensileSteelStress = previous.getTensileSteelStress();
        // Forza di trazione nell'acciaio
        final BigDecimal tensileSteelForce = tensileSteelArea.multiply(tensileSteelStress);

        // Area FRP (C34·C35·C36 = tf·bf·nf)
        final BigDecimal frpArea = computeFrpArea(input).setScale(SCALE, RoundingMode.HALF_UP);
        // C82 σf: Tensione nel rinforzo FRP
        previous = calculateFrpStress(params, input, neutralAxisPosition, previous);
        final BigDecimal frpStress = previous.getFrpStress();
        // Forza di trazione nel rinforzo FRP
        final BigDecimal frpForce = frpArea.multiply(frpStress);

        // Somma delle forze: Compressione (+) e Trazione (-)
        final BigDecimal equilibrium = concreteForce.add(compressiveSteelForce).subtract(tensileSteelForce).subtract(frpForce).setScale(SCALE, RoundingMode.HALF_UP);

        return previous.toBuilder().neutralAxisPosition(neutralAxisPosition).equilibrium(equilibrium).build();
    }

    // ψ C71 firstDimensionlessCoefficient = IF(C75<=0.002,1000*C75*(0.5-(1000/12)*C75),1-(2/3000/C75))
    protected FlexuralCalculationResult calculateFirstAdimensionalCoefficient(
            final RectangularBeamParams params,
            final FlexuralVerifyExecutionInput input,
            final BigDecimal neutralAxisPosition,
            final FlexuralCalculationResult previous
    ) {
        //C75 εc [-]	Concrete strain
        final FlexuralCalculationResult withConcreteStrain = this.calculateConcreteStrain(params, input, neutralAxisPosition, previous);
        final BigDecimal concreteStrain = withConcreteStrain.getConcreteStrain();

        final BigDecimal firstAdimensionalCoefficient;
        if (concreteStrain.compareTo(BigDecimal.valueOf(0.002)) < 0) {
            //1000*C75*(0.5-(1000/12)*C75)
            firstAdimensionalCoefficient = BigDecimal.valueOf(1000)
                    .multiply(concreteStrain)
                    .multiply((BigDecimal.valueOf(0.5).subtract(BigDecimal.valueOf(1000).divide(BigDecimal.valueOf(12), SCALE, RoundingMode.HALF_UP).multiply(concreteStrain))));
        } else {
            // 1-(2/3000/C75)
            firstAdimensionalCoefficient = BigDecimal.ONE.subtract(BigDecimal.valueOf(2).divide(BigDecimal.valueOf(3000), SCALE, RoundingMode.HALF_UP).divide(concreteStrain, SCALE, RoundingMode.HALF_UP));
        }
        return withConcreteStrain.toBuilder().firstAdimensionalCoefficient(firstAdimensionalCoefficient.setScale(SCALE, RoundingMode.HALF_UP)).build();
    }

    // C72 λ secondDimensionlessCoefficient
    // =SE(C75<=0.002,(8-1000*C75)/4/(6-1000*C75),(1000*C75*(3000*C75-4)+2)/(2000*C75*(3000*C75-2)))
    protected FlexuralCalculationResult calculateSecondDimensionlessCoefficient(final RectangularBeamParams params,
                                                                                final FlexuralVerifyExecutionInput input,
                                                                                final BigDecimal neutralAxisPosition,
                                                                                FlexuralCalculationResult previous) {
        //C75 εc [-]	Concrete strain
        previous = this.calculateConcreteStrain(params, input, neutralAxisPosition, previous);
        final BigDecimal concreteStrain = previous.getConcreteStrain();

        final BigDecimal secondAdimensionalCoefficient;
        if (concreteStrain.compareTo(BigDecimal.valueOf(0.002)) <= 0) {
            // (8 - 1000 * concreteStrain) / 4 / (6 - 1000 * concreteStrain)
            final BigDecimal numerator = BigDecimal.valueOf(8).subtract(BigDecimal.valueOf(1000).multiply(concreteStrain));
            final BigDecimal denominator = BigDecimal.valueOf(6).subtract(BigDecimal.valueOf(1000).multiply(concreteStrain));
            secondAdimensionalCoefficient = numerator.divide(BigDecimal.valueOf(4), SCALE, RoundingMode.HALF_UP)
                    .divide(denominator, SCALE, RoundingMode.HALF_UP);
        } else {
            // (1000 * concreteStrain * (3000 * concreteStrain - 4) + 2) / (2000 * concreteStrain * (3000 * concreteStrain - 2))
            final BigDecimal part1 = BigDecimal.valueOf(3000).multiply(concreteStrain).subtract(BigDecimal.valueOf(4));
            final BigDecimal numerator = BigDecimal.valueOf(1000).multiply(concreteStrain).multiply(part1).add(BigDecimal.valueOf(2));
            final BigDecimal part2 = BigDecimal.valueOf(3000).multiply(concreteStrain).subtract(BigDecimal.valueOf(2));
            final BigDecimal denominator = BigDecimal.valueOf(2000).multiply(concreteStrain).multiply(part2);
            secondAdimensionalCoefficient = numerator.divide(denominator, SCALE, RoundingMode.HALF_UP);
        }

        return previous.toBuilder().secondAdimensionalCoefficient(secondAdimensionalCoefficient).build();
    }

    // C75 =IF(C65=1,(C64+C2)*C69/(C7-C69),C21)
    protected FlexuralCalculationResult calculateConcreteStrain(
            final RectangularBeamParams params,
            final FlexuralVerifyExecutionInput input,
            final BigDecimal neutralAxisPosition,
            final FlexuralCalculationResult previous
    ) {
        // C65
        final FlexuralCalculationResult resultWithFailureMode = this.getFailureMode(params, input, previous);
        var failureMode = resultWithFailureMode.getFailureMode();
        // C64 maxDesignReinforcementStrain
        final FlexuralCalculationResult resultWithMaxDesignReinforcementStrain = getMaxDesignReinforcementStrain(params, input, resultWithFailureMode);
        final BigDecimal maxDesignReinforcementStrain = resultWithMaxDesignReinforcementStrain.getMaxDesignReinforcementStrain();
        // C2
        final BigDecimal initialDeformation = params.getInitialDeformation();
        final BigDecimal C7 = params.getGeometry().getHeight();

        final BigDecimal concreteStrain;
        if (failureMode == 1) {
            // (C64+C2)*C69/(C7-C69)
            concreteStrain = ((maxDesignReinforcementStrain.add(initialDeformation)).multiply(neutralAxisPosition)).divide(C7.subtract(neutralAxisPosition), SCALE, RoundingMode.HALF_UP);
        } else {
            // C21
            concreteStrain = CONCRETE_ULTIMATE_STRAIN;
        }
        return resultWithMaxDesignReinforcementStrain.toBuilder().concreteStrain(concreteStrain).build();
    }

    // C47 kb geometric_correction_coefficient
    // =SE(((2-C46)/(1+C46))^0.5>1,((2-C46)/(1+C46))^0.5,1)
    protected FlexuralCalculationResult calculateGeometricCorrectionCoefficient(
            final RectangularBeamParams params,
            final FlexuralVerifyExecutionInput input,
            final FlexuralCalculationResult previous) {
        final BigDecimal result = geometricCoefficientService.calculateGeometricCorrectionCoefficient(params.getGeometry(), input);
        return previous.toBuilder().geometricCorrectionCoefficient(result).build();
    }

    // C59 ffdd,2 [MPa]	Tensione max rinforzo modo 2 - valore di progetto [form. 4.12] max_reinforcement_stress
    // =1.25*C47/1.3*((2*C40*1.6/4/C27*((C18*C19)^0.5)*0.25)/(C34*C36))^0.5
    protected FlexuralCalculationResult calculateMaxReinforcementStress(
            final RectangularBeamParams params,
            final FlexuralVerifyExecutionInput input,
            final FlexuralCalculationResult previous) {

        final ReinforcedConcreteProduct product = input.getProduct();

        // C47: geometric_correction_coefficient
        final FlexuralCalculationResult resultWithGeometricCorrectionCoefficient = calculateGeometricCorrectionCoefficient(params, input, previous);
        final BigDecimal geometricCorrectionCoefficient = resultWithGeometricCorrectionCoefficient.getGeometricCorrectionCoefficient();

        // C18: resistenza media calcestruzzo
        final BigDecimal averageCompressiveStrength = params.getMaterialProperties().getConcreteClass().getAverageCompressiveStrength();
        // C19: resistenza media a trazione calcestruzzo
        final BigDecimal averageTensileStrength = params.getMaterialProperties().getConcreteClass().getAverageTensileStrength();
        // C40: modulo elastico FRP
        final BigDecimal elasticModulus = product.getElasticModulus();
        // C27: fattore di confidenza
        final BigDecimal confidenceFactor = params.getMaterialProperties().getKnowledgeLevel().getValue();
        // C34: spessore FRP (tf)
        final BigDecimal thickness = product.getThickness();
        // C36: numero strati (nf)
        final BigDecimal layersNumber = BigDecimal.valueOf(input.getLayersNumber());

        // ((C18*C19)^0.5)
        final BigDecimal sqrtConcrete = new BigDecimal(Math.sqrt(averageCompressiveStrength.multiply(averageTensileStrength).doubleValue())).setScale(SCALE, RoundingMode.HALF_UP);

        // Numeratore interno: 2 * C40 * 1.6 / 4 / C27 * sqrt(C18*C19) * 0.25
        final BigDecimal numerator = BigDecimal.valueOf(2)
                .multiply(elasticModulus)
                .multiply(BigDecimal.valueOf(1.6))
                .divide(BigDecimal.valueOf(4), SCALE, RoundingMode.HALF_UP)
                .divide(confidenceFactor, SCALE, RoundingMode.HALF_UP)
                .multiply(sqrtConcrete)
                .multiply(BigDecimal.valueOf(0.25));

        // Denominatore interno: (C34 * C36)
        final BigDecimal denominator = thickness.multiply(layersNumber);

        // Rapporto e radice quadrata
        final BigDecimal sqrtTerm = numerator.divide(denominator, SCALE, RoundingMode.HALF_UP).sqrt(new MathContext(SCALE));

        // Fattore moltiplicativo esterno: 1.25 * C47 / 1.3
        final BigDecimal factor = BigDecimal.valueOf(1.25)
                .multiply(geometricCorrectionCoefficient)
                .divide(BigDecimal.valueOf(1.3), 9, RoundingMode.HALF_UP);

        // Risultato finale
        final BigDecimal maxReinforcementStress = factor.multiply(sqrtTerm).setScale(SCALE, RoundingMode.HALF_UP);

        return resultWithGeometricCorrectionCoefficient.toBuilder().maxReinforcementStress(maxReinforcementStress).build();
    }

    // C60 εfdd,2 [-]	Deformazione max rinforzo modo 2  [form. 4.13]
    protected FlexuralCalculationResult calculateMaxReinforcementStrain(
            final RectangularBeamParams params,
            final FlexuralVerifyExecutionInput input,
            final FlexuralCalculationResult previous) {
        final ReinforcedConcreteProduct product = input.getProduct();
        final FlexuralCalculationResult resultWithMaxReinforcementStress = this.calculateMaxReinforcementStress(params, input, previous);
        final BigDecimal maxReinforcementStress = resultWithMaxReinforcementStress.getMaxReinforcementStress();
        //C40
        final BigDecimal elasticModulus = product.getElasticModulus();
        final BigDecimal maxReinforcementStrain = maxReinforcementStress.divide(elasticModulus, SCALE, RoundingMode.HALF_UP);
        return resultWithMaxReinforcementStress.toBuilder().maxReinforcementStrain(maxReinforcementStrain).build();
    }

    // C44 γRd	Fatt. parziale a flessione [Tab. 3.2] sempre uguale a 1
    // EXTRACTED
    protected FlexuralCalculationResult calculatePartialBendingFactor(final FlexuralCalculationResult previous) {
        return previous.toBuilder().partialBendingFactor(BigDecimal.ONE).build();
    }

    // C45 γf	Fatt. parziale materiale [Tab. 3.1]
    // =SE(C32="Carbonio preformato",1.25,1.3)
    // EXTRACTED
    protected FlexuralCalculationResult calculateMaterialPartialFactor(final ReinforcedConcreteProduct product,
                                                                       final FlexuralCalculationResult previous) {
        final BigDecimal factor = environmentalAndSafetyFactorService.calculateMaterialPartialFactor(product);
        return previous.toBuilder().materialPartialFactor(factor).build();
    }

    // C64 εfd Max design reinforcement strain [eq. 4.51]
    // =MIN(C43*C41/C45,C60)
    protected FlexuralCalculationResult getMaxDesignReinforcementStrain(
            final RectangularBeamParams params,
            final FlexuralVerifyExecutionInput input,
            FlexuralCalculationResult previous
    ) {
        final ReinforcedConcreteProduct product = input.getProduct();

        // C41 system deformation from product
        final BigDecimal systemDeformation = product.getSystemDeformation();

        // C43
        previous = getAmbientFactor(params, product, previous);
        final BigDecimal ambientFactor = previous.getEnvironmentalConversionFactor();

        // C45
        previous = calculateMaterialPartialFactor(product, previous);
        final BigDecimal partialSafetyFactor = previous.getMaterialPartialFactor();

        // C60 εfdd,2 	max_reinforcement_strain
        previous = calculateMaxReinforcementStrain(params, input, previous);
        final BigDecimal maxReinforcementStrain = previous.getMaxReinforcementStrain();

        final BigDecimal maxDesignReinforcementStrain = ambientFactor.multiply(systemDeformation).divide(partialSafetyFactor, SCALE, RoundingMode.HALF_UP).min(maxReinforcementStrain);

        return previous.toBuilder()
                .maxReinforcementStrain(maxReinforcementStrain)
                .maxDesignReinforcementStrain(maxDesignReinforcementStrain)
                .secondExperimentalData(systemDeformation)
                .build();
    }

    // 65 Failure Mode § *******
    // EXTRACTED
    protected FlexuralCalculationResult getFailureMode(final RectangularBeamParams params,
                                                       final FlexuralVerifyExecutionInput input,
                                                       final FlexuralCalculationResult previous) {
        final FlexuralCalculationResult resultWithMaxReinforcementStress = calculateMaxReinforcementStress(params, input, previous);
        final BigDecimal maxStress = resultWithMaxReinforcementStress.getMaxReinforcementStress(); // C59
        int failureMode = failureModeService.getFailureMode(params, input, maxStress);
        return resultWithMaxReinforcementStress.toBuilder().failureMode(failureMode).build();
    }

    // fyd,d = fyk / (FC * γs,d ) [Mpa]	Design yield strength - ductile mechanisms
    // =D45/D35/1
    // EXTRACTED
    protected BigDecimal getDesignYieldStrengthForDuctileMechanisms(final RectangularBeamParams params) {
        return materialStrengthService.getDesignYieldStrengthForDuctileMechanisms(params.getMaterialProperties());
    }

    // fcd,d = fcm / (FC * γc,d ) [Mpa]	Design compressive strength - ductile mechanisms
    // =D39/D35/1
    // EXTRACTED
    protected BigDecimal getDesignCompressiveStrengthForDuctileMechanisms(final RectangularBeamParams params) {
        return materialStrengthService.getDesignCompressiveStrengthForDuctileMechanisms(params.getMaterialProperties());
    }


    /**
     * Calcola il fattore di conversione ambientale basandosi sul tipo di materiale e la classe di esposizione.
     * Questo metodo traduce la logica nidificata di SE/CERCA.VERT del foglio Excel.
     *
     * @param params  I parametri della trave, che contengono la classe di esposizione.
     * @param product Il prodotto, che contiene il tipo di materiale.
     * @return Il fattore ambientale calcolato come BigDecimal.
     * @throws IllegalArgumentException se le proprietà richieste non sono valide.
     */
    protected FlexuralCalculationResult getAmbientFactor(final RectangularBeamParams params,
                                                         final ReinforcedConcreteProduct product,
                                                         final FlexuralCalculationResult previous) {

        final BigDecimal environmentalConversionFactor =
                environmentalAndSafetyFactorService.getAmbientFactor(params.getGeometry().getExposure(), product);

        return previous.toBuilder().environmentalConversionFactor(environmentalConversionFactor).build();
    }

    // C48: fbm [MPa] average value of Tension tangent max
    // =SE(C32="Carbonio preformato",0.8*((C18*C19)^0.5)/(2*C27),1.25*((C18*C19)^0.5)/(2*C27))
    protected FlexuralCalculationResult calculateAverageValueOfTensionTangentMax(final RectangularBeamParams params,
                                                                                 final FlexuralVerifyExecutionInput input,
                                                                                 final FlexuralCalculationResult previous) {
        final ReinforcedConcreteProduct product = input.getProduct();
        final FiberType fiberType = product.getFiberType();

        // C18: resistenza media calcestruzzo
        final BigDecimal avgConcreteStrength = params.getMaterialProperties().getConcreteClass().getAverageCompressiveStrength();
        // C19: resistenza media a trazione calcestruzzo
        final BigDecimal avgTensileStrength = params.getMaterialProperties().getConcreteClass().getAverageTensileStrength();
        // C27: fattore di confidenz
        final BigDecimal confidenceFactor = params.getMaterialProperties().getKnowledgeLevel().getValue();

        final BigDecimal sqrtBase = new BigDecimal(Math.sqrt(avgConcreteStrength.multiply(avgTensileStrength).doubleValue()));
        final BigDecimal denominator = confidenceFactor.multiply(BigDecimal.valueOf(2));

        final BigDecimal averageValueOfTensionTangentMax;
        if (fiberType == FiberType.PREFORMED_CARBON) {
            averageValueOfTensionTangentMax = BigDecimal.valueOf(0.8).multiply(sqrtBase).divide(denominator, SCALE, RoundingMode.HALF_UP);
        } else {
            averageValueOfTensionTangentMax = BigDecimal.valueOf(1.25).multiply(sqrtBase).divide(denominator, SCALE, RoundingMode.HALF_UP);
        }

        return previous.toBuilder().averageValueOfTensionTangentMax(averageValueOfTensionTangentMax).build();
    }

    // C52 Led [mm]	Lungh. ottimale di ancoraggio di progetto [form. 4.1]
    // =SE(C32="Carbonio preformato",MAX(250,1.2/C48*(((PI.GRECO()^2)*C40*C34*C36*C50/2)^0.5)),MAX(100,1.2/C48*(((PI.GRECO()^2)*C40*C34*C36*C50/2)^0.5)))
    protected FlexuralCalculationResult calculateOptimalDesignAnchorageLength(
            final RectangularBeamParams params,
            final FlexuralVerifyExecutionInput input,
            final FlexuralCalculationResult previous) {

        final ReinforcedConcreteProduct product = input.getProduct();
        final FiberType fiberType = product.getFiberType();

        /// C40: modulo elastico FRP
        final BigDecimal elasticModulus = product.getElasticModulus();
        // C34: spessore FRP (tf)
        final BigDecimal thickness = product.getThickness();
        // C36: numero strati (nf)
        final BigDecimal layersNumber = BigDecimal.valueOf(input.getLayersNumber());
        // C48: tensione di progetto FRP
        final FlexuralCalculationResult resultWithAverageValueOfTensionTangentMax = this.calculateAverageValueOfTensionTangentMax(params, input, previous);
        final BigDecimal averageValueOfTensionTangentMax = resultWithAverageValueOfTensionTangentMax.getAverageValueOfTensionTangentMax();
        // C50: Energia specifica di frattura = 1/2 * C48 * 0.25
        final BigDecimal fractureEnergy = averageValueOfTensionTangentMax.multiply(BigDecimal.valueOf(0.25))
                .divide(BigDecimal.valueOf(2), SCALE, RoundingMode.HALF_UP);

        // π²
        final BigDecimal piSquared = BigDecimal.valueOf(Math.PI * Math.PI);

        // ((π² * C40 * C34 * C35 * C36 * C50) / 2)
        final BigDecimal base = piSquared
                .multiply(elasticModulus)
                .multiply(thickness)
                .multiply(layersNumber)
                .multiply(fractureEnergy)
                .divide(BigDecimal.valueOf(2), SCALE, RoundingMode.HALF_UP);

        // sqrt(...)
        final BigDecimal sqrtBase = new BigDecimal(Math.sqrt(base.doubleValue()));

        // 1.2 / C48 * sqrt(...)
        final BigDecimal anchorageLength = BigDecimal.valueOf(1.2)
                .divide(averageValueOfTensionTangentMax, SCALE, RoundingMode.HALF_UP)
                .multiply(sqrtBase);

        final BigDecimal result;
        if (fiberType == FiberType.PREFORMED_CARBON) {
            result = anchorageLength.max(BigDecimal.valueOf(250));
        } else {
            result = anchorageLength.max(BigDecimal.valueOf(100));
        }

        return resultWithAverageValueOfTensionTangentMax.toBuilder().fractureEnergy(fractureEnergy).optimalDesignAnchorageLength(result.setScale(SCALE, RoundingMode.HALF_UP)).build();

    }

    // Area FRP = tf * bf * nf  (C34 * C35 * C36)
    // EXTRACTED
    private BigDecimal computeFrpArea(final FlexuralVerifyExecutionInput input) {
        // tf (C34) = product.getThickness(); bf (C35) = input.getStripWidth(); nf (C36) = input.getLayersNumber()
        return frpUtilsService.computeFrpArea(input);
    }
}
