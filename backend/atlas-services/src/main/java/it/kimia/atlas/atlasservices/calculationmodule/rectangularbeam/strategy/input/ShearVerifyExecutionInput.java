package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input;

import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProduct;
import it.kimia.atlas.atlasservices.calculationmodule.dto.ReinforcementLayout;

import java.math.BigDecimal;

public interface ShearVerifyExecutionInput {

    int getStripSpacingAlongElementAxis();

    int getStripSpacingOrthogonalElementAxis();

    int getLayersNumber();

    int getStripInclination();

    int getConcreteStrutInclination();

    ReinforcedConcreteProduct getProduct();

    ReinforcementLayout getReinforcementLayout();

    int getWebHeight();

    int getStripWidth();

    BigDecimal getAppliedShearForce();
}

