package it.kimia.atlas.atlasservices.calculationmodule;

import it.kimia.atlas.atlasservices.calculationmodule.dto.ModuleCreateDTO;
import it.kimia.atlas.atlasservices.calculationmodule.dto.ModuleUpdateDTO;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for managing modules within projects.
 */
public interface CalculationModuleService {

    /**
     * Get all modules for a project.
     *
     * @param projectId the project ID
     * @return a list of all modules in the project
     */
    List<CalculationModule> getAllModules(String projectId);

    /**
     * Get a module by ID.
     *
     * @param projectId the project ID
     * @param moduleId  the module ID
     * @return the module, if found
     */
    Optional<CalculationModule> getModuleById(String projectId, String moduleId);

    /**
     * Create a new module in a project.
     *
     * @param projectId       the project ID
     * @param moduleCreateDTO the module to create
     * @return the created module
     */
    CalculationModule createModule(String projectId, ModuleCreateDTO moduleCreateDTO);

    /**
     * Update an existing module.
     *
     * @param projectId       the project ID
     * @param moduleId        the module ID
     * @param moduleUpdateDTO the updated module
     * @return the updated module
     */
    Optional<CalculationModule> updateModule(String projectId, String moduleId, ModuleUpdateDTO moduleUpdateDTO);

    /**
     * Delete a module by ID.
     *
     * @param projectId the project ID
     * @param moduleId  the module ID
     */
    void deleteModule(String projectId, String moduleId);


    /**
     * Update an existing module.
     *
     * @param projectId the project ID
     * @param moduleId  the module ID
     * @param params    parma objet to validate and store
     * @return the updated module
     */
    Optional<CalculationModule> updateModuleParams(String projectId, String moduleId, Object params);

}
