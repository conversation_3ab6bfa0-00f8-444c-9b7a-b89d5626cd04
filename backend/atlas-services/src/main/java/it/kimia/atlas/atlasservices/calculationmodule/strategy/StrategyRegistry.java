package it.kimia.atlas.atlasservices.calculationmodule.strategy;

import it.kimia.atlas.atlasservices.calculationmodule.CalculationModule;
import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Registry for calculation strategies.
 * <p>
 * This class manages all available calculation strategies and provides methods to find
 * the appropriate strategy for a given module type and calculation type.
 */
@Component
public class StrategyRegistry {

    private final Map<StrategyKey, CalculationStrategy<?, ?>> strategies = new HashMap<>();

    /**
     * Constructs a new StrategyRegistry with the provided list of CalculationStrategy instances.
     * <p>
     * The strategies are organized by their module type and calculation type for efficient lookup.
     *
     * @param strategies the list of available calculation strategies
     */
    public StrategyRegistry(List<CalculationStrategy<?, ?>> strategies) {
        strategies.forEach(strategy -> {
            StrategyKey key = new StrategyKey(strategy.getModuleType(), strategy.getCalculationType());
            this.strategies.put(key, strategy);
        });
    }

    /**
     * Finds the appropriate strategy for the given module type and calculation type.
     *
     * @param moduleType      the type of module
     * @param calculationType the type of calculation
     * @return an Optional containing the strategy if found, or empty if no strategy is available
     */
    public Optional<CalculationStrategy<?, ?>> findStrategy(ModuleType moduleType, CalculationType calculationType) {
        StrategyKey key = new StrategyKey(moduleType, calculationType);
        return Optional.ofNullable(strategies.get(key));
    }

    /**
     * Finds the appropriate strategy for the given module and calculation type.
     *
     * @param module          the module
     * @param calculationType the type of calculation
     * @return an Optional containing the strategy if found, or empty if no strategy is available
     */
    public Optional<CalculationStrategy<?, ?>> findStrategy(CalculationModule module, CalculationType calculationType) {
        return findStrategy(module.getType(), calculationType);
    }

    /**
     * Checks if a strategy exists for the given module type and calculation type.
     *
     * @param moduleType      the type of module
     * @param calculationType the type of calculation
     * @return true if a strategy exists, false otherwise
     */
    public boolean hasStrategy(ModuleType moduleType, CalculationType calculationType) {
        StrategyKey key = new StrategyKey(moduleType, calculationType);
        return strategies.containsKey(key);
    }

    /**
     * Checks if a strategy exists for the given module and calculation type.
     *
     * @param module          the module
     * @param calculationType the type of calculation
     * @return true if a strategy exists, false otherwise
     */
    public boolean hasStrategy(CalculationModule module, CalculationType calculationType) {
        return hasStrategy(module.getType(), calculationType);
    }

    /**
     * Key class for the strategy map.
     * <p>
     * This class combines a module type and calculation type to create a unique key
     * for looking up strategies in the registry.
     */
    private static class StrategyKey {
        private final ModuleType moduleType;
        private final CalculationType calculationType;

        public StrategyKey(ModuleType moduleType, CalculationType calculationType) {
            this.moduleType = moduleType;
            this.calculationType = calculationType;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            StrategyKey that = (StrategyKey) o;
            return moduleType == that.moduleType && calculationType == that.calculationType;
        }

        @Override
        public int hashCode() {
            return 31 * moduleType.hashCode() + calculationType.hashCode();
        }
    }
}