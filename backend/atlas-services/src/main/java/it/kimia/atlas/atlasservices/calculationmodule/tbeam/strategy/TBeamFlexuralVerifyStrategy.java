package it.kimia.atlas.atlasservices.calculationmodule.tbeam.strategy;

import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationStrategy;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationType;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.StrategyInputValidator;
import it.kimia.atlas.atlasservices.calculationmodule.tbeam.TBeamModule;
import it.kimia.atlas.atlasservices.calculationmodule.tbeam.TBeamParams;
import it.kimia.atlas.atlasservices.calculationmodule.tbeam.strategy.input.TBeamFlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.tbeam.strategy.input.TBeamFlexuralVerifyExecutionInputMapper;
import it.kimia.atlas.atlasservices.calculationmodule.tbeam.strategy.input.TBeamFlexuralVerifyInput;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Strategy implementation for FLEXURAL_VERIFY calculations on TBeamModule.
 * <p>
 * This strategy performs flexural verification calculations on T-shaped beam modules.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class TBeamFlexuralVerifyStrategy implements CalculationStrategy<TBeamModule, TBeamFlexuralVerifyInput> {

    private final StrategyInputValidator<TBeamFlexuralVerifyInput> tBeamFlexuralInputValidatorStrategy;
    private final TBeamFlexuralVerifyExecutionInputMapper flexuralVerifyExecutionInputMapper;

    @Override
    public Class<TBeamFlexuralVerifyInput> getInputType() {
        return TBeamFlexuralVerifyInput.class;
    }

    @Override
    public TBeamModule execute(TBeamModule module, TBeamFlexuralVerifyInput input) {

        // Get module parameters
        final TBeamParams params = module.getParams();
        if (params == null) {
            throw new IllegalStateException("Module parameters are not set");
        }

        final TBeamFlexuralVerifyInput verifiedInput = tBeamFlexuralInputValidatorStrategy.validate(input);
        final TBeamFlexuralVerifyExecutionInput flexuralVerifyExecutionInput =
                flexuralVerifyExecutionInputMapper.map(verifiedInput);

        // For demonstration purposes, we'll just log the calculation and return the module
        log.info("FLEXURAL_VERIFY calculation completed for T-beam module {} with bending moment {} kNm, strip width {} mm, and layers number {}",
                module.getId(), input.getBendingMoment(), input.getStripWidth(), input.getLayersNumber());

        module.setFlexuralVerifyExecutionInput(flexuralVerifyExecutionInput);

        return module;
    }


    @Override
    public ModuleType getModuleType() {
        return ModuleType.T_BEAM;
    }

    @Override
    public CalculationType getCalculationType() {
        return CalculationType.FLEXURAL_VERIFY;
    }

}