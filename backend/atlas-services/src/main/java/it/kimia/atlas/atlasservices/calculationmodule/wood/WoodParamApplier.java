package it.kimia.atlas.atlasservices.calculationmodule.wood;

import com.fasterxml.jackson.databind.ObjectMapper;
import it.kimia.atlas.atlasservices.calculationmodule.CalculationModule;
import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import it.kimia.atlas.atlasservices.calculationmodule.ParamApplier;
import it.kimia.atlas.atlasservices.calculationmodule.validator.ValidationException;
import it.kimia.atlas.atlasservices.calculationmodule.validator.model.ValidationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Parameter applier for Wood modules.
 * <p>
 * This class handles the application of parameters to Wood calculation modules,
 * including validation, parsing, and merging of parameters.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class WoodParamApplier implements ParamApplier {

    private final WoodParamsValidator validator;
    private final ObjectMapper objectMapper;
    private final WoodParamsMapper mapper;

    @Override
    public ModuleType getModuleType() {
        return ModuleType.WOOD;
    }

    @Override
    public void apply(CalculationModule calculationModule, Object params) {
        if (calculationModule instanceof WoodModule module) {
            log.debug("Applying parameters to Wood module: {}", module.getId());

            // Parse the parameters from Object to WoodParams
            final WoodParams parsedParams = objectMapper.convertValue(params, WoodParams.class);

            // Validate the parsed parameters
            ValidationResult validationResult = validator.validate(parsedParams);
            if (!validationResult.isValid()) {
                log.warn("Wood parameters validation failed for module {}: {}", 
                    module.getId(), validationResult.getFieldErrors());
                throw new ValidationException("Validation Failed", validationResult.getFieldErrors());
            }

            // Merge the new parameters with existing ones
            final WoodParams mergedParams = mapper.merge(module.getParams(), parsedParams);
            module.setParams(mergedParams);

            // Clear any existing calculation results since parameters have changed
            // When calculation result classes are implemented, clear them here:
            // module.setFlexuralCalculationResult(null);
            // module.setShearCalculationResult(null);

            log.info("Successfully applied parameters to Wood module: {}", module.getId());
        } else {
            throw new IllegalArgumentException("Invalid module type: " + calculationModule.getType() + 
                ". Expected: " + ModuleType.WOOD);
        }
    }
}
