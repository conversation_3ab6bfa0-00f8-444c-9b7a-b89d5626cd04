package it.kimia.atlas.atlasservices.product;

import it.kimia.atlas.atlasservices.project.ProjectType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository for managing Product entities.
 */
@Repository
public interface ProductRepository extends MongoRepository<Product, String> {

    /**
     * Find products by name containing the given string (case-insensitive).
     *
     * @param name the name to search for
     * @param pageable pagination information
     * @return a page of products with names containing the given string
     */
    Page<Product> findByNameContainingIgnoreCase(String name, Pageable pageable);

    /**
     * Find products by category.
     *
     * @param category the category to search for
     * @param pageable pagination information
     * @return a page of products with the given category
     */
    Page<Product> findByCategoriesContaining(ProjectType category, Pageable pageable);

    /**
     * Find products by product type.
     *
     * @param productType the product type to search for
     * @param pageable pagination information
     * @return a page of products with the given product type
     */
    Page<Product> findByProductType(String productType, Pageable pageable);
}