package it.kimia.atlas.atlasservices.calculationmodule.tbeam;

import it.kimia.atlas.atlasservices.calculationmodule.dto.RebarsDefinition;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Reinforcement bar parameters for T-shaped beam.
 * <p>
 * This class contains the reinforcement bar parameters specific to T-shaped beams.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TBeamRebar {
    @Positive(message = "validation.tbeam.reinforcementBar.diameter.positive")
    @NotNull(message = "validation.tbeam.reinforcementBar.top.notnull")
    @Valid
    private RebarsDefinition top;

    @Positive(message = "validation.tbeam.reinforcementBar.diameter.positive")
    @NotNull(message = "validation.tbeam.reinforcementBar.bottom.notnull")
    @Valid
    private RebarsDefinition bottom;
}