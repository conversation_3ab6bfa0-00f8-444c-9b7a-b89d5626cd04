package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.AmbientFactorData;
import it.kimia.atlas.atlasservices.calculationmodule.Exposure;
import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProduct;
import it.kimia.atlas.atlasservices.product.FiberType;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

@Service
public class EnvironmentalAndSafetyFactorService {

    public BigDecimal getAmbientFactor(final Exposure exposure,
                                       final ReinforcedConcreteProduct product) {
        Objects.requireNonNull(product, "Il prodotto non può essere nullo.");
        Objects.requireNonNull(product.getFiberType(), "Il tipo di fibra del prodotto non può essere nullo.");
        Objects.requireNonNull(exposure, "La classe di esposizione non può essere nulla.");

        final FiberType fiberType = product.getFiberType();

        final AmbientFactorData factorData = AmbientFactorData.fromExposure(exposure);

        return switch (fiberType) {
            case STEEL -> factorData.getSteelFactor();
            case GLASS -> factorData.getGlassFactor();
            case CARBON -> factorData.getCarbonFactor();
            case PREFORMED_CARBON -> factorData.getPreformedCarbonFactor();
            default -> throw new IllegalArgumentException("This product is not allowed for this calculation.");
        };
    }

    // =SE(C32="Carbonio preformato",1.25,1.3)
    public BigDecimal calculateMaterialPartialFactor(final ReinforcedConcreteProduct product) {
        if (product.getFiberType() == FiberType.PREFORMED_CARBON) {
            return BigDecimal.valueOf(1.25);
        } else {
            return BigDecimal.valueOf(1.3);
        }
    }

}