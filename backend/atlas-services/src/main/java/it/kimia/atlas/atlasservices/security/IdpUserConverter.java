package it.kimia.atlas.atlasservices.security;

import it.kimia.atlas.atlasservices.security.model.Group;
import it.kimia.atlas.atlasservices.security.model.IdpUser;
import it.kimia.atlas.atlasservices.security.model.User;
import it.kimia.atlas.atlasservices.shared.model.CurrentUser;
import it.kimia.atlas.atlasservices.shared.model.UserGroup;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

/**
 * Converter for transforming security model objects to user model objects.
 * This class is in the security module to avoid exposing security model classes to other modules.
 */
@Component
public class IdpUserConverter {

    /**
     * Converts an IdpUser to a CurrentUser.
     *
     * @param idpUser the IdpUser to convert
     * @return the converted CurrentUser
     */
    public CurrentUser convertToCurrentUser(IdpUser idpUser) {
        if (idpUser == null) {
            return null;
        }

        User user = idpUser.getUser();

        return CurrentUser.builder()
                .id(user.getId())
                .username(user.getUsername())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .email(user.getEmail())
                .emailVerified(user.getEmailVerified())
                .createdTimestamp(user.getCreatedTimestamp())
                .enabled(user.getEnabled())
                .roles(idpUser.getRoles().stream()
                        .map(Enum::name)
                        .collect(Collectors.toList()))
                .groups(idpUser.getGroups().stream()
                        .map(this::convertToUserGroup)
                        .collect(Collectors.toList()))
                .build();
    }

    /**
     * Converts a Group to a UserGroup.
     *
     * @param group the Group to convert
     * @return the converted UserGroup
     */
    protected UserGroup convertToUserGroup(Group group) {
        return UserGroup.builder()
                .name(group.getName())
                .userIds(group.getUsers().stream()
                        .map(User::getId)
                        .collect(Collectors.toList()))
                .build();
    }
}
