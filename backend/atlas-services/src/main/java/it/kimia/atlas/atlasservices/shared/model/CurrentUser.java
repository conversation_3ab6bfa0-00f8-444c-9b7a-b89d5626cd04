package it.kimia.atlas.atlasservices.shared.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Represents the current authenticated user in the system.
 * This class is in the shared package to be accessible by multiple modules.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CurrentUser {
    private String id;
    private String username;
    private String firstName;
    private String lastName;
    private String email;
    private Boolean emailVerified;
    private Long createdTimestamp;
    private Boolean enabled;
    private List<String> roles;
    private List<UserGroup> groups;

    /**
     * Gets all user IDs in the visibility cone of this user.
     * This includes the user's own ID and the IDs of all users in the user's subgroups.
     *
     * @return A set of user IDs in the visibility cone
     */
    public Set<String> getVisibilityCone() {
        Set<String> userIds = Optional.ofNullable(groups).orElse(List.of()).stream()
                .flatMap(subGroup -> subGroup.getUserIds().stream())
                .collect(Collectors.toSet());

        userIds.add(id);

        return userIds;
    }

    /**
     * Checks if the user has visibility to a project with the given user ID.
     *
     * @param projectUserId The user ID of the project owner
     * @return true if the user has visibility, false otherwise
     */
    public boolean hasVisibilityTo(String projectUserId) {
        if (projectUserId == null) {
            return false;
        }

        // User has visibility if they are the owner or if the owner is in their subgroups
        if (projectUserId.equals(id)) {
            return true;
        }

        return Optional.ofNullable(groups).orElse(List.of()).stream()
                .flatMap(subGroup -> subGroup.getUserIds().stream())
                .anyMatch(userId -> projectUserId.equals(userId));
    }
}