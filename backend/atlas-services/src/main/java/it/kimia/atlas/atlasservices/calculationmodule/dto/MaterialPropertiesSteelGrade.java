package it.kimia.atlas.atlasservices.calculationmodule.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MaterialPropertiesSteelGrade {
    @NotNull(message = "validation.rectangularbeam.steelGrade.id.notnull")
    private String id;

    /**
     * The name of the steel grade (e.g., "Aq42", "Aq50").
     */
    @NotNull(message = "validation.rectangularbeam.steelGrade.name.notnull")
    private String name;

    /**
     * The characteristic yield strength in MPa. (fyk)
     */
    @NotNull(message = "validation.rectangularbeam.steelGrade.yieldStrength.notnull")
    @Positive(message = "validation.rectangularbeam.steelGrade.yieldStrength.positive")
    private BigDecimal yieldStrength;

    /**
     * The characteristic tensile strength in MPa.
     */
    @NotNull(message = "validation.rectangularbeam.steelGrade.tensileStrength.notnull")
    @Positive(message = "validation.rectangularbeam.steelGrade.tensileStrength.positive")
    private Integer tensileStrength;

    /**
     * The characteristic elongation percentage.
     */
    @NotNull(message = "validation.rectangularbeam.steelGrade.elongationPercentage.notnull")
    @Positive(message = "validation.rectangularbeam.steelGrade.elongationPercentage.positive")
    private Double elongationPercentage;

    /**
     * The characteristic elastic modulus in MPa.
     */
    @NotNull(message = "validation.rectangularbeam.steelGrade.elasticModulus.notnull")
    @Positive(message = "validation.rectangularbeam.steelGrade.elasticModulus.positive")
    private Integer elasticModulus;

    /**
     * The design yield strength (in MPa) to be used for ductile mechanisms,
     * according to structural codes. This value ensures that, under extreme loads,
     * the steel behaves in a ductile manner, allowing significant deformation before failure.
     */
    @NotNull(message = "validation.rectangularbeam.steelGrade.designYieldStrengthForBrittleMechanisms.notnull")
    @Positive(message = "validation.rectangularbeam.steelGrade.designYieldStrengthForBrittleMechanisms.positive")
    private BigDecimal designYieldStrengthForBrittleMechanisms;

    /**
     * The design yield strength (in MPa) to be used for brittle mechanisms,
     * as specified by structural codes. This value is used to check elements
     * or details that may fail in a brittle (non-ductile) manner, ensuring safety against sudden failure.
     */
    @NotNull(message = "validation.rectangularbeam.steelGrade.designYieldStrengthForDuctileMechanisms.notnull")
    @Positive(message = "validation.rectangularbeam.steelGrade.designYieldStrengthForDuctileMechanisms.positive")
    private BigDecimal designYieldStrengthForDuctileMechanisms;

}
