package it.kimia.atlas.atlasservices.materials;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Service implementation for managing steel grade materials using MongoDB.
 */
@Service
@RequiredArgsConstructor
public class SteelGradeServiceMongoDb implements SteelGradeService {

    private final SteelGradeRepository steelGradeRepository;

    /**
     * Get all steel grades with pagination and optional filtering.
     *
     * @param pageable pagination information
     * @return a page of steel grades matching the criteria
     */
    @Override
    public Page<SteelGrade> getSteelGrades(
            Pageable pageable) {
        return steelGradeRepository.findAll(pageable);
    }

    /**
     * Get a steel grade by ID.
     *
     * @param id the steel grade ID
     * @return the steel grade, if found
     */
    @Override
    public Optional<SteelGrade> getSteelGradeById(String id) {
        return steelGradeRepository.findById(id);
    }

}