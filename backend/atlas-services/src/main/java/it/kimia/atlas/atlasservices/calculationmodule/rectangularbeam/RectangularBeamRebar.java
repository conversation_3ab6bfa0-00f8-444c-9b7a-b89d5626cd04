package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam;

import it.kimia.atlas.atlasservices.calculationmodule.dto.RebarsDefinition;
import it.kimia.atlas.atlasservices.calculationmodule.dto.TransverseReinforcement;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RectangularBeamRebar {
    @NotNull(message = "validation.rectangularbeam.reinforcementBar.top.notnull")
    @Valid
    private RebarsDefinition top;

    @NotNull(message = "validation.rectangularbeam.reinforcementBar.bottom.notnull")
    @Valid
    private RebarsDefinition bottom;

    @NotNull(message = "validation.rectangularbeam.reinforcementBar.transverse.notnull")
    @Valid
    private TransverseReinforcement transverse;
}
