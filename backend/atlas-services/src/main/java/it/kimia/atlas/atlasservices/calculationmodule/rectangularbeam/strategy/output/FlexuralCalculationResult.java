package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.output;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder(toBuilder = true)
public class FlexuralCalculationResult {
    // C77
    private BigDecimal compressedSteelStrain;
    private BigDecimal concreteStrain;
    private BigDecimal averageValueOfTensionTangentMax;
    private BigDecimal environmentalConversionFactor;
    private BigDecimal equilibrium;
    private BigDecimal firstAdimensionalCoefficient;
    private BigDecimal firstExperimentalData;
    private BigDecimal fractureEnergy;
    private BigDecimal frpReinforcementStrain;
    private BigDecimal frpStress;
    private BigDecimal geometricCorrectionCoefficient;
    private BigDecimal materialPartialFactor;
    private BigDecimal maxDesignReinforcementStrain;
    private BigDecimal maxReinforcementStrain;
    private BigDecimal maxReinforcementStress;
    private BigDecimal momentCapacity;
    private BigDecimal neutralAxisPosition;
    private BigDecimal optimalDesignAnchorageLength;
    private BigDecimal partialBendingFactor;
    private BigDecimal secondAdimensionalCoefficient;
    private BigDecimal secondExperimentalData;
    private BigDecimal steelCompressedTension;
    private BigDecimal tensileSteelStrain;
    private BigDecimal tensileSteelStress;
    private boolean checkResult;
    private int failureMode;
}
