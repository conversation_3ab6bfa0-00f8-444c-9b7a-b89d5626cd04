// GeometricCoefficientServiceImpl.java
package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamGeometry;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInput;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;

@Service
public class GeometricCoefficientServiceImpl implements GeometricCoefficientService {

    //C46 bf/b	Rapporto largh. rinforzo/largh. sezione
    // =SE(C35/C6<0.25,0.25,C35/C6)
    public BigDecimal getWidthSectionRation(RectangularBeamGeometry geometry, FlexuralVerifyExecutionInput input) {
        final BigDecimal ratio = BigDecimal.valueOf(input.getStripWidth())
                .divide(geometry.getWidth(), SCALE, RoundingMode.HALF_UP);
        return (ratio.compareTo(BigDecimal.valueOf(0.25)) < 0 ? BigDecimal.valueOf(0.25) : ratio).setScale(SCALE, RoundingMode.HALF_UP);
    }

    @Override
    public BigDecimal calculateGeometricCorrectionCoefficient(RectangularBeamGeometry geometry, FlexuralVerifyExecutionInput input) {
        final BigDecimal widthSectionRation = getWidthSectionRation(geometry, input);
        final BigDecimal coefficient = BigDecimal.valueOf(2).subtract(widthSectionRation)
                .divide(BigDecimal.valueOf(1).add(widthSectionRation), SCALE, RoundingMode.HALF_UP);
        if (coefficient.compareTo(BigDecimal.ONE) > 0) {
            return coefficient.sqrt(new MathContext(SCALE));
        } else {
            return BigDecimal.ONE.setScale(SCALE, RoundingMode.HALF_UP);
        }
    }
}