package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamParams;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.ShearVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.output.ShearCalculationResult;

public interface RectangularBeamShearVerify {
    ShearCalculationResult execute(final RectangularBeamParams params,
                                   final ShearVerifyExecutionInput shearVerifyExecutionInput);
}
