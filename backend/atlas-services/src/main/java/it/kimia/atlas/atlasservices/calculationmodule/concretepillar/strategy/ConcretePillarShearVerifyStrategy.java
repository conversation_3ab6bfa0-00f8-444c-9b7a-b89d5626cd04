package it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy;

import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.ConcretePillarModule;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.ConcretePillarParams;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input.ConcretePillarShearVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input.ConcretePillarShearVerifyInput;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.mappers.ConcretePillarShearVerifyExecutionInputMapper;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.output.ConcretePillarShearCalculationResult;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.strategy.ConcretePillarShearVerify;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationStrategy;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationType;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.StrategyInputValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Strategy implementation for SHEAR_VERIFY calculations on TBeamModule.
 * <p>
 * This strategy performs flexural verification calculations on Pillar beam modules.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ConcretePillarShearVerifyStrategy implements CalculationStrategy<ConcretePillarModule, ConcretePillarShearVerifyInput> {

    private final StrategyInputValidator<ConcretePillarShearVerifyInput> concretePillarFlexuralInputValidatorStrategy;
    private final ConcretePillarShearVerifyExecutionInputMapper mapper;
    private final ConcretePillarShearVerify concretePillarShearVerify;

    @Override
    public Class<ConcretePillarShearVerifyInput> getInputType() {
        return ConcretePillarShearVerifyInput.class;
    }

    @Override
    public ConcretePillarModule execute(ConcretePillarModule module, ConcretePillarShearVerifyInput input) {

        // Get module parameters
        final ConcretePillarParams params = module.getParams();
        if (params == null) {
            throw new IllegalStateException("Module parameters are not set");
        }

        final ConcretePillarShearVerifyInput verifiedInput = concretePillarFlexuralInputValidatorStrategy.validate(input);
        
        final ConcretePillarShearVerifyExecutionInput shearVerifyExecutionInput =
                mapper.map(verifiedInput);

        final ConcretePillarShearCalculationResult result = concretePillarShearVerify.execute(params, shearVerifyExecutionInput);

        module.setShearVerifyExecutionInput(shearVerifyExecutionInput);
        module.setShearCalculationResult(result);

        return module;
    }


    @Override
    public ModuleType getModuleType() {
        return ModuleType.PILLAR;
    }

    @Override
    public CalculationType getCalculationType() {
        return CalculationType.SHEAR_VERIFY;
    }

}