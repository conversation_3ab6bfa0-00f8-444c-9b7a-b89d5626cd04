// infra/BisectionRootFinder.java
package it.kimia.atlas.atlasservices.calculationmodule.utility;


import java.math.BigDecimal;
import java.util.function.Function;

public class BisectionRootFinder implements RootFinder {

    private final BigDecimal tol;
    private final int maxIter;

    public BisectionRootFinder(
            BigDecimal tol,
            int maxIter
    ) {
        this.tol = tol;
        this.maxIter = maxIter;
    }

    @Override
    public <T> T solve(BigDecimal lower, BigDecimal upper,
                       Function<BigDecimal, T> eval,
                       Function<T, BigDecimal> residual) {
        return BisectionSolver.solveByBisection(lower, upper, tol, maxIter, eval, residual);
    }
}