package it.kimia.atlas.atlasservices.calculationmodule.tbeam;

import it.kimia.atlas.atlasservices.calculationmodule.Polarity;
import it.kimia.atlas.atlasservices.calculationmodule.dto.MaterialProperties;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Parameters for T-shaped beam calculation module.
 * <p>
 * This class contains the parameters specific to T-shaped beam calculations.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TBeamParams implements Serializable {

    @NotNull
    private BigDecimal initialDeformation;

    @NotNull(message = "validation.tbeam.polarity.notnull")
    private Polarity polarity;

    @Valid
    private TBeamGeometry geometry;

    @Valid
    private TBeamRebar longitudinalReinforcement;

    @Valid
    private MaterialProperties materialProperties;
}