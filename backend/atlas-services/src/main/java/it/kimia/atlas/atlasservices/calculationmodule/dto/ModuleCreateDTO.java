package it.kimia.atlas.atlasservices.calculationmodule.dto;

import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * DTO for creating a new module.
 */
@Data
public class ModuleCreateDTO {

    @NotBlank(message = "Name is required")
    private String name;

    private String description;

    @NotNull(message = "Module type is required")
    private ModuleType type;

}