package it.kimia.atlas.atlasservices.product;

import it.kimia.atlas.atlasservices.product.dtos.ProductCreateDTO;
import it.kimia.atlas.atlasservices.product.dtos.ProductUpdateDTO;
import it.kimia.atlas.atlasservices.project.ProjectType;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for managing products.
 * Protected by Keycloak authentication.
 */
@RestController
@RequestMapping("/api/v2/products")
@PreAuthorize("isAuthenticated()")
@RequiredArgsConstructor
public class ProductController {

    private final ProductService productService;

    /**
     * Get all products with pagination and optional filtering.
     *
     * @param name        optional name filter (partial match, case-insensitive)
     * @param category    optional category filter
     * @param productType optional product type filter
     * @param pageable    pagination information
     * @return a page of products matching the criteria
     */
    @GetMapping
    public ResponseEntity<Page<Product>> getProducts(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) ProjectType category,
            @RequestParam(required = false) String productType,
            Pageable pageable) {
        return ResponseEntity.ok(productService.getProducts(
                name, category, productType, pageable));
    }

    /**
     * Get a product by ID.
     *
     * @param id the product ID
     * @return the product, if found
     */
    @GetMapping("/{id}")
    public ResponseEntity<Product> getProductById(@PathVariable String id) {
        return productService.getProductById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Create a new product.
     *
     * @param productDTO the product to create
     * @return the created product
     */
    @PostMapping
    public ResponseEntity<Product> createProduct(@Valid @RequestBody ProductCreateDTO productDTO) {
        return new ResponseEntity<>(productService.createProduct(productDTO), HttpStatus.CREATED);
    }

    /**
     * Update an existing product.
     *
     * @param id               the product ID
     * @param productUpdateDTO the updated product data
     * @return the updated product
     */
    @PutMapping("/{id}")
    public ResponseEntity<Product> updateProduct(
            @PathVariable String id,
            @Valid @RequestBody ProductUpdateDTO productUpdateDTO) {
        return productService.updateProduct(id, productUpdateDTO)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Delete a product by ID.
     *
     * @param id the product ID
     * @return no content
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteProduct(@PathVariable String id) {
        productService.deleteProduct(id);
        return ResponseEntity.noContent().build();
    }
}