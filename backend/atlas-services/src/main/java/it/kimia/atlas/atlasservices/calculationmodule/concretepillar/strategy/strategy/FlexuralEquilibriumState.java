package it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.strategy;

import lombok.Getter;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;


@Getter
public final class FlexuralEquilibriumState {

    private static final MathContext MC = new MathContext(20, RoundingMode.HALF_UP);

    // variabile
    private final BigDecimal xNeutralAxis; // C70

    // deformazioni
    private final BigDecimal epsC;    // C76
    private final BigDecimal epsS_t;  // C77
    private final BigDecimal epsS_c;  // C78
    private final BigDecimal epsFRP;  // C79

    // coefficienti/tensioni
    private final BigDecimal C72;         // coeff. adimensionale cls
    private final BigDecimal sigmaS_t;    // C81
    private final BigDecimal sigmaS_c;    // C82
    private final BigDecimal sigmaFRP;    // C83

    // contributi di sforzo (forze assiali, segno già coerente con C71)
    private final BigDecimal N_concrete;  // + C72*C6*C70*C17
    private final BigDecimal N_steel_c;   // + C12*C82
    private final BigDecimal N_steel_t;   // - C11*C81
    private final BigDecimal N_frp;       // - C83*C35*C34*C36
    private final BigDecimal N_ext;       // - Nsollec_kN*1000

    // residuo (da azzerare)
    private final BigDecimal residual;    // C71

    // costruttore privato (usa factory sotto)
    private FlexuralEquilibriumState(BigDecimal xNeutralAxis,
                                     BigDecimal epsC, BigDecimal epsS_t, BigDecimal epsS_c, BigDecimal epsFRP,
                                     BigDecimal C72, BigDecimal sigmaS_t, BigDecimal sigmaS_c, BigDecimal sigmaFRP,
                                     BigDecimal N_concrete, BigDecimal N_steel_c, BigDecimal N_steel_t, BigDecimal N_frp, BigDecimal N_ext,
                                     BigDecimal residual) {
        this.xNeutralAxis = xNeutralAxis;
        this.epsC = epsC;
        this.epsS_t = epsS_t;
        this.epsS_c = epsS_c;
        this.epsFRP = epsFRP;
        this.C72 = C72;
        this.sigmaS_t = sigmaS_t;
        this.sigmaS_c = sigmaS_c;
        this.sigmaFRP = sigmaFRP;
        this.N_concrete = N_concrete;
        this.N_steel_c = N_steel_c;
        this.N_steel_t = N_steel_t;
        this.N_frp = N_frp;
        this.N_ext = N_ext;
        this.residual = residual;
    }

    // ----- factory: calcola tutto lo stato (replica Excel) -----
    public static FlexuralEquilibriumState compute(
            BigDecimal xNeutralAxis, // C70
            int caseFlag,      // C66 (1 o 2)
            BigDecimal epsFrpDesign, // C64
            BigDecimal epsInit,      // C2
            BigDecimal hSection,     // C7
            BigDecimal dEffective,   // C10
            BigDecimal coverTop,     // C9
            BigDecimal epsLimit,     // C21
            BigDecimal widthSection, // C6
            BigDecimal fcd,          // C17
            BigDecimal AsComp,       // C12
            BigDecimal AsTens,       // C11
            BigDecimal bFrp,         // C35
            BigDecimal tFrp,         // C34
            BigDecimal nLayers,      // C36
            BigDecimal Es,           // C26
            BigDecimal fyd,          // C23
            BigDecimal Efrp,         // C40
            BigDecimal Nsollec_kN    // Verifiche!D57
    ) {
        // C76..C79
        BigDecimal epsC = (caseFlag == 1)
                ? (epsFrpDesign.add(epsInit, MC)).multiply(xNeutralAxis, MC)
                .divide(hSection.subtract(xNeutralAxis, MC), MC)
                : epsLimit;

        BigDecimal epsS_t = (caseFlag == 1)
                ? (epsFrpDesign.add(epsInit, MC))
                .multiply(dEffective.subtract(xNeutralAxis, MC), MC)
                .divide(hSection.subtract(xNeutralAxis, MC), MC)
                : epsLimit.multiply(dEffective.subtract(xNeutralAxis, MC), MC)
                .divide(xNeutralAxis, MC);

        BigDecimal epsS_c = (caseFlag == 1)
                ? (epsFrpDesign.add(epsInit, MC))
                .multiply(xNeutralAxis.subtract(coverTop, MC), MC)
                .divide(hSection.subtract(xNeutralAxis, MC), MC)
                : epsLimit.multiply(xNeutralAxis.subtract(coverTop, MC), MC)
                .divide(xNeutralAxis, MC);

        BigDecimal epsFRP = (caseFlag == 1)
                ? epsFrpDesign
                : epsLimit.divide(xNeutralAxis, MC)
                .multiply(hSection.subtract(xNeutralAxis, MC), MC)
                .subtract(epsInit, MC);

        // C72
        BigDecimal C72 = (epsC.compareTo(new BigDecimal("0.002")) <= 0)
                ? new BigDecimal("1000").multiply(epsC, MC)
                .multiply(new BigDecimal("0.5")
                        .subtract(new BigDecimal("1000").divide(new BigDecimal("12"), MC).multiply(epsC, MC), MC), MC)
                : BigDecimal.ONE.subtract(new BigDecimal("2").divide(new BigDecimal("3000"), MC).divide(epsC, MC), MC);

        // tensioni acciaio con limite fyd
        BigDecimal sigmaS_t = min(epsS_t.multiply(Es, MC), fyd); // C81
        BigDecimal sigmaS_c = min(epsS_c.multiply(Es, MC), fyd); // C82

        // FRP
        BigDecimal sigmaFRP = epsFRP.multiply(Efrp, MC);         // C83

        // contributi (segni come in Excel)
        BigDecimal N_concrete = C72.multiply(widthSection, MC).multiply(xNeutralAxis, MC).multiply(fcd, MC);
        BigDecimal N_steel_c = AsComp.multiply(sigmaS_c, MC);
        BigDecimal N_steel_t = AsTens.multiply(sigmaS_t, MC).negate(); // - C11*C81
        BigDecimal N_frp = sigmaFRP.multiply(bFrp, MC).multiply(tFrp, MC).multiply(nLayers, MC).negate();
        BigDecimal N_ext = Nsollec_kN.multiply(new BigDecimal("1000"), MC).negate();

        BigDecimal residual = N_concrete.add(N_steel_c, MC)
                .add(N_steel_t, MC)
                .add(N_frp, MC)
                .add(N_ext, MC);

        return new FlexuralEquilibriumState(
                xNeutralAxis, epsC, epsS_t, epsS_c, epsFRP,
                C72, sigmaS_t, sigmaS_c, sigmaFRP,
                N_concrete, N_steel_c, N_steel_t, N_frp, N_ext,
                residual
        );
    }

    private static BigDecimal min(BigDecimal a, BigDecimal b) {
        return a.compareTo(b) <= 0 ? a : b;
    }
}