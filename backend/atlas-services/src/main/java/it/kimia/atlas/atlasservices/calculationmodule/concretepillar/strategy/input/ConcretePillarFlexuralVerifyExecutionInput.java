package it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input;

import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProduct;

import java.math.BigDecimal;

public interface ConcretePillarFlexuralVerifyExecutionInput {
    BigDecimal getAppliedAxialForce();

    int getStripWidth();

    int getLayersNumber();

    BigDecimal getBendingMoment();

    ReinforcedConcreteProduct getProduct();
}

