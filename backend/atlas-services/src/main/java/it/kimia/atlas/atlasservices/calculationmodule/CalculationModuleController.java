package it.kimia.atlas.atlasservices.calculationmodule;

import it.kimia.atlas.atlasservices.calculationmodule.dto.ModuleCreateDTO;
import it.kimia.atlas.atlasservices.calculationmodule.dto.ModuleUpdateDTO;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing modules within projects.
 */
@RestController
@RequestMapping("/api/v2/projects/{projectId}/modules")
@RequiredArgsConstructor
public class CalculationModuleController {

    private final CalculationModuleService calculationModuleService;

    /**
     * Get all modules for a project.
     *
     * @param projectId the project ID
     * @return a list of all modules in the project
     */
    @GetMapping
    public ResponseEntity<List<CalculationModule>> getAllModules(@PathVariable String projectId) {
        return ResponseEntity.ok(calculationModuleService.getAllModules(projectId));
    }

    /**
     * Get a module by ID.
     *
     * @param projectId the project ID
     * @param moduleId  the module ID
     * @return the module, if found
     */
    @GetMapping("/{moduleId}")
    public ResponseEntity<CalculationModule> getModuleById(@PathVariable String projectId, @PathVariable String moduleId) {
        return calculationModuleService.getModuleById(projectId, moduleId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Create a new module in a project.
     *
     * @param projectId       the project ID
     * @param moduleCreateDTO the module to create
     * @return the created module
     */
    @PostMapping
    public ResponseEntity<CalculationModule> createModule(@PathVariable String projectId, @Valid @RequestBody ModuleCreateDTO moduleCreateDTO) {
        return new ResponseEntity<>(calculationModuleService.createModule(projectId, moduleCreateDTO), HttpStatus.CREATED);
    }

    /**
     * Update an existing module.
     *
     * @param projectId       the project ID
     * @param moduleId        the module ID
     * @param moduleUpdateDTO the updated module
     * @return the updated module
     */
    @PutMapping("/{moduleId}")
    public ResponseEntity<CalculationModule> updateModule(@PathVariable String projectId, @PathVariable String moduleId, @Valid @RequestBody ModuleUpdateDTO moduleUpdateDTO) {
        return calculationModuleService.updateModule(projectId, moduleId, moduleUpdateDTO)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Delete a module by ID.
     *
     * @param projectId the project ID
     * @param moduleId  the module ID
     * @return no content
     */
    @DeleteMapping("/{moduleId}")
    public ResponseEntity<Void> deleteModule(@PathVariable String projectId, @PathVariable String moduleId) {
        calculationModuleService.deleteModule(projectId, moduleId);
        return ResponseEntity.noContent().build();
    }

    /**
     * Update module params.
     *
     * @param projectId the project ID
     * @param moduleId  the module ID
     * @param params    the module params
     * @return the updated module
     */
    @PutMapping("/{moduleId}/params")
    public ResponseEntity<CalculationModule> updateModuleParams(@PathVariable String projectId, @PathVariable String moduleId, @Valid @RequestBody Object params) {
        return calculationModuleService.updateModuleParams(projectId, moduleId, params)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

}
