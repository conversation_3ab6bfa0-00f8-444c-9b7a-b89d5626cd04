package it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.mappers;

import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProductResolver;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input.ConcretePillarShearVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input.ConcretePillarShearVerifyExecutionInputImpl;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input.ConcretePillarShearVerifyInput;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ConcretePillarConfinementVerifyExecutionInputMapper {

    private final ReinforcedConcreteProductResolver reinforcedConcreteProductResolver;

    public ConcretePillarShearVerifyExecutionInput map(ConcretePillarShearVerifyInput input) {
        return new ConcretePillarShearVerifyExecutionInputImpl(
                reinforcedConcreteProductResolver.resolve(input.getProduct())
        );
    }
}

