package it.kimia.atlas.atlasservices.project;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import it.kimia.atlas.atlasservices.project.dtos.ProjectCreateDTO;
import it.kimia.atlas.atlasservices.project.dtos.ProjectUpdateDTO;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for managing projects.
 * Protected by Keycloak authentication.
 */
@RestController
@RequestMapping("/api/v2/projects")
@RequiredArgsConstructor
@Tag(name = "Projects", description = "APIs for managing projects")
public class ProjectController {

    private final ProjectService projectService;

    /**
     * Get all projects.
     *
     * @return a list of all projects
     */
    @Operation(summary = "Get all projects", description = "Retrieves a paginated list of all projects.")
    @ApiResponse(responseCode = "200", description = "Successfully retrieved the list of projects")
    @GetMapping
    public ResponseEntity<Page<Project>> getAllProjects(Pageable pageable) {
        return ResponseEntity.ok(projectService.getAllProjects(pageable));
    }


    /**
     * Get a project by ID.
     *
     * @param id the project ID
     * @return the project, if found
     */
    @Operation(summary = "Get a project by ID", description = "Retrieves a single project by its unique identifier.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the project"),
            @ApiResponse(responseCode = "404", description = "Project not found")
    })
    @GetMapping("/{id}")
    public ResponseEntity<Project> getProjectById(@PathVariable String id) {
        return projectService.getProjectById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Create a new project.
     *
     * @param projectDTO the project to create
     * @return the created project
     */
    @Operation(summary = "Create a new project", description = "Creates a new project with the provided data.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Project created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data provided")
    })
    @PostMapping
    public ResponseEntity<Project> createProject(@Valid @RequestBody ProjectCreateDTO projectDTO) {
        return new ResponseEntity<>(projectService.createProject(projectDTO), HttpStatus.CREATED);
    }

    /**
     * Update an existing project.
     *
     * @param id               the project ID
     * @param projectUpdateDTO the updated project
     * @return the updated project
     */
    @Operation(summary = "Update an existing project", description = "Updates an existing project identified by its ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Project updated successfully"),
            @ApiResponse(responseCode = "404", description = "Project not found"),
            @ApiResponse(responseCode = "400", description = "Invalid input data provided")
    })
    @PutMapping("/{id}")
    public ResponseEntity<Project> updateProject(@PathVariable String id, @Valid @RequestBody ProjectUpdateDTO projectUpdateDTO) {
        return projectService.updateProject(id, projectUpdateDTO)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Delete a project by ID.
     *
     * @param id the project ID
     * @return no content
     */
    @Operation(summary = "Delete a project", description = "Deletes a project by its unique identifier.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Project deleted successfully"),
            @ApiResponse(responseCode = "404", description = "Project not found")
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteProject(@PathVariable String id) {
        projectService.deleteProject(id);
        return ResponseEntity.noContent().build();
    }

}
