package it.kimia.atlas.atlasservices.product;

import it.kimia.atlas.atlasservices.product.dtos.ProductCreateDTO;
import it.kimia.atlas.atlasservices.product.dtos.ProductUpdateDTO;
import it.kimia.atlas.atlasservices.project.ProjectType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

/**
 * Service interface for managing products.
 */
public interface ProductService {

    /**
     * Get all products with pagination and optional filtering.
     *
     * @param name        optional name filter (partial match, case-insensitive)
     * @param category    optional category filter
     * @param productType optional product type filter
     * @param pageable    pagination information
     * @return a page of products matching the criteria
     */
    Page<Product> getProducts(
            String name,
            ProjectType category,
            String productType,
            Pageable pageable);

    /**
     * Get a product by ID.
     *
     * @param id the product ID
     * @return the product, if found
     */
    Optional<Product> getProductById(String id);

    /**
     * Create a new product.
     *
     * @param productDTO the product to create
     * @return the created product
     */
    Product createProduct(ProductCreateDTO productDTO);

    /**
     * Update an existing product.
     *
     * @param id               the product ID
     * @param productUpdateDTO the updated product data
     * @return the updated product, if found
     */
    Optional<Product> updateProduct(String id, ProductUpdateDTO productUpdateDTO);

    /**
     * Delete a product by ID.
     *
     * @param id the product ID
     */
    void deleteProduct(String id);
}