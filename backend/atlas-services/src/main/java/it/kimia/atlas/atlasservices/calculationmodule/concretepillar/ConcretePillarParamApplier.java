package it.kimia.atlas.atlasservices.calculationmodule.concretepillar;

import com.fasterxml.jackson.databind.ObjectMapper;
import it.kimia.atlas.atlasservices.calculationmodule.CalculationModule;
import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import it.kimia.atlas.atlasservices.calculationmodule.ParamApplier;
import it.kimia.atlas.atlasservices.calculationmodule.validator.ValidationException;
import it.kimia.atlas.atlasservices.calculationmodule.validator.model.ValidationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class ConcretePillarParamApplier implements ParamApplier {

    private final ConcretePillarParamsValidator validator;
    private final ObjectMapper objectMapper;
    private final ConcretePillarParamsMapper mapper;

    public ModuleType getModuleType() {
        return ModuleType.PILLAR;
    }

    public void apply(CalculationModule calculationModule, Object params) {

        if (calculationModule instanceof ConcretePillarModule module) {

            final ConcretePillarParams parsedParams = objectMapper.convertValue(params, ConcretePillarParams.class);

            ValidationResult validationResult = validator.validate(parsedParams);
            if (!validationResult.isValid()) {
                throw new ValidationException("Validation Failed", validationResult.getFieldErrors());
            }

            final ConcretePillarParams mergedParams = mapper.merge(module.getParams(), parsedParams);
            module.setParams(mergedParams);
            module.setFlexuralCalculationResult(null);
            module.setShearCalculationResult(null);
        } else {
            throw new IllegalArgumentException("Invalid module type: " + calculationModule.getType());
        }
    }
}
