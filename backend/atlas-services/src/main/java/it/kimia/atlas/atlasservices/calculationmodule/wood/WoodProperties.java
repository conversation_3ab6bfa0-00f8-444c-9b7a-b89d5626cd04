package it.kimia.atlas.atlasservices.calculationmodule.wood;

import it.kimia.atlas.atlasservices.materials.WoodCategory;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class WoodProperties {

    @NotNull(message = "validation.wood.properties.category.notnull")
    private WoodCategory category;

    @Positive(message = "validation.wood.properties.characteristicBendingStrength.positive")
    private BigDecimal characteristicBendingStrength;

    @Positive(message = "validation.wood.properties.characteristicShearStrength.positive")
    private BigDecimal characteristicShearStrength;

    @Positive(message = "validation.wood.properties.characteristicTensileStrength.positive")
    private BigDecimal characteristicTensileStrength;

    @Positive(message = "validation.wood.properties.characteristicCompressiveStrength.positive")
    private BigDecimal characteristicCompressiveStrength;

    @Positive(message = "validation.wood.properties.meanDensity.positive")
    private BigDecimal meanDensity;

    @Positive(message = "validation.wood.properties.meanShearModulus.positive")
    private BigDecimal meanShearModulus;

    @Positive(message = "validation.wood.properties.elasticityModulusParallelToGrain.positive")
    private BigDecimal elasticityModulusParallelToGrain;

    @Positive(message = "validation.wood.properties.meanElasticityModulus.positive")
    private BigDecimal meanElasticityModulus;

    @Positive(message = "validation.wood.properties.partialMaterialFactor.positive")
    private BigDecimal partialMaterialFactor;

}
