package it.kimia.atlas.atlasservices.calculationmodule.wood;

import com.sun.istack.NotNull;
import it.kimia.atlas.atlasservices.materials.WoodCategory;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.BigInteger;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class WoodProperties {

    private WoodCategory category;

    private BigDecimal characteristicBendingStrength;

    private BigDecimal characteristicShearStrength;

    private BigDecimal characteristicTensileStrength;

    private BigDecimal characteristicCompressiveStrength;

    private BigDecimal meanDensity;

    private BigDecimal meanShearModulus;

    private BigDecimal elasticityModulusParallelToGrain;

    private BigDecimal meanElasticityModulus;

    private BigDecimal partialMaterialFactor;

}
