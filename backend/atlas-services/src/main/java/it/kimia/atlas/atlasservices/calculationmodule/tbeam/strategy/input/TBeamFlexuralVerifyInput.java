package it.kimia.atlas.atlasservices.calculationmodule.tbeam.strategy.input;

import it.kimia.atlas.atlasservices.calculationmodule.dto.product.ConcreteCalculationProductInput;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Input for the FLEXURAL_VERIFY calculation strategy.
 * Contains parameters specific to flexural verification calculations.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TBeamFlexuralVerifyInput {

    /**
     * The strip width to verify against, in mm. bf [mm]
     */
    @NotNull(message = "validation.flexuralverify.stripWidth.notnull")
    @Positive(message = "validation.flexuralverify.stripWidth.positive")
    private int stripWidth;

    /**
     * The number of the layers to apply in calculations. nf [-]
     */
    @NotNull(message = "validation.flexuralverify.layersNumber.notnull")
    @Positive(message = "validation.flexuralverify.layersNumber.positive")
    private int layersNumber;

    /**
     * The bending moment to apply in calculations (msd). Msd  [kNm]
     */
    @NotNull(message = "validation.flexuralverify.bendingMoment.notnull")
    @Positive(message = "validation.flexuralverify.bendingMoment.positive")
    private int bendingMoment;

    /**
     * The product to be used in the calculation. This can be an existing product
     * from the database (specified by ID) or a custom product with user-defined properties.
     */
    @NotNull(message = "validation.flexuralverify.product.notnull")
    @Valid
    private ConcreteCalculationProductInput product;
}