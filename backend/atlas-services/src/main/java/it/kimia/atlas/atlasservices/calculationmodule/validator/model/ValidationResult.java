package it.kimia.atlas.atlasservices.calculationmodule.validator.model;

import it.kimia.atlas.atlasservices.shared.model.FieldErrorDTO;

import java.util.ArrayList;
import java.util.List;

public class ValidationResult {
    private final boolean valid;
    private final String errorMessage;
    private final List<FieldErrorDTO> fieldErrors;

    /**
     * Constructor for backward compatibility
     */
    public ValidationResult(boolean valid, String errorMessage) {
        this.valid = valid;
        this.errorMessage = errorMessage;
        this.fieldErrors = new ArrayList<>();
    }

    /**
     * Constructor with field errors
     */
    public ValidationResult(boolean valid, String errorMessage, List<FieldErrorDTO> fieldErrors) {
        this.valid = valid;
        this.errorMessage = errorMessage;
        this.fieldErrors = fieldErrors != null ? fieldErrors : new ArrayList<>();
    }

    /**
     * Factory method to create a valid result
     */
    public static ValidationResult valid() {
        return new ValidationResult(true, null);
    }

    /**
     * Factory method to create an invalid result with field errors
     */
    public static ValidationResult invalid(String errorMessage, List<FieldErrorDTO> fieldErrors) {
        return new ValidationResult(false, errorMessage, fieldErrors);
    }

    public boolean isValid() {
        return valid;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public List<FieldErrorDTO> getFieldErrors() {
        return fieldErrors;
    }
}