package it.kimia.atlas.atlasservices.product;

import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProduct;
import it.kimia.atlas.atlasservices.project.ProjectType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;

/**
 * Represents a product in the system.
 */
@Document(collection = "products")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Product implements ReinforcedConcreteProduct {

    @Id
    private String id;
    private String name;
    private List<ProjectType> categories;

    // Common properties for fiber-based products
    private FiberType fiberType;
    private BigDecimal thickness;
    private BigDecimal tensileStrength;
    private BigDecimal elasticModulus;

    // Documentation link
    private String documentationLink;

    // Product type (mesh, connector, mortar)
    private String productType;

    // Properties for mesh
    private Double ultimateStrain;

    // Properties for connectors
    private Double density;
    private Double maxResistance;
    private Double weight;
    private Double crossSectionArea;
    private Double diameter;
    private Double pullOutResistance;

    // Properties for mortar
    private Double designStrength;
    private OrientationEnum orientation;
    private int maxLayerNumber;
    private List<Integer> availableWidths;

    /**
     * Calculates the system deformation (ε = σ/E).
     * This method is safe against null values and division by zero.
     *
     * @return The calculated deformation as a BigDecimal, or BigDecimal.ZERO if input values are invalid.
     */
    public BigDecimal getSystemDeformation() {
        // 1. Check for null values or a zero divisor to prevent exceptions
        if (this.tensileStrength == null || this.elasticModulus.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal tensileStrengthBd = this.tensileStrength;
        BigDecimal elasticModulusBd = this.elasticModulus;

        return tensileStrengthBd.divide(elasticModulusBd, SCALE, RoundingMode.HALF_UP);
    }
}
