package it.kimia.atlas.atlasservices.calculationmodule.utility;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;
import java.util.function.Function;

public final class BisectionSolver {

    private BisectionSolver() {
    }

    /**
     * Overload generico: valuta T (es. FlexuralCalculationResult) e usa un estrattore per il residuo.
     * Comportamento identico a quello richiesto:
     * - midpoint = (lower+upper)/2 con scala 12 e HALF_UP
     * - scelta sottointervallo: fLower * fMid <= 0 -> ramo sinistro
     * - ritorno: RIVALUTA eval(mid) prima di restituire
     */
    public static <T> T solveByBisection(
            BigDecimal lower,
            BigDecimal upper,
            BigDecimal tolerance,
            int maxIterations,
            Function<BigDecimal, T> eval,
            Function<T, BigDecimal> residualExtractor,
            java.util.function.BiConsumer<Integer, String> logger // può essere (i,msg) -> log.info(msg)
    ) {
        Objects.requireNonNull(lower);
        Objects.requireNonNull(upper);
        Objects.requireNonNull(tolerance);
        Objects.requireNonNull(eval);
        Objects.requireNonNull(residualExtractor);

        // Valutazioni ai capi
        final T lres = eval.apply(lower);
        BigDecimal fLower = residualExtractor.apply(lres);

        final T ures = eval.apply(upper);
        BigDecimal fUpper = residualExtractor.apply(ures);

        if (fLower.multiply(fUpper).compareTo(BigDecimal.ZERO) > 0) {
            throw new IllegalStateException("Bisezione: la funzione non cambia segno nell'intervallo.");
        }

        BigDecimal a = lower;
        BigDecimal b = upper;
        BigDecimal fa = fLower;
        BigDecimal fb = fUpper;

        for (int i = 0; i < maxIterations; i++) {
            BigDecimal mid = a.add(b).divide(BigDecimal.valueOf(2), 12, RoundingMode.HALF_UP);
            final T mres = eval.apply(mid);
            BigDecimal fMid = residualExtractor.apply(mres);

            if (logger != null) {
                logger.accept(i, String.format("iter %d: lower=%s upper=%s", i, a.toPlainString(), b.toPlainString()));
            }

            // Criteri di arresto
            if (fMid.abs().compareTo(tolerance) < 0 ||
                    b.subtract(a).abs().compareTo(tolerance) < 0) {
                // RIVALUTA come nel codice originale prima di tornare
                return eval.apply(mid);
            }

            // Scelta sottointervallo (<=)
            if (fa.multiply(fMid).compareTo(BigDecimal.ZERO) <= 0) {
                b = mid;
                fb = fMid;
            } else {
                a = mid;
                fa = fMid;
            }
        }

        // Max iter: ritorna valutazione al punto medio (come l’originale al termine)
        final BigDecimal mid = a.add(b).divide(BigDecimal.valueOf(2), 12, RoundingMode.HALF_UP);
        return eval.apply(mid);
    }

    /**
     * Overload compatibile: accetta direttamente la tua lambda `equilibrium(...)` che restituisce T,
     * e assume che il residuo sia ottenuto tramite `residualGetter`.
     */
    public static <T> T solveByBisection(
            BigDecimal lower,
            BigDecimal upper,
            BigDecimal tolerance,
            int maxIterations,
            Function<BigDecimal, T> eval,
            Function<T, BigDecimal> residualGetter
    ) {
        return solveByBisection(lower, upper, tolerance, maxIterations, eval, residualGetter, null);
    }
}