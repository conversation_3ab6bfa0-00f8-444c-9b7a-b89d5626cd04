package it.kimia.atlas.atlasservices.calculationmodule.concretepillar;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring")
public interface ConcretePillarParamsMapper {

    /**
     * Metodo pubblico che unisce due istanze di ConcretePillarParams.
     * - Se 'existing' è nullo, crea una copia completa di 'updates'.
     * - Se 'existing' non è nullo, aggiorna 'existing' con i valori non nulli di 'updates'.
     *
     * @param existing L'oggetto da aggiornare (può essere nullo).
     * @param updates  L'oggetto con i nuovi dati.
     * @return L'oggetto unito.
     */
    default ConcretePillarParams merge(ConcretePillarParams existing, ConcretePillarParams updates) {
        if (updates == null) {
            return existing;
        }
        if (existing == null) {
            return clone(updates);
        }
        return update(existing, updates);
    }

    ConcretePillarParams clone(ConcretePillarParams source);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    ConcretePillarParams update(@MappingTarget ConcretePillarParams existing, ConcretePillarParams updates);
}