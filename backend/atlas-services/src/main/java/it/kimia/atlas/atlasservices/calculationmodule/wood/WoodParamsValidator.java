package it.kimia.atlas.atlasservices.calculationmodule.wood;

import it.kimia.atlas.atlasservices.calculationmodule.validator.model.ValidationResult;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * Validator for WoodParams.
 * <p>
 * This class validates WoodParams objects using Jakarta Bean Validation.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class WoodParamsValidator {

    private final Validator validator;

    /**
     * Validates the given WoodParams object.
     *
     * @param params the WoodParams to validate
     * @return ValidationResult containing validation status and any errors
     */
    public ValidationResult validate(WoodParams params) {
        if (params == null) {
            log.warn("WoodParams is null");
            return ValidationResult.invalid("WoodParams cannot be null");
        }

        Set<ConstraintViolation<WoodParams>> violations = validator.validate(params);
        
        if (violations.isEmpty()) {
            log.debug("WoodParams validation successful");
            return ValidationResult.valid();
        }

        log.warn("WoodParams validation failed with {} violations", violations.size());
        return ValidationResult.fromViolations(violations);
    }
}
