package it.kimia.atlas.atlasservices.calculationmodule.wood;

import it.kimia.atlas.atlasservices.calculationmodule.validator.model.ValidationResult;
import it.kimia.atlas.atlasservices.shared.model.FieldErrorDTO;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * Validator for WoodParams.
 * <p>
 * This class validates WoodParams objects using Jakarta Bean Validation.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class WoodParamsValidator {

    private final Validator validator;

    /**
     * Validates the given WoodParams object.
     *
     * @param params the WoodParams to validate
     * @return ValidationResult containing validation status and any errors
     */
    public ValidationResult validate(WoodParams params) {
        if (params == null) {
            log.warn("WoodParams is null");
            List<FieldErrorDTO> fieldErrors = List.of(
                new FieldErrorDTO("params", "notnull", "WoodParams cannot be null")
            );
            return ValidationResult.invalid("WoodParams cannot be null", fieldErrors);
        }

        Set<ConstraintViolation<WoodParams>> violations = validator.validate(params);

        if (violations.isEmpty()) {
            log.debug("WoodParams validation successful");
            return ValidationResult.valid();
        }

        log.warn("WoodParams validation failed with {} violations", violations.size());

        // Convert violations to FieldErrorDTO objects
        List<FieldErrorDTO> fieldErrors = new ArrayList<>();
        for (ConstraintViolation<WoodParams> violation : violations) {
            String fieldName = violation.getPropertyPath().toString();
            String errorCode = violation.getMessage();
            String defaultMessage = "Validation failed for field: " + fieldName;

            fieldErrors.add(new FieldErrorDTO(fieldName, errorCode, defaultMessage));
        }

        return ValidationResult.invalid("Validation Failed", fieldErrors);
    }
}
