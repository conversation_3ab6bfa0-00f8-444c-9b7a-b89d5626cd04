package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.dto.MaterialProperties;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public interface MaterialStrengthService {

    BigDecimal getDesignYieldStrengthForDuctileMechanisms(final MaterialProperties materialProperties);

    // fyd,f = fyk / (FC * γs,f ) [Mpa]	Design yield strength - brittle mechanisms
    BigDecimal getDesignYieldStrengthForBrittleMechanisms(final MaterialProperties materialProperties);

    // fcd,d = fcm / (FC * γc,d ) [Mpa]	Design compressive strength - ductile mechanisms
    BigDecimal getDesignCompressiveStrengthForDuctileMechanisms(final MaterialProperties materialProperties);

    // fcd,d = fcm / (FC * γc,d ) [Mpa]	Design compressive strength - brittle mechanisms
    BigDecimal getDesignCompressiveStrengthForBrittleMechanisms(final MaterialProperties materialProperties);
}