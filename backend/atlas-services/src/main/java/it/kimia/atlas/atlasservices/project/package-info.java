/**
 * This package represents the Project component in the Atlas Services application.
 * 
 * It provides functionality for managing projects, including:
 * - Project entity definition
 * - Project repository for data access
 * - Project service for business logic
 * - Project controller for REST API endpoints
 * 
 * This package is designed to be accessible by all other components in the application.
 */
package it.kimia.atlas.atlasservices.project;
