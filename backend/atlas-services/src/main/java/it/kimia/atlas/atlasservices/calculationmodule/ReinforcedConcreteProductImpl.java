package it.kimia.atlas.atlasservices.calculationmodule;

import it.kimia.atlas.atlasservices.product.FiberType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReinforcedConcreteProductImpl implements ReinforcedConcreteProduct {

    private String id;

    private String name;

    private BigDecimal thickness;

    private BigDecimal elasticModulus;

    private BigDecimal tensileStrength;

    private FiberType fiberType;

    /**
     * Calculates the system deformation (ε = σ/E).
     * This method is safe against null values and division by zero.
     *
     * @return The calculated deformation as a BigDecimal, or BigDecimal.ZERO if input values are invalid.
     */
    public BigDecimal getSystemDeformation() {
        // 1. Check for null values or a zero divisor to prevent exceptions
        if (this.tensileStrength == null || this.elasticModulus.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal tensileStrengthBd = this.tensileStrength;
        BigDecimal elasticModulusBd = this.elasticModulus;

        return tensileStrengthBd.divide(elasticModulusBd, SCALE, RoundingMode.HALF_UP);
    }
}

