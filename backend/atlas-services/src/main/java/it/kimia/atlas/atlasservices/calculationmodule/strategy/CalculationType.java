package it.kimia.atlas.atlasservices.calculationmodule.strategy;

/**
 * Defines the types of calculations that can be performed on modules.
 * <p>
 * This enum is used to identify different calculation strategies.
 */
public enum CalculationType {
    /**
     * Flexural verification calculation.
     */
    FLEXURAL_VERIFY,

    /**
     * Shear verification calculation.
     */
    SHEAR_VERIFY,

    /**
     * Confinement verification calculation.
     */
    CONFINEMENT_VERIFY
}