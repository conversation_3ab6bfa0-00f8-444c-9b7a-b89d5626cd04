package it.kimia.atlas.atlasservices.calculationmodule.concretepillar;

import it.kimia.atlas.atlasservices.calculationmodule.CalculationModule;
import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input.ConcretePillarFlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input.ConcretePillarShearVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.output.ConcretePillarFlexuralCalculationResult;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.output.ConcretePillarShearCalculationResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * T-shaped beam calculation module.
 * <p>
 * This module is used for calculations on T-shaped beams.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ConcretePillarModule extends CalculationModule {
    
    public static ModuleType moduleType = ModuleType.PILLAR;

    private ConcretePillarParams params;

    private ConcretePillarFlexuralVerifyExecutionInput flexuralVerifyExecutionInput;
    private ConcretePillarShearVerifyExecutionInput shearVerifyExecutionInput;
    private ConcretePillarFlexuralCalculationResult flexuralCalculationResult;
    private ConcretePillarShearCalculationResult shearCalculationResult;

    @Override
    public ModuleType getType() {
        return moduleType;
    }
}