package it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.factory;

import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.ConcretePillarModule;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationStrategy;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationType;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.factory.CalculationStrategyFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class ConcretePillarStrategyFactory implements CalculationStrategyFactory<ConcretePillarModule> {

    private final Map<CalculationType, CalculationStrategy<ConcretePillarModule, ?>> strategies;

    public ConcretePillarStrategyFactory(List<CalculationStrategy<ConcretePillarModule, ?>> rectangularBeamStrategies) {
        this.strategies = rectangularBeamStrategies.stream()
                .collect(Collectors.toMap(CalculationStrategy::getCalculationType, strategy -> strategy));
    }

    @Override
    public Optional<CalculationStrategy<ConcretePillarModule, ?>> createStrategy(CalculationType calculationType) {
        return Optional.ofNullable(strategies.get(calculationType));
    }

    @Override
    public ModuleType getModuleType() {
        return ModuleType.PILLAR;
    }
}