// GeometricCoefficientService.java
package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamGeometry;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInput;

import java.math.BigDecimal;

public interface GeometricCoefficientService {

    BigDecimal calculateGeometricCorrectionCoefficient(RectangularBeamGeometry geometry, FlexuralVerifyExecutionInput input);
}