package it.kimia.atlas.atlasservices.calculationmodule.strategy.factory;

import it.kimia.atlas.atlasservices.calculationmodule.CalculationModule;
import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationStrategy;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationType;

import java.util.Optional;

/**
 * Abstract Factory for creating calculation strategies for a specific module type.
 * Each concrete factory is responsible for creating strategies for a single type of module.
 *
 * @param <T> The specific type of CalculationModule this factory handles.
 */
public interface CalculationStrategyFactory<T extends CalculationModule> {

    /**
     * Creates a calculation strategy for the given calculation type.
     *
     * @param calculationType The type of calculation.
     * @return An Optional containing the typed strategy if found, otherwise empty.
     */
    Optional<CalculationStrategy<T, ?>> createStrategy(CalculationType calculationType);

    /**
     * @return The ModuleType this factory is responsible for.
     */
    ModuleType getModuleType();
}