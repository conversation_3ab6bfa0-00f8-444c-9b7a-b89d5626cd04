package it.kimia.atlas.atlasservices.materials;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for managing Wood entities.
 */
@Repository
public interface WoodRepository extends MongoRepository<Wood, String> {

    /**
     * Find a wood material by its name.
     *
     * @param name the name of the wood material (e.g., "C14", "D18", "GL20h")
     * @return the wood material if found
     */
    Optional<Wood> findByName(String name);

    /**
     * Find a wood material by its name (case-insensitive).
     *
     * @param name the name of the wood material (e.g., "C14", "D18", "GL20h")
     * @return the wood material if found
     */
    Optional<Wood> findByNameIgnoreCase(String name);

    /**
     * Get all wood material names.
     *
     * @return list of all wood material names
     */
    @Query(value = "{}", fields = "{ 'name' : 1, '_id' : 0 }")
    List<String> findAllNames();

}