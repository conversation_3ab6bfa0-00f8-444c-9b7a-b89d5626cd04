package it.kimia.atlas.atlasservices.materials;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Represents a steel grade material in the system.
 */
@Document(collection = "steel_grades")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SteelGrade {

    @Id
    private String id;

    /**
     * The name of the steel grade (e.g., "Aq42", "Aq50").
     */
    private String name;

    /**
     * The characteristic yield strength in MPa.
     */
    private Integer yieldStrength;

    /**
     * The characteristic tensile strength in MPa.
     */
    private Integer tensileStrength;

    /**
     * The characteristic elongation percentage.
     */
    private Double elongationPercentage;

    /**
     * The characteristic elastic modulus in MPa.
     */
    private Integer elasticModulus;
}