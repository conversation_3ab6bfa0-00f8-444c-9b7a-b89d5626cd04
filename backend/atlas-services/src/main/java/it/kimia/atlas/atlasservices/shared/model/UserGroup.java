package it.kimia.atlas.atlasservices.shared.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Represents a group of users in the system.
 * This class is in the shared package to be accessible by multiple modules.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserGroup {
    private String name;
    private List<String> userIds;
}