package it.kimia.atlas.atlasservices.materials;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST controller for managing reinforced concrete materials.
 * Protected by Keycloak authentication.
 */
@RestController
@RequestMapping("/api/v2/materials/reinforced-concretes")
@PreAuthorize("isAuthenticated()")
@RequiredArgsConstructor
@Tag(name = "Materials - Reinforced Concrete", description = "APIs for managing reinforced concrete materials")
public class ReinforcedConcreteController {

    private final ReinforcedConcreteService reinforcedConcreteService;

    /**
     * Get all reinforced concretes with pagination and optional filtering.
     *
     * @param pageable pagination information
     * @return a page of reinforced concretes matching the criteria
     */
    @Operation(summary = "Get all reinforced concretes", description = "Retrieves a paginated list of all reinforced concrete materials.")
    @ApiResponse(responseCode = "200", description = "Successfully retrieved the list of reinforced concretes")
    @GetMapping
    public ResponseEntity<Page<ReinforcedConcrete>> getReinforcedConcretes(
            Pageable pageable) {
        return ResponseEntity.ok(reinforcedConcreteService.getReinforcedConcretes(pageable));
    }

    /**
     * Get a reinforced concrete by ID.
     *
     * @param id the reinforced concrete ID
     * @return the reinforced concrete, if found
     */
    @Operation(summary = "Get a reinforced concrete by ID", description = "Retrieves a single reinforced concrete by its unique identifier.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the reinforced concrete"),
            @ApiResponse(responseCode = "404", description = "Reinforced concrete not found")
    })
    @GetMapping("/{id}")
    public ResponseEntity<ReinforcedConcrete> getReinforcedConcreteById(
            @Parameter(description = "ID of the reinforced concrete to be retrieved") @PathVariable String id) {
        return reinforcedConcreteService.getReinforcedConcreteById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

}
