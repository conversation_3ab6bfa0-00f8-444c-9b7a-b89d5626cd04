package it.kimia.atlas.atlasservices.calculationmodule;

public enum Exposure {

    /**
     * For components or services that are only accessible within the internal network or system.
     * They are not exposed to the public internet and are meant for private consumption.
     * Corresponds to "Interna".
     */
    INTERNAL,

    /**
     * For components or services that are publicly accessible from the internet.
     * These endpoints are open to the public or external partners.
     * Corresponds to "Esterna".
     */
    EXTERNAL,

    /**
     * For components or services exposed to a high-threat, untrusted, or "aggressive" environment.
     * This implies that the component requires heightened security measures, monitoring, and validation,
     * as it might be a primary target for attacks.
     * Corresponds to "Aggressiva".
     */
    AGGRESSIVE
}
