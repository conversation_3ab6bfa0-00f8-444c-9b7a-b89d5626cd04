package it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.strategy;

import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.ConcretePillarParams;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input.ConcretePillarFlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.output.ConcretePillarFlexuralCalculationResult;

public interface ConcretePillarFlexuralVerify {
    ConcretePillarFlexuralCalculationResult execute(final ConcretePillarParams params,
                                                    final ConcretePillarFlexuralVerifyExecutionInput input);
}
