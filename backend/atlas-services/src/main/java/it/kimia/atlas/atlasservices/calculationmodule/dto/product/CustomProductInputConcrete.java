package it.kimia.atlas.atlasservices.calculationmodule.dto.product;

import it.kimia.atlas.atlasservices.product.FiberType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Represents a custom product with user-defined properties for a calculation.
 * These properties are a subset of the main Product entity relevant for flexural verification.
 * Use sourceType: "CUSTOM" in the JSON payload.
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class CustomProductInputConcrete extends ConcreteCalculationProductInput {

    /**
     * custom id
     */
    @NotBlank(message = "validation.product.id.notblank")
    private String id;

    @NotBlank(message = "validation.product.name.notblank")
    private String name;

    @NotNull(message = "validation.product.thickness.notnull")
    @Positive(message = "validation.product.thickness.positive")
    private BigDecimal thickness;

    @NotNull(message = "validation.product.tensileStrength.notnull")
    @Positive(message = "validation.product.tensileStrength.positive")
    private BigDecimal tensileStrength;

    @NotNull(message = "validation.product.elasticModulus.notnull")
    @Positive(message = "validation.product.elasticModulus.positive")
    private BigDecimal elasticModulus;

    @NotNull(message = "validation.product.fiberType.notnull")
    private FiberType fiberType;
/*
    @NotNull(message = "validation.product.ultimateStrain.notnull")
    @Positive(message = "validation.product.ultimateStrain.positive")
    private Double density;

    @Positive(message = "validation.product.maxResistance.positive")
    private Double maxResistance;

    @Positive(message = "validation.product.weight.positive")
    private Double weight;

    @Positive(message = "validation.product.crossSectionArea.positive")
    private Double crossSectionArea;

    @Positive(message = "validation.product.diameter.positive")
    private Double diameter;

    @Positive(message = "validation.product.pullOutResistance.positive")
    private Double pullOutResistance;

    @Positive(message = "validation.product.designStrength.positive")
    private Double designStrength;*/
}