package it.kimia.atlas.atlasservices.calculationmodule.wood;

import com.fasterxml.jackson.annotation.JsonIgnore;
import it.kimia.atlas.atlasservices.calculationmodule.wood.strategy.enums.ExpositionType;
import it.kimia.atlas.atlasservices.product.Product;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WoodCompositeGeometry {

    private String productId;

    @JsonIgnore
    private Product product;

    private BigDecimal stripWidth;

    private BigDecimal equivalentThickness;

    private BigDecimal layersNumber;

    private ExpositionType expositionType;

    private BigDecimal environmentalConversionFactor;

}
