package it.kimia.atlas.atlasservices.calculationmodule.validator;

import it.kimia.atlas.atlasservices.calculationmodule.validator.model.ValidationResult;

public interface ParamsValidator {
    /**
     * Validates parameters for a calculation module.
     * 
     * @param params the parameters to validate, can be a Map<String, Object> or a specific params object
     * @return a validation result containing whether the validation passed and any error messages
     */
    ValidationResult validate(Object params);
}
