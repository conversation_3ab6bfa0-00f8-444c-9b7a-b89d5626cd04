package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.factory;

import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamModule;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationStrategy;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationType;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.factory.CalculationStrategyFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class RectangularBeamStrategyFactory implements CalculationStrategyFactory<RectangularBeamModule> {

    private final Map<CalculationType, CalculationStrategy<RectangularBeamModule, ?>> strategies;

    public RectangularBeamStrategyFactory(List<CalculationStrategy<RectangularBeamModule, ?>> rectangularBeamStrategies) {
        this.strategies = rectangularBeamStrategies.stream()
                .collect(Collectors.toMap(CalculationStrategy::getCalculationType, strategy -> strategy));
    }

    @Override
    public Optional<CalculationStrategy<RectangularBeamModule, ?>> createStrategy(CalculationType calculationType) {
        return Optional.ofNullable(strategies.get(calculationType));
    }

    @Override
    public ModuleType getModuleType() {
        return ModuleType.RECTANGULAR_BEAM;
    }
}