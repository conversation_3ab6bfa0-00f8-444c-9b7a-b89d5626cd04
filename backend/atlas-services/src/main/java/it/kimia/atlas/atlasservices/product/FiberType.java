package it.kimia.atlas.atlasservices.product;

import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Arrays;

/**
 * Rappresenta i tipi di materiale utilizzati nei calcoli, corrispondenti alla cella C32 del foglio Excel.
 */
public enum FiberType {
    BASALT("basalt"),
    CARBON("carbon"),
    GALVANIZED_STEEL("galvanized_steel"),
    STEEL("steel"),
    GLASS("glass"),
    PREFORMED_CARBON("preformed_carbon"),
    UNKNOWN("unknown");

    private final String value;

    FiberType(String value) {
        this.value = value;
    }

    public static FiberType fromValue(String text) {
        return Arrays.stream(values())
                .filter(b -> b.value.equalsIgnoreCase(text))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("FiberType not found: " + text));
    }

    @JsonValue
    public String getValue() {
        return value;
    }
}