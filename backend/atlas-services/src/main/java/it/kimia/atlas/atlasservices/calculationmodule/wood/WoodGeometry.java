package it.kimia.atlas.atlasservices.calculationmodule.wood;

import it.kimia.atlas.atlasservices.calculationmodule.wood.strategy.enums.WoodGeometryLoadDuration;
import it.kimia.atlas.atlasservices.calculationmodule.wood.strategy.enums.WoodGeometryServiceClass;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class WoodGeometry {

    //part :- 1
    private BigDecimal beamSectionWidth;

    private BigDecimal beamSectionHeight;

    private BigDecimal beamSpacing;

    private BigDecimal beamSpan;

    private BigDecimal sectionModulus;

    private BigDecimal inertiaMomentAboutY;

    //part :- 2

    private WoodGeometryServiceClass serviceClass;

    private WoodGeometryLoadDuration loadDuration;

    private BigDecimal correctionFactor;

    private BigDecimal deformabilityFactor;

    private BigDecimal designBendingStrength;

    private BigDecimal designShearStrength;

    private BigDecimal elasticityInstantaneousModulus;

    private BigDecimal longTermElasticityModulus;

    private BigDecimal airRelativeHumidity;

}
