package it.kimia.atlas.atlasservices.calculationmodule.wood;

import it.kimia.atlas.atlasservices.calculationmodule.wood.strategy.enums.WoodGeometryLoadDuration;
import it.kimia.atlas.atlasservices.calculationmodule.wood.strategy.enums.WoodGeometryServiceClass;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class WoodGeometry {

    //part :- 1
    @Positive(message = "validation.wood.geometry.beamSectionWidth.positive")
    private BigDecimal beamSectionWidth;

    @Positive(message = "validation.wood.geometry.beamSectionHeight.positive")
    private BigDecimal beamSectionHeight;

    @Positive(message = "validation.wood.geometry.beamSpacing.positive")
    private BigDecimal beamSpacing;

    @Positive(message = "validation.wood.geometry.beamSpan.positive")
    private BigDecimal beamSpan;

    @Positive(message = "validation.wood.geometry.sectionModulus.positive")
    private BigDecimal sectionModulus;

    @Positive(message = "validation.wood.geometry.inertiaMomentAboutY.positive")
    private BigDecimal inertiaMomentAboutY;

    //part :- 2

    @NotNull(message = "validation.wood.geometry.serviceClass.notnull")
    private WoodGeometryServiceClass serviceClass;

    @NotNull(message = "validation.wood.geometry.loadDuration.notnull")
    private WoodGeometryLoadDuration loadDuration;

    @Positive(message = "validation.wood.geometry.correctionFactor.positive")
    private BigDecimal correctionFactor;

    @Positive(message = "validation.wood.geometry.deformabilityFactor.positive")
    private BigDecimal deformabilityFactor;

    @Positive(message = "validation.wood.geometry.designBendingStrength.positive")
    private BigDecimal designBendingStrength;

    @Positive(message = "validation.wood.geometry.designShearStrength.positive")
    private BigDecimal designShearStrength;

    @Positive(message = "validation.wood.geometry.elasticityInstantaneousModulus.positive")
    private BigDecimal elasticityInstantaneousModulus;

    @Positive(message = "validation.wood.geometry.longTermElasticityModulus.positive")
    private BigDecimal longTermElasticityModulus;

    @PositiveOrZero(message = "validation.wood.geometry.airRelativeHumidity.positiveOrZero")
    private BigDecimal airRelativeHumidity;

}
