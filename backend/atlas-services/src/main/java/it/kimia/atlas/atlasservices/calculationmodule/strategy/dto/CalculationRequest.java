package it.kimia.atlas.atlasservices.calculationmodule.strategy.dto;

import it.kimia.atlas.atlasservices.calculationmodule.strategy.CalculationType;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Data Transfer Object for calculation requests.
 * <p>
 * This class represents a request to execute a calculation on a module.
 * It contains the calculation type and the input for the calculation.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CalculationRequest {

    /**
     * The type of calculation to execute.
     */
    @NotNull(message = "validation.calculationrequest.calculationtype.notnull")
    private CalculationType calculationType;

    /**
     * The input for the calculation.
     * This can be any object that the strategy can handle.
     */
    private Object input;
}