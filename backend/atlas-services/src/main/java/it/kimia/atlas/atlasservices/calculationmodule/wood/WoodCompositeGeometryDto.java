package it.kimia.atlas.atlasservices.calculationmodule.wood;

import it.kimia.atlas.atlasservices.calculationmodule.wood.strategy.enums.ExpositionType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WoodCompositeGeometryDto {

    private String productId;
    private BigDecimal stripWidth;
    private BigDecimal equivalentThickness;
    private BigDecimal layersNumber;
    private ExpositionType expositionType;
    private BigDecimal environmentalConversionFactor;

}
