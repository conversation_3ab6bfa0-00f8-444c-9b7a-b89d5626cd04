package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.dto.MaterialProperties;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;


/**
 * First implementation but this calculation has being moved to frontend
 */
@Service
public class MaterialStrengthCalculatorService implements MaterialStrengthService {

    public static final BigDecimal BRITTLE_MECHANISM_YIELD_CONSTANT = BigDecimal.valueOf(1.15);
    public static final BigDecimal BRITTLE_MECHANISM_COMPRESSIVE_CONSTANT = BigDecimal.valueOf(1.5);

    public BigDecimal getDesignYieldStrengthForDuctileMechanisms(final MaterialProperties materialProperties) {
        var kl = materialProperties.getKnowledgeLevel();
        var yieldStrength = materialProperties.getSteelGrade().getYieldStrength();
        return yieldStrength
                .divide(kl.getValue(), SCALE, RoundingMode.HALF_UP);
    }

    // fyd,f = fyk / (FC * γs,f ) [Mpa]	Design yield strength - brittle mechanisms
    public BigDecimal getDesignYieldStrengthForBrittleMechanisms(final MaterialProperties materialProperties) {
        var kl = materialProperties.getKnowledgeLevel();
        var yieldStrength = materialProperties.getSteelGrade().getYieldStrength();
        return yieldStrength
                .divide(kl.getValue(), SCALE, RoundingMode.HALF_UP)
                .divide(BRITTLE_MECHANISM_YIELD_CONSTANT, SCALE, RoundingMode.HALF_UP);
    }

    // fcd,d = fcm / (FC * γc,d ) [Mpa]	Design compressive strength - ductile mechanisms
    public BigDecimal getDesignCompressiveStrengthForDuctileMechanisms(final MaterialProperties materialProperties) {
        var kl = materialProperties.getKnowledgeLevel();
        var averageCompressiveStrength = materialProperties.getConcreteClass().getAverageCompressiveStrength();
        return averageCompressiveStrength
                .divide(kl.getValue(), SCALE, RoundingMode.HALF_UP);
    }

    // fcd,d = fcm / (FC * γc,d ) [Mpa]	Design compressive strength - brittle mechanisms
    public BigDecimal getDesignCompressiveStrengthForBrittleMechanisms(final MaterialProperties materialProperties) {
        var kl = materialProperties.getKnowledgeLevel();
        var averageCompressiveStrength = materialProperties.getConcreteClass().getAverageCompressiveStrength();
        return averageCompressiveStrength
                .divide(kl.getValue(), SCALE, RoundingMode.HALF_UP)
                .divide(BRITTLE_MECHANISM_COMPRESSIVE_CONSTANT, SCALE, RoundingMode.HALF_UP);
    }
}