package it.kimia.atlas.atlasservices.project;

import it.kimia.atlas.atlasservices.project.dtos.ProjectCreateDTO;
import it.kimia.atlas.atlasservices.project.dtos.ProjectUpdateDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.AccessDeniedException;

import java.util.Optional;


public interface ProjectService {

    /**
     * Get all projects visible to the current user.
     *
     * @return a list of all projects visible to the current user
     */
    Page<Project> getAllProjects(Pageable pageable);

    /**
     * Get a project by ID if it's visible to the current user.
     *
     * @param id the project ID
     * @return the project, if found and visible to the current user
     * @throws AccessDeniedException if the project is not visible to the current user
     */
    Optional<Project> getProjectById(String id);


    /**
     * Create a new project for the current user.
     *
     * @param projectDTO the project to create
     * @return the created project
     */
    Project createProject(ProjectCreateDTO projectDTO);

    /**
     * Update an existing project if it's visible to the current user.
     *
     * @param id      the project ID
     * @param project the updated project
     * @return the updated project, if found and visible to the current user
     * @throws AccessDeniedException if the project is not visible to the current user
     */
    Optional<Project> updateProject(String id, ProjectUpdateDTO projectUpdateDTO);

    /**
     * Delete a project by ID if it's visible to the current user.
     *
     * @param id the project ID
     * @throws AccessDeniedException if the project is not visible to the current user
     */
    void deleteProject(String id);

}
