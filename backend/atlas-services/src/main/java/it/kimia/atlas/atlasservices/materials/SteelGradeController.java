package it.kimia.atlas.atlasservices.materials;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST controller for managing steel grade materials.
 * Protected by Keycloak authentication.
 */
@RestController
@RequestMapping("/api/v2/materials/steel-grades")
@PreAuthorize("isAuthenticated()")
@RequiredArgsConstructor
@Tag(name = "Materials - Steel Grades", description = "APIs for managing steel grade materials")
public class SteelGradeController {

    private final SteelGradeService steelGradeService;

    /**
     * Get all steel grades with pagination and optional filtering.
     *
     * @param pageable pagination information
     * @return a page of steel grades matching the criteria
     */
    @Operation(summary = "Get all steel grades", description = "Retrieves a paginated list of all steel grade materials.")
    @ApiResponse(responseCode = "200", description = "Successfully retrieved the list of steel grades")
    @GetMapping
    public ResponseEntity<Page<SteelGrade>> getSteelGrades(
            Pageable pageable) {
        return ResponseEntity.ok(steelGradeService.getSteelGrades(pageable));
    }

    /**
     * Get a steel grade by ID.
     *
     * @param id the steel grade ID
     * @return the steel grade, if found
     */
    @Operation(summary = "Get a steel grade by ID", description = "Retrieves a single steel grade by its unique identifier.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the steel grade"),
            @ApiResponse(responseCode = "404", description = "Steel grade not found")
    })
    @GetMapping("/{id}")
    public ResponseEntity<SteelGrade> getSteelGradeById(
            @Parameter(description = "ID of the steel grade to be retrieved") @PathVariable String id) {
        return steelGradeService.getSteelGradeById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

}