package it.kimia.atlas.atlasservices.calculationmodule.tbeam.strategy.input;

import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProductResolver;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class TBeamFlexuralVerifyExecutionInputMapper {

    private final ReinforcedConcreteProductResolver reinforcedConcreteProductResolver;

    public TBeamFlexuralVerifyExecutionInput map(TBeamFlexuralVerifyInput input) {
        return new TBeamFlexuralVerifyExecutionInputImpl(
                input.getStripWidth(),
                input.getLayersNumber(),
                input.getBendingMoment(),
                reinforcedConcreteProductResolver.resolve(input.getProduct())
        );
    }
}

