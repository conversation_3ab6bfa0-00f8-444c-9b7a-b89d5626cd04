package it.kimia.atlas.atlasservices.calculationmodule.dto;

import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RebarsDefinition {
    @Positive(message = "validation.rebars.diameter.positive")
    private int diameter;

    @Positive(message = "validation.rebars.quantity.positive")
    private int quantity;

    @Positive(message = "validation.rebars.area.positive")
    private BigDecimal area;
}
