package it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.input;

import it.kimia.atlas.atlasservices.calculationmodule.dto.product.ConcreteCalculationProductInput;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Input for the FLEXURAL_VERIFY calculation strategy for Concrete Pillar.
 * Contains parameters specific to flexural verification calculations.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConcretePillarFlexuralVerifyInput {

    /**
     * The strip width to verify against, in mm. bf [mm]
     */
    @NotNull(message = "validation.flexuralverify.stripWidth.notnull")
    @Positive(message = "validation.flexuralverify.stripWidth.positive")
    private int stripWidth;

    /**
     * The number of the layers to apply in calculations. nf [-]
     */
    @NotNull(message = "validation.flexuralverify.layersNumber.notnull")
    @Positive(message = "validation.flexuralverify.layersNumber.positive")
    private int layersNumber;

    /**
     * Applied axial force. Nsd  [kN]
     */
    @NotNull(message = "validation.flexuralverify.appliedAxialForce.notnull")
    @Positive(message = "validation.flexuralverify.appliedAxialForce.positive")
    private BigDecimal appliedAxialForce;

    /**
     * The bending moment to apply in calculations (msd). Msd [kNm]
     */
    @NotNull(message = "validation.flexuralverify.bendingMoment.notnull")
    @Positive(message = "validation.flexuralverify.bendingMoment.positive")
    private BigDecimal bendingMoment;

    /**
     * The product to be used in the calculation. This can be an existing product
     * from the database (specified by ID) or a custom product with user-defined properties.
     */
    @NotNull(message = "validation.flexuralverify.product.notnull")
    @Valid
    private ConcreteCalculationProductInput product;
}