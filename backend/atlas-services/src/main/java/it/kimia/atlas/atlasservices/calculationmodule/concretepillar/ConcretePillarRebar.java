package it.kimia.atlas.atlasservices.calculationmodule.concretepillar;

import it.kimia.atlas.atlasservices.calculationmodule.dto.RebarsDefinition;
import it.kimia.atlas.atlasservices.calculationmodule.dto.TransverseReinforcement;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Reinforcement bar parameters for Pillar calculations.
 * <p>
 * This class contains the reinforcement bar parameters specific to Pillar calculations.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConcretePillarRebar {
    @NotNull(message = "validation.pillar.reinforcementBar.top.notnull")
    @Valid
    private RebarsDefinition top;

    @NotNull(message = "validation.pillar.reinforcementBar.bottom.notnull")
    @Valid
    private RebarsDefinition bottom;

    @NotNull(message = "validation.pillar.reinforcementBar.transverse.notnull")
    @Valid
    private TransverseReinforcement transverse;
}