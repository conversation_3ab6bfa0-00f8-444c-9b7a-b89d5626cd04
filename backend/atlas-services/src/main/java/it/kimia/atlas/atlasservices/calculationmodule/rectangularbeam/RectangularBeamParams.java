package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam;

import it.kimia.atlas.atlasservices.calculationmodule.Polarity;
import it.kimia.atlas.atlasservices.calculationmodule.dto.MaterialProperties;
import jakarta.validation.Valid;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RectangularBeamParams implements Serializable {

    @PositiveOrZero(message = "validation.rectangularbeam.initialDeformation.positiveOrZero")
    private BigDecimal initialDeformation;

    @Valid
    private Polarity polarity;

    @Valid
    private RectangularBeamGeometry geometry;

    @Valid
    private RectangularBeamRebar reinforcementBar;

    @Valid
    private MaterialProperties materialProperties;
}
