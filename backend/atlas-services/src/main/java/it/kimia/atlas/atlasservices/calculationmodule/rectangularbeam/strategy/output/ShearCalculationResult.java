package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.output;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder(toBuilder = true)
public class ShearCalculationResult {
    private BigDecimal frpShearContribution;
    private boolean checkResult;
    private BigDecimal firstExperimentalData;
    private BigDecimal secondExperimentalData;
    private BigDecimal environmentalConversionFactor;
    private BigDecimal partialShearFactor;
    private BigDecimal materialPartialFactor;
    private BigDecimal designReinforcementStrain;
    private BigDecimal geometricCorrectionCoefficient;
    private BigDecimal optimalDesignAnchorageLength;
    private BigDecimal effectiveDesignStressFrp;
    private BigDecimal stirrupShearContribution;
    private BigDecimal concreteShearContribution;
    private BigDecimal shearCapacity;
}
