package it.kimia.atlas.atlasservices.calculationmodule;

import it.kimia.atlas.atlasservices.calculationmodule.concretepillar.ConcretePillarModule;
import it.kimia.atlas.atlasservices.calculationmodule.dto.ModuleCreateDTO;
import it.kimia.atlas.atlasservices.calculationmodule.dto.ModuleUpdateDTO;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamModule;
import it.kimia.atlas.atlasservices.calculationmodule.tbeam.TBeamModule;
import it.kimia.atlas.atlasservices.project.Project;
import it.kimia.atlas.atlasservices.project.ProjectRepository;
import it.kimia.atlas.atlasservices.project.ProjectService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Implementation of the ModuleService interface.
 */
@Service
@RequiredArgsConstructor
public class CalculationModuleServiceImpl implements CalculationModuleService {

    private final ProjectService projectService;
    private final ProjectRepository projectRepository;
    private final ParamApplierFactory paramApplierFactory;

    @Override
    public List<CalculationModule> getAllModules(String projectId) {
        return projectService.getProjectById(projectId)
                .map(Project::getModules)
                .orElseThrow(() -> new AccessDeniedException("Project not found or access denied"));
    }

    @Override
    public Optional<CalculationModule> getModuleById(String projectId, String moduleId) {
        return projectService.getProjectById(projectId)
                .map(project -> project.getModules().stream()
                        .filter(module -> module.getId().equals(moduleId))
                        .findFirst())
                .orElseThrow(() -> new AccessDeniedException("Project not found or access denied"));
    }

    @Override
    public CalculationModule createModule(String projectId, ModuleCreateDTO moduleCreateDTO) {
        return projectService.getProjectById(projectId)
                .map(project -> {
                    // Create the appropriate module type based on the DTO
                    CalculationModule module;
                    switch (moduleCreateDTO.getType()) {
                        case RECTANGULAR_BEAM:
                            module = new RectangularBeamModule();
                            break;
                        case T_BEAM:
                            module = new TBeamModule();
                            break;
                        case PILLAR:
                            module = new ConcretePillarModule();
                            break;
                        // Add cases for other module types as needed
                        default:
                            throw new IllegalArgumentException("Unsupported module type: " + moduleCreateDTO.getType());
                    }

                    // Set common properties
                    module.setName(moduleCreateDTO.getName());
                    module.setDescription(moduleCreateDTO.getDescription());
                    module.setCreatedAt(java.time.LocalDateTime.now());
                    module.setLastModified(java.time.LocalDateTime.now());

                    // Add the module to the project
                    List<CalculationModule> modules = new ArrayList<>(Optional.ofNullable(project.getModules()).orElse(new ArrayList<>()));
                    modules.add(module);
                    project.setModules(modules);

                    // Save the project
                    projectRepository.save(project);

                    return module;
                })
                .orElseThrow(() -> new AccessDeniedException("Project not found or access denied"));
    }

    @Override
    public Optional<CalculationModule> updateModule(String projectId, String moduleId, ModuleUpdateDTO moduleUpdateDTO) {
        return projectService.getProjectById(projectId)
                .flatMap(project -> {
                    // Find the module to update
                    Optional<CalculationModule> moduleOpt = project.getModules().stream()
                            .filter(m -> m.getId().equals(moduleId))
                            .findFirst();

                    if (moduleOpt.isPresent()) {
                        CalculationModule module = moduleOpt.get();

                        // Update common properties if provided
                        if (moduleUpdateDTO.getName() != null) {
                            module.setName(moduleUpdateDTO.getName());
                        }
                        if (moduleUpdateDTO.getDescription() != null) {
                            module.setDescription(moduleUpdateDTO.getDescription());
                        }
                        module.setLastModified(java.time.LocalDateTime.now());

                        // Save the project
                        projectRepository.save(project);

                        return Optional.of(module);
                    }

                    return Optional.empty();
                });
    }

    @Override
    public void deleteModule(String projectId, String moduleId) {
        projectService.getProjectById(projectId)
                .ifPresent(project -> {
                    List<CalculationModule> updatedModules = project.getModules().stream()
                            .filter(module -> !module.getId().equals(moduleId))
                            .collect(Collectors.toList());

                    project.setModules(updatedModules);
                    projectRepository.save(project);
                });
    }

    @Override
    public Optional<CalculationModule> updateModuleParams(String projectId, String moduleId, Object params) {
        return projectService.getProjectById(projectId)
                .flatMap(project -> {
                    // Find the module to update
                    Optional<CalculationModule> moduleOpt = project.getModules().stream()
                            .filter(m -> m.getId().equals(moduleId))
                            .findFirst();

                    if (moduleOpt.isPresent()) {
                        CalculationModule module = moduleOpt.get();

                        ParamApplier applier = paramApplierFactory.get(module);
                        applier.apply(module, params);

                        module.setLastModified(java.time.LocalDateTime.now());

                        projectRepository.save(project);

                        return Optional.of(module);
                    }

                    return Optional.empty();
                });
    }

}
