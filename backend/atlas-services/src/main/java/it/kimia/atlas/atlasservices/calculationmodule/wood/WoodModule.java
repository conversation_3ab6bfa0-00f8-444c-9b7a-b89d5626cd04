package it.kimia.atlas.atlasservices.calculationmodule.wood;

import it.kimia.atlas.atlasservices.calculationmodule.CalculationModule;
import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Wood calculation module.
 * <p>
 * This module is used for calculations on wood structures.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WoodModule extends CalculationModule {

    public static final ModuleType moduleType = ModuleType.WOOD;

    private WoodParams params;

    // Calculation results can be added here as needed
    // For example:
    // private WoodFlexuralCalculationResult flexuralCalculationResult;
    // private WoodShearCalculationResult shearCalculationResult;

    @Override
    public ModuleType getType() {
        return moduleType;
    }
}
