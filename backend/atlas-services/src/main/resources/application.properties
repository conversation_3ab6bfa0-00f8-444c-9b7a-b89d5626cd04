spring.application.name=atlas-services
# ==================================================
# BASE/LOCAL PROFILE
# This is the default profile for local development
# ==================================================
# ==================================================
# MongoDB Configuration (Single Source of Truth)
# ==================================================
spring.data.mongodb.uri=mongodb://localhost:27017/atlas
spring.data.mongodb.auto-index-creation=true
# ==================================================
# Server Configuration
# ==================================================
server.port=8000
# ==================================================
# Keycloak & Security Configuration
# ==================================================
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://localhost:8080/realms/atlas
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://localhost:8080/realms/atlas/protocol/openid-connect/certs
keycloak.server-url=http://localhost:8080
keycloak.realm=master
keycloak.client-id=admin-cli
keycloak.username=admin
keycloak.password=password
keycloak.roles=ADMIN,USER
# ==================================================
# Other Configurations
# ==================================================
logging.level.org.springframework.security=DEBUG
logging.level.liquibase=INFO
logging.level.org.springframework.boot.autoconfigure.liquibase=DEBUG
# ===============================
# CUSTOM APPLICATION PROPERTIES
# ===============================
# CORS Configuration
# A comma-separated list of origins allowed to connect.
# No trailing slashes.
app.cors.allowed-origins=http://localhost:3000
# ==================================================
# Swagger/OpenAPI Configuration
# ==================================================
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.swagger-ui.filter=true
springdoc.swagger-ui.disable-swagger-default-url=true
springdoc.packages-to-scan=it.kimia.atlas.atlasservices
# ==================================================
# Root finder tuning
# ==================================================
#rootfinder.qualifier=bisection
rootfinder.qualifier=brent
# tuning bisezione
#rootfinder.bisection.tol=1e-12
#rootfinder.bisection.max-iter=1000
# tuning brent
#rootfinder.brent.absTol=1e-12
#rootfinder.brent.relTol=1e-12
#rootfinder.brent.fTol=1e-12
#rootfinder.brent.maxEval=1000