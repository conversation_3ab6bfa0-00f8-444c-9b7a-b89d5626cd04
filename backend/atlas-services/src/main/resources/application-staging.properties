spring.application.name=atlas-services
# ==================================================
# STAGING PROFILE
# This profile is intended for use in Jenkins CI/CD pipeline
# and connects to services defined in docker-compose.test.yaml
# ==================================================
# ==================================================
# MongoDB Configuration (Single Source of Truth)
# ==================================================
spring.data.mongodb.uri=*******************************************************************************************
spring.data.mongodb.auto-index-creation=true
# ==================================================
# Server Configuration
# ==================================================
server.port=8000
# ==================================================
# Keycloak & Security Configuration
# ==================================================
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://keycloak_auth_test:8080/realms/atlas
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://keycloak_auth_test:8080/realms/atlas/protocol/openid-connect/certs
keycloak.server-url=http://keycloak_auth_test:8080
keycloak.realm=master
keycloak.client-id=admin-cli
keycloak.username=admin
keycloak.password=admin_password
keycloak.roles=ADMIN,USER
# ==================================================
# Other Configurations
# ==================================================
logging.level.org.springframework.security=INFO
logging.level.liquibase=INFO
logging.level.org.springframework.boot.autoconfigure.liquibase=INFO
# ===============================
# CUSTOM APPLICATION PROPERTIES
# ===============================
# CORS Configuration
# A comma-separated list of origins allowed to connect.
# No trailing slashes.
app.cors.allowed-origins=http://192.168.100.9:3000,https://app-srt.kimia.it/