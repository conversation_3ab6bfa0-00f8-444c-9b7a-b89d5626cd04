[{"name": "it.kimia.atlas.atlasservices.AtlasServicesApplication", "allDeclaredConstructors": true, "allDeclaredMethods": true, "allDeclaredFields": true}, {"name": "org.springframework.boot.autoconfigure.SpringBootApplication", "allDeclaredConstructors": true, "allDeclaredMethods": true}, {"name": "org.springframework.boot.context.properties.ConfigurationProperties", "allDeclaredConstructors": true, "allDeclaredMethods": true}, {"name": "org.springframework.data.mongodb.core.mapping.Document", "allDeclaredConstructors": true, "allDeclaredMethods": true}, {"name": "org.springframework.web.bind.annotation.RestController", "allDeclaredConstructors": true, "allDeclaredMethods": true}, {"name": "org.springframework.stereotype.Service", "allDeclaredConstructors": true, "allDeclaredMethods": true}, {"name": "org.springframework.stereotype.Repository", "allDeclaredConstructors": true, "allDeclaredMethods": true}, {"name": "org.springframework.context.annotation.Configuration", "allDeclaredConstructors": true, "allDeclaredMethods": true}]