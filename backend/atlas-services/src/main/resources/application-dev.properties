spring.application.name=atlas-services
# ==================================================
# DEV PROFILE
# This profile is intended for development use when connecting
# to staging services from outside Docker
# ==================================================
# ==================================================
# MongoDB Configuration (Single Source of Truth)
# ==================================================
spring.data.mongodb.uri=****************************************************************************************
spring.data.mongodb.auto-index-creation=true
# ==================================================
# Server Configuration
# ==================================================
server.port=8050
# ==================================================
# Keycloak & Security Configuration
# ==================================================
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://*************:9080/realms/atlas
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://*************:9080/realms/atlas/protocol/openid-connect/certs
keycloak.server-url=http://*************:9080
keycloak.realm=master
keycloak.client-id=admin-cli
keycloak.username=admin
keycloak.password=admin_password
keycloak.roles=ADMIN,USER
# ===============================
# CUSTOM APPLICATION PROPERTIES
# ===============================
# CORS Configuration
# A comma-separated list of origins allowed to connect.
# No trailing slashes.
app.cors.allowed-origins=http://localhost:3000