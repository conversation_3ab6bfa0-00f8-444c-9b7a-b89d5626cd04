package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamParams;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInputImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class FailureModeServiceTest {

    @Mock
    private ReinforcementRatioService reinforcementRatioService;

    @InjectMocks
    private FailureModeService failureModeService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void returnsDuctileWhenMechanicalRatioIsLessThanOrEqualToLimit() {
        RectangularBeamParams params = mock(RectangularBeamParams.class);
        FlexuralVerifyExecutionInput input = mock(FlexuralVerifyExecutionInputImpl.class);
        BigDecimal maxStress = BigDecimal.valueOf(100);
        BigDecimal mechanicalReinforcementRatio = BigDecimal.valueOf(0.3);
        BigDecimal limitMechanicalReinforcementRatio = BigDecimal.valueOf(0.4);

        when(reinforcementRatioService.calculateMechanicalReinforcementRatio(params, input, maxStress))
                .thenReturn(mechanicalReinforcementRatio);
        when(reinforcementRatioService.calculateLimitMechanicalReinforcementRatio(params))
                .thenReturn(limitMechanicalReinforcementRatio);

        int result = failureModeService.getFailureMode(params, input, maxStress);

        assertEquals(1, result);
    }

    @Test
    void returnsBrittleWhenMechanicalRatioIsGreaterThanLimit() {
        RectangularBeamParams params = mock(RectangularBeamParams.class);
        FlexuralVerifyExecutionInput input = mock(FlexuralVerifyExecutionInputImpl.class);
        BigDecimal maxStress = BigDecimal.valueOf(100);
        BigDecimal mechanicalReinforcementRatio = BigDecimal.valueOf(0.5);
        BigDecimal limitMechanicalReinforcementRatio = BigDecimal.valueOf(0.4);

        when(reinforcementRatioService.calculateMechanicalReinforcementRatio(params, input, maxStress))
                .thenReturn(mechanicalReinforcementRatio);
        when(reinforcementRatioService.calculateLimitMechanicalReinforcementRatio(params))
                .thenReturn(limitMechanicalReinforcementRatio);

        int result = failureModeService.getFailureMode(params, input, maxStress);

        assertEquals(2, result);
    }

    @Test
    void handlesZeroLimitMechanicalReinforcementRatio() {
        RectangularBeamParams params = mock(RectangularBeamParams.class);
        FlexuralVerifyExecutionInput input = mock(FlexuralVerifyExecutionInputImpl.class);
        BigDecimal maxStress = BigDecimal.valueOf(100);
        BigDecimal mechanicalReinforcementRatio = BigDecimal.valueOf(0.3);
        BigDecimal limitMechanicalReinforcementRatio = BigDecimal.ZERO;

        when(reinforcementRatioService.calculateMechanicalReinforcementRatio(params, input, maxStress))
                .thenReturn(mechanicalReinforcementRatio);
        when(reinforcementRatioService.calculateLimitMechanicalReinforcementRatio(params))
                .thenReturn(limitMechanicalReinforcementRatio);

        int result = failureModeService.getFailureMode(params, input, maxStress);

        assertEquals(2, result);
    }

    @Test
    void handlesZeroMechanicalReinforcementRatio() {
        RectangularBeamParams params = mock(RectangularBeamParams.class);
        FlexuralVerifyExecutionInput input = mock(FlexuralVerifyExecutionInputImpl.class);
        BigDecimal maxStress = BigDecimal.valueOf(100);
        BigDecimal mechanicalReinforcementRatio = BigDecimal.ZERO;
        BigDecimal limitMechanicalReinforcementRatio = BigDecimal.valueOf(0.4);

        when(reinforcementRatioService.calculateMechanicalReinforcementRatio(params, input, maxStress))
                .thenReturn(mechanicalReinforcementRatio);
        when(reinforcementRatioService.calculateLimitMechanicalReinforcementRatio(params))
                .thenReturn(limitMechanicalReinforcementRatio);

        int result = failureModeService.getFailureMode(params, input, maxStress);

        assertEquals(1, result);
    }
}