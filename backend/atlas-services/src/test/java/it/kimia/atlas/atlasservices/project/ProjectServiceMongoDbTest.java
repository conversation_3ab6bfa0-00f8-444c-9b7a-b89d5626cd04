package it.kimia.atlas.atlasservices.project;

import it.kimia.atlas.atlasservices.project.dtos.ProjectCreateDTO;
import it.kimia.atlas.atlasservices.project.dtos.ProjectUpdateDTO;
import it.kimia.atlas.atlasservices.project.mappers.ProjectMapper;
import it.kimia.atlas.atlasservices.shared.model.CurrentUser;
import it.kimia.atlas.atlasservices.user.UserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.AccessDeniedException;

import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ProjectServiceMongoDbTest {

    @Mock
    private ProjectRepository projectRepository;

    @Mock
    private UserService userService;

    @Mock
    private ProjectMapper projectMapper;

    @InjectMocks
    private ProjectServiceMongoDb projectService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("Get all projects visible to the current user")
    void getAllProjectsVisibleToCurrentUser() {
        CurrentUser currentUser = mock(CurrentUser.class);
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(currentUser.getVisibilityCone()).thenReturn(Set.of("user1", "user2"));
        Pageable pageable = mock(Pageable.class);
        Page<Project> mockPage = mock(Page.class);
        when(projectRepository.findByUserIdIn(anyList(), eq(pageable))).thenReturn(mockPage);

        Page<Project> result = projectService.getAllProjects(pageable);

        assertNotNull(result);
        verify(projectRepository).findByUserIdIn(anyList(), eq(pageable));
    }

    @Test
    @DisplayName("Get project by ID when visible to the current user")
    void getProjectByIdWhenVisibleToCurrentUser() {
        String projectId = "project1";
        String userId = "userId";
        Project project = mock(Project.class);
        when(project.getUserId()).thenReturn(userId);
        CurrentUser currentUser = mock(CurrentUser.class);
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(currentUser.hasVisibilityTo(anyString())).thenReturn(true);
        when(projectRepository.findById(projectId)).thenReturn(Optional.of(project));

        Optional<Project> result = projectService.getProjectById(projectId);

        assertTrue(result.isPresent());
        verify(userService).getCurrentUser();
        verify(projectRepository).findById(projectId);
    }

    @Test
    @DisplayName("Throw AccessDeniedException when project is not visible to the current user")
    void throwAccessDeniedExceptionWhenProjectNotVisible() {
        String projectId = "project1";
        Project project = mock(Project.class);
        CurrentUser currentUser = mock(CurrentUser.class);
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(currentUser.hasVisibilityTo(anyString())).thenReturn(false);
        when(projectRepository.findById(projectId)).thenReturn(Optional.of(project));

        assertThrows(AccessDeniedException.class, () -> projectService.getProjectById(projectId));
        verify(userService).getCurrentUser();
        verify(projectRepository).findById(projectId);
    }

    @Test
    @DisplayName("Create a new project for the current user")
    void createNewProjectForCurrentUser() {
        ProjectCreateDTO projectCreateDTO = mock(ProjectCreateDTO.class);
        Project project = mock(Project.class);
        CurrentUser currentUser = mock(CurrentUser.class);
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(currentUser.getId()).thenReturn("user1");
        when(projectMapper.toEntity(projectCreateDTO)).thenReturn(project);
        when(projectRepository.save(project)).thenReturn(project);

        Project result = projectService.createProject(projectCreateDTO);

        assertNotNull(result);
        verify(projectMapper).toEntity(projectCreateDTO);
        verify(projectRepository).save(project);
    }

    @Test
    @DisplayName("Update an existing project when visible to the current user")
    void updateExistingProjectWhenVisibleToCurrentUser() {
        String projectId = "project1";
        String userId = "userId";
        ProjectUpdateDTO projectUpdateDTO = mock(ProjectUpdateDTO.class);
        Project existingProject = mock(Project.class);
        CurrentUser currentUser = mock(CurrentUser.class);
        when(currentUser.hasVisibilityTo(anyString())).thenReturn(true);
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(existingProject.getUserId()).thenReturn(userId);
        when(projectRepository.findById(projectId)).thenReturn(Optional.of(existingProject));
        when(projectRepository.save(existingProject)).thenReturn(existingProject);

        Optional<Project> result = projectService.updateProject(projectId, projectUpdateDTO);

        assertTrue(result.isPresent());
        verify(projectMapper).updateProjectFromDto(projectUpdateDTO, existingProject);
        verify(projectRepository).save(existingProject);
    }

    @Test
    @DisplayName("Delete a project by ID when visible to the current user")
    void deleteProjectByIdWhenVisibleToCurrentUser() {
        String projectId = "project1";
        String userId = "userId";
        Project project = mock(Project.class);
        when(project.getUserId()).thenReturn(userId);
        CurrentUser currentUser = mock(CurrentUser.class);
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(currentUser.hasVisibilityTo(anyString())).thenReturn(true);
        when(projectRepository.findById(projectId)).thenReturn(Optional.of(project));

        projectService.deleteProject(projectId);

        verify(projectRepository).deleteById(projectId);
    }
}