package it.kimia.atlas.atlasservices.calculationmodule.concretepillar.strategy.strategy;

import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services.EnvironmentalAndSafetyFactorService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;
import static org.junit.jupiter.api.Assertions.assertEquals;

class ConcretePillarFlexuralVerifyServiceTest {
    @Mock
    private EnvironmentalAndSafetyFactorService environmentalAndSafetyFactorService;
    @InjectMocks
    private ConcretePillarFlexuralVerifyService svc;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    @Test
    void testC59_C60_C64() {
        // Valori dal file
        BigDecimal C18 = new BigDecimal("48");
        BigDecimal C19 = new BigDecimal("3.567060641061807");
        BigDecimal C27 = new BigDecimal("1.35");
        BigDecimal C34 = new BigDecimal("0.165");
        BigDecimal C36 = new BigDecimal("1");
        BigDecimal C40 = new BigDecimal("210000");
        BigDecimal C47 = new BigDecimal("1.118033988749895");

        // C59
        BigDecimal c59 = svc.calculateMaxReinforcementStress(C18, C19, C27, C34, C36, C40, C47);
        assertEquals(BigDecimal.valueOf(1688.5945705625).setScale(5, RoundingMode.HALF_UP)
                , c59.setScale(5, RoundingMode.HALF_UP));

        // C60 = C59 / C40
        BigDecimal c60 = svc.calculateMaxReinforcementStrain(c59, C40);
        assertEquals(BigDecimal.valueOf(0.008040926526).setScale(SCALE, RoundingMode.HALF_UP),
                c60.setScale(SCALE, RoundingMode.HALF_UP));

        // Parametri per C64
        BigDecimal C41 = new BigDecimal("0.012857142857142857");
        BigDecimal C43 = new BigDecimal("0.95");
        BigDecimal C45 = new BigDecimal("1.3");

        // C64 = MIN(C43*C41/C45, C60)
        BigDecimal c64 = svc.calculateDesignReinforcementStrain(C43, C41, C45, c60);

        // Controlli
        double value1 = C43.multiply(C41).divide(C45, ConcretePillarFlexuralVerifyService.MC).doubleValue();
        assertEquals(Math.min(value1, c60.doubleValue()), c64.doubleValue(), 1e-15);

        // Valore atteso da file
        assertEquals(BigDecimal.valueOf(0.008040926526).setScale(SCALE, RoundingMode.HALF_UP),
                c64.setScale(SCALE, RoundingMode.HALF_UP));
    }

    @Test
    void testC66() {
        BigDecimal mechanicalLimit = new BigDecimal("0.5369244135534318");   // C67
        BigDecimal mechanicalRatio = new BigDecimal("0.009328731165496035"); // C68
        int c66 = svc.getFailureMode(mechanicalLimit, mechanicalRatio);
        assertEquals(1, c66);
    }

    @Test
    void testC67_C68_C66() {
        // --- Input per C67 ---
        BigDecimal C21 = new BigDecimal("0.0035");
        BigDecimal C7 = new BigDecimal("300");
        BigDecimal C10 = new BigDecimal("280");
        BigDecimal C25 = new BigDecimal("0.002087378640776699");
        BigDecimal C2 = new BigDecimal("0");
        BigDecimal C69 = new BigDecimal("0.03283400655314332");
        BigDecimal C12 = new BigDecimal("307.8760800517997");
        BigDecimal C11 = new BigDecimal("307.8760800517997");

        BigDecimal c67 = svc.calculateMechanicalLimitRatio(C21, C7, C10, C25, C2, C69, C12, C11);
        assertEquals(BigDecimal.valueOf(0.5369244135).setScale(SCALE, RoundingMode.HALF_UP),
                c67.setScale(SCALE, RoundingMode.HALF_UP));

        // --- Input per C68 ---
        BigDecimal C35 = new BigDecimal("100");
        BigDecimal C34 = new BigDecimal("0.165");
        BigDecimal C36 = new BigDecimal("1");
        BigDecimal C65 = new BigDecimal("1688.5945705625145");
        BigDecimal C17 = new BigDecimal("35.55555555555555");
        BigDecimal C6 = new BigDecimal("300");

        BigDecimal c68 = svc.calculateMechanicalReinforcementRatio(C35, C34, C36, C65, C17, C6, C10);
        assertEquals(BigDecimal.valueOf(0.009328731165496035).setScale(SCALE, RoundingMode.HALF_UP),
                c68.setScale(SCALE, RoundingMode.HALF_UP));

        // --- C66 ---
        int c66 = svc.getFailureMode(c67, c68);
        assertEquals(1, c66);
    }

    @Test
    void testC69() {
        BigDecimal C11 = new BigDecimal("307.8760800517997");
        BigDecimal C24 = new BigDecimal("318.5185185185185");
        BigDecimal C17 = new BigDecimal("35.55555555555555");
        BigDecimal C6 = new BigDecimal("300");
        BigDecimal C10 = new BigDecimal("280");

        BigDecimal c69 = svc.calculateMechanicalTensileRebarRatio(C11, C24, C17, C6, C10);
        assertEquals(0.03283400654, c69.doubleValue(), 1e-15);
    }
}