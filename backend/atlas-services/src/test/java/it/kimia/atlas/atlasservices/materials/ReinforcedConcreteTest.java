package it.kimia.atlas.atlasservices.materials;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.List;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;

class ReinforcedConcreteTest {


    @DisplayName("Verify known values")
    @Test
    void verificaRisultatiConValoriControllati() {
        record CasoTest(ReinforcedConcrete test,
                        int expectedCompressive,
                        BigDecimal expectedTensile,
                        BigInteger expectedElastic) {
        }

        List<CasoTest> casi = List.of(
                new CasoTest(new ReinforcedConcrete(null, "C8/10", 10, 8),
                        16,
                        BigDecimal.valueOf(1.2980246133).setScale(SCALE, RoundingMode.HALF_UP),
                        BigInteger.valueOf(25331)),
                new CasoTest(new ReinforcedConcrete(null, "C40/50", 50, 40),
                        48,
                        BigDecimal.valueOf(3.5670606411).setScale(SCALE, RoundingMode.HALF_UP),
                        BigInteger.valueOf(35220)),
                new CasoTest(new ReinforcedConcrete(null, "C28/35", 35, 28),
                        36,
                        BigDecimal.valueOf(2.8317392032).setScale(SCALE, RoundingMode.HALF_UP),
                        BigInteger.valueOf(32308))
        );

        for (CasoTest caso : casi) {
            Assertions.assertEquals(caso.expectedCompressive, caso.test.getAverageCompressiveStrength());
            Assertions.assertEquals(caso.expectedTensile, caso.test.getAverageTensileStrength());
            Assertions.assertEquals(caso.expectedElastic, caso.test.getElasticModulus());
        }
    }
}