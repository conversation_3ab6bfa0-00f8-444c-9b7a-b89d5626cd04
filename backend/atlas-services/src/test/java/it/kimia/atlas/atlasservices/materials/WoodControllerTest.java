package it.kimia.atlas.atlasservices.materials;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Unit tests for WoodController.
 */
@WebMvcTest(WoodController.class)
@DisplayName("Wood Controller Tests")
class WoodControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WoodService woodService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @DisplayName("Should return wood material when found by name")
    void shouldReturnWoodWhenFoundByName() throws Exception {
        // Given
        Wood wood = Wood.builder()
                .id("wood-id-1")
                .name("C14")
                .category(WoodCategory.CONIFER_AND_POPLAR_WOOD)
                .flexuralStrength(14.0)
                .tensileStrengthParallel(8.0)
                .compressiveStrengthParallel(16.0)
                .shearStrength(1.7)
                .elasticModulusMeanParallel(7000.0)
                .meanDensity(350.0)
                .build();

        when(woodService.getWoodByName("C14")).thenReturn(Optional.of(wood));

        // When & Then
        mockMvc.perform(get("/api/v2/materials/woods/search")
                        .param("name", "C14")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value("wood-id-1"))
                .andExpect(jsonPath("$.name").value("C14"))
                .andExpect(jsonPath("$.category").value("CONIFER_AND_POPLAR_WOOD"))
                .andExpect(jsonPath("$.flexuralStrength").value(14.0))
                .andExpect(jsonPath("$.meanDensity").value(350.0));
    }

    @Test
    @DisplayName("Should return 404 when wood material not found by name")
    void shouldReturn404WhenWoodNotFoundByName() throws Exception {
        // Given
        when(woodService.getWoodByName(anyString())).thenReturn(Optional.empty());

        // When & Then
        mockMvc.perform(get("/api/v2/materials/woods/search")
                        .param("name", "UNKNOWN")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    @DisplayName("Should handle case-insensitive search")
    void shouldHandleCaseInsensitiveSearch() throws Exception {
        // Given
        Wood wood = Wood.builder()
                .id("wood-id-2")
                .name("GL20h")
                .category(WoodCategory.GLUED_LAMINATED_TIMBER)
                .flexuralStrength(20.0)
                .meanDensity(385.0)
                .build();

        when(woodService.getWoodByName("gl20h")).thenReturn(Optional.of(wood));

        // When & Then
        mockMvc.perform(get("/api/v2/materials/woods/search")
                        .param("name", "gl20h")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("GL20h"))
                .andExpect(jsonPath("$.category").value("GLUED_LAMINATED_TIMBER"));
    }
}
