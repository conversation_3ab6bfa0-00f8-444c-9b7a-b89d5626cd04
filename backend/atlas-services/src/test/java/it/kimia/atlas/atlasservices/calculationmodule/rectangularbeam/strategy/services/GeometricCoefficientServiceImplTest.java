package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamGeometry;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInputImpl;
import org.junit.jupiter.api.*;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class GeometricCoefficientServiceImplTest {

    private GeometricCoefficientServiceImpl service;

    @BeforeEach
    void setUp() {
        service = new GeometricCoefficientServiceImpl();
    }

    @Nested
    @DisplayName("getWidthSectionRation")
    class GetWidthSectionRation {

        @Test
        @DisplayName("Returns 1 when are equals")
        void returnsOneWhenEquals() {
            RectangularBeamGeometry geometry = mock(RectangularBeamGeometry.class);
            when(geometry.getWidth()).thenReturn(new BigDecimal(300));
            FlexuralVerifyExecutionInput input = mock(FlexuralVerifyExecutionInputImpl.class);
            when(input.getStripWidth()).thenReturn(300);

            BigDecimal result = service.getWidthSectionRation(geometry, input);

            Assertions.assertEquals(new BigDecimal(1).setScale(SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Returns 0.25 ratio when it is lower than or equal to 0.2500000000")
        void returnsMinimumRatio() {
            RectangularBeamGeometry geometry = mock(RectangularBeamGeometry.class);
            when(geometry.getWidth()).thenReturn(new BigDecimal(300));
            FlexuralVerifyExecutionInput input = mock(FlexuralVerifyExecutionInputImpl.class);
            when(input.getStripWidth()).thenReturn(50);

            BigDecimal result = service.getWidthSectionRation(geometry, input);

            Assertions.assertEquals(new BigDecimal("0.2500000000"), result);
        }

        @Test
        @DisplayName("Returns calculated ratio when it is greater than or equal to 0.25")
        void returnsCalculatedRatio() {
            RectangularBeamGeometry geometry = mock(RectangularBeamGeometry.class);
            when(geometry.getWidth()).thenReturn(new BigDecimal(300));
            FlexuralVerifyExecutionInput input = mock(FlexuralVerifyExecutionInputImpl.class);
            when(input.getStripWidth()).thenReturn(100);

            BigDecimal result = service.getWidthSectionRation(geometry, input);

            Assertions.assertEquals(BigDecimal.valueOf(0.3333333333), result);
        }
    }

    @Nested
    @DisplayName("calculateGeometricCorrectionCoefficient")
    class CalculateGeometricCorrectionCoefficient {

        @Test
        @DisplayName("Returns 1 when coefficient is less than or equal to 1")
        void returnsOneForLowCoefficient() {
            RectangularBeamGeometry geometry = mock(RectangularBeamGeometry.class);
            when(geometry.getWidth()).thenReturn(new BigDecimal(300));
            FlexuralVerifyExecutionInput input = mock(FlexuralVerifyExecutionInputImpl.class);
            when(input.getStripWidth()).thenReturn(500);

            BigDecimal result = service.calculateGeometricCorrectionCoefficient(geometry, input);

            Assertions.assertEquals(BigDecimal.ONE.setScale(SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Returns square root of coefficient when it is greater than 1")
        void returnsSquareRootForHighCoefficient() {
            RectangularBeamGeometry geometry = mock(RectangularBeamGeometry.class);
            when(geometry.getWidth()).thenReturn(new BigDecimal(300));
            FlexuralVerifyExecutionInput input = mock(FlexuralVerifyExecutionInputImpl.class);
            when(input.getStripWidth()).thenReturn(100);

            BigDecimal result = service.calculateGeometricCorrectionCoefficient(geometry, input);

            Assertions.assertEquals(new BigDecimal("1.12"), result.setScale(2, BigDecimal.ROUND_HALF_UP));
        }
    }
}
