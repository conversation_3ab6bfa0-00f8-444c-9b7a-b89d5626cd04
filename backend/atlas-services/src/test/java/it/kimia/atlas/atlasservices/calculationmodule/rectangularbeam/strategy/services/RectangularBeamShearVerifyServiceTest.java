package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.dto.ReinforcementLayout;
import it.kimia.atlas.atlasservices.product.FiberType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

class RectangularBeamShearVerifyServiceTest {

    @Mock
    private ConcreteCoverService concreteCoverService;
    @Mock
    private EnvironmentalAndSafetyFactorService environmentalAndSafetyFactorService;
    @Mock
    private FrpUtilsService frpUtilsService;
    @Mock
    private MaterialStrengthService materialStrengthService;

    @InjectMocks
    private RectangularBeamShearVerifyService rectangularBeamShearVerifyService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    @ParameterizedTest
    @CsvSource({
            "10, 100, 0.25",  // Ratio less than 0.25
            "100, 400, 0.25", // Ratio exactly 0.25
            "100, 200, 0.50"  // Ratio greater than 0.25
    })
    void returnsCorrectReinforcementWidthRatio(BigDecimal stripWidth, BigDecimal b, BigDecimal expectedRatio) {
        BigDecimal result = rectangularBeamShearVerifyService.getReinforcementWidthRatio(stripWidth, b);
        assertEquals(expectedRatio.setScale(SCALE, RoundingMode.HALF_UP), result);
    }

    @ParameterizedTest
    @CsvSource({
            "100, 0",         // Denominator is zero
    })
    void throwsExceptionForInvalidInputs(BigDecimal stripWidth, BigDecimal b) {
        assertThrows(IllegalArgumentException.class, () ->
                rectangularBeamShearVerifyService.getReinforcementWidthRatio(stripWidth, b)
        );
    }

    @ParameterizedTest
    @CsvSource({
            "0.25, 1.1832159566", // Valore tipico, risultato > 1
            "0.67, 1.0",          // Valore da excel
            "1.0, 1.0",           // Risultato esattamente 1
            "1.5, 1.0",           // Risultato < 1, deve restituire 1
            "0.0, 1.4142135624"   // Caso limite, sqrt(2/1) = 1.4142
    })
    void returnsCorrectGeometricCorrectionCoefficient(BigDecimal reinforcementWidthRatio, BigDecimal expected) {
        BigDecimal result = rectangularBeamShearVerifyService.getGeometricCorrectionCoefficient(reinforcementWidthRatio);
        assertEquals(expected.setScale(SCALE, RoundingMode.HALF_UP), result);
    }

    @ParameterizedTest
    @CsvSource({
            "-1.0",   // Denominatore zero
            "-2.0"    // Denominatore negativo
    })
    void throwsExceptionForInvalidDenominator(BigDecimal reinforcementWidthRatio) {
        assertThrows(IllegalArgumentException.class, () ->
                rectangularBeamShearVerifyService.getGeometricCorrectionCoefficient(reinforcementWidthRatio)
        );
    }

    @ParameterizedTest
    @CsvSource({
            // productType, fcm, fctm, FC, expected
            "CARBON, 16, 1.3, 1.35, 2.1114359724",              // Caso C8/10, Carbonio, LC1(1.35)
            "PREFORMED_CARBON, 16, 1.3, 1.35, 1.3513190223",    // Caso C8/10, Carbonio preformato, LC1(1.35)
            "CARBON, 16, 1.3, 1.2, 2.3753654690",              // Caso C8/10, Carbonio, LC2(1.20)
            "PREFORMED_CARBON, 16, 1.3, 1.2, 1.5202339001",    // Caso C8/10, Carbonio preformato, LC2(1.20)
            "CARBON, 48, 3.57, 1.35, 6.0603956192",              // Caso C40/50, Carbonio, LC1(1.35)
            "PREFORMED_CARBON, 48, 3.57, 1.35, 3.8786531963",    // Caso C40/50, Carbonio preformato, LC1(1.35)
            "CARBON, 48, 3.57, 1.2, 6.8179450716",              // Caso C40/50, Carbonio, LC2(1.20)
            "PREFORMED_CARBON, 48, 3.57, 1.2, 4.3634848459",    // Caso C40/50, Carbonio preformato, LC2(1.20)
    })
    void returnsCorrectAverageBondStress(FiberType productType, BigDecimal fcm, BigDecimal fctm, BigDecimal fc, BigDecimal expected) {
        BigDecimal result = rectangularBeamShearVerifyService.getAverageBondStress(productType, fcm, fctm, fc);
        assertEquals(expected.setScale(SCALE, RoundingMode.HALF_UP), result);
    }

    @ParameterizedTest
    @CsvSource({
            "PREFORMED_CARBON, 30, 2.5, 0",   // FC zero
            "CARBON, 40, 3.0, 0"              // FC zero, altro tipo
    })
    void throwsExceptionForZeroConfidenceFactorWhenCalculatingAverageBondStress(FiberType productType, BigDecimal fcm, BigDecimal fctm, BigDecimal fc) {
        assertThrows(IllegalArgumentException.class, () ->
                rectangularBeamShearVerifyService.getAverageBondStress(productType, fcm, fctm, fc)
        );
    }


    @ParameterizedTest
    @CsvSource({
            // productType, fcm, fctm, FC, expected
            "CARBON, 16, 1.3, 1.35, 1.0134892668",              // Caso C8/10, Carbonio, LC1(1.35)
            "PREFORMED_CARBON, 16, 1.3, 1.35, 0.5912020723",    // Caso C8/10, Carbonio preformato, LC1(1.35)
            "CARBON, 16, 1.3, 1.2, 1.1401754251",              // Caso C8/10, Carbonio, LC2(1.20)
            "PREFORMED_CARBON, 16, 1.3, 1.2, 0.6651023313",    // Caso C8/10, Carbonio preformato, LC2(1.20)
            "CARBON, 48, 3.57, 1.35, 2.9089898972",              // Caso C40/50, Carbonio, LC1(1.35)
            "PREFORMED_CARBON, 48, 3.57, 1.35, 1.6969107734",    // Caso C40/50, Carbonio preformato, LC1(1.35)
            "CARBON, 48, 3.57, 1.2, 3.2726136344",              // Caso C40/50, Carbonio, LC2(1.20)
            "PREFORMED_CARBON, 48, 3.57, 1.2, 1.9090246201",    // Caso C40/50, Carbonio preformato, LC2(1.20)
    })
    void returnsCorrectCharacteristicBondStress(FiberType productType, BigDecimal fcm, BigDecimal fctm, BigDecimal fc, BigDecimal expected) {
        BigDecimal result = rectangularBeamShearVerifyService.getCharacteristicBondStress(productType, fcm, fctm, fc);
        assertEquals(expected.setScale(SCALE, RoundingMode.HALF_UP), result);
    }

    @ParameterizedTest
    @CsvSource({
            "PREFORMED_CARBON, 30, 2.5, 0",   // FC zero
            "CARBON, 40, 3.0, 0"              // FC zero, altro tipo
    })
    void throwsExceptionForZeroConfidenceFactorWhenCalculatingCharacteristicBondStress(FiberType productType, BigDecimal fcm, BigDecimal fctm, BigDecimal fc) {
        assertThrows(IllegalArgumentException.class, () ->
                rectangularBeamShearVerifyService.getCharacteristicBondStress(productType, fcm, fctm, fc)
        );
    }

    @ParameterizedTest
    @CsvSource({
            // productType, averageBondStress, elasticModulus, thickness, layersNumber, averageFractureEnergy, expected
            "PREFORMED_CARBON, 2.1114359724, 210000, 0.167, 1, 0.25, 250",   // Risultato calcolato < 250, restituisce 250
            "CARBON, 2.1114359724, 60000, 0.167, 1, 0.264, 100",             // Risultato calcolato < 100, restituisce 100
            "CARBON, 2.1114359724, 210000, 0.167, 1, 0.264, 121", // Test da file excel 1
            "PREFORMED_CARBON, 1.3513190223, 200000, 1.4, 1, 0.169, 429", // Test da file excel 2
            "PREFORMED_CARBON, 1.3513190223, 200000, 1.4, 3, 0.169, 743", // Test da file excel 3
    })
    void returnsCorrectOptimalDesignAnchorageLength(
            FiberType productType,
            BigDecimal averageBondStress,
            BigDecimal elasticModulus,
            BigDecimal thickness,
            BigDecimal layersNumber,
            BigDecimal averageFractureEnergy,
            BigDecimal expected
    ) {
        BigDecimal result = rectangularBeamShearVerifyService.getOptimalDesignAnchorageLength(
                productType, averageBondStress, elasticModulus, thickness, layersNumber, averageFractureEnergy
        );
        assertEquals(expected, result.setScale(0, RoundingMode.HALF_UP));
    }

    @ParameterizedTest
    @CsvSource({
            "PREFORMED_CARBON, 0, 230000, 0.167, 1, 0.25", // averageBondStress zero
            "CARBON, 0, 230000, 0.167, 1, 0.25"            // averageBondStress zero, altro tipo
    })
    void throwsExceptionForZeroAverageBondStressWhenCalculatingOptimalDesignAnchorageLength(
            FiberType productType,
            BigDecimal averageBondStress,
            BigDecimal elasticModulus,
            BigDecimal thickness,
            BigDecimal layersNumber,
            BigDecimal averageFractureEnergy
    ) {
        assertThrows(IllegalArgumentException.class, () ->
                rectangularBeamShearVerifyService.getOptimalDesignAnchorageLength(
                        productType, averageBondStress, elasticModulus, thickness, layersNumber, averageFractureEnergy
                )
        );
    }

    @ParameterizedTest
    @CsvSource({
            "10, 100, 0.10",    // Normal ratio
            "50, 100, 0.50",    // Exactly 0.5
            "60, 100, 0.50",    // Greater than 0.5, should return 0.5
            "-10, 100, 0.00",   // Negative ratio, should return 0
            "0, 100, 0.00",      // Zero ratio
            "20, 300, 0.0666666667",      // Test from excel source 1
            "20, 500, 0.0400000000",      // Test from excel source 2
            "50, 300, 0.1666666667"      // Test from excel source 3
    })
    void returnsCorrectCurvatureToWebWidthRatio(BigDecimal cornerRadius, BigDecimal width, BigDecimal expected) {
        BigDecimal result = rectangularBeamShearVerifyService.getCurvatureToWebWidthRatio(cornerRadius, width);
        assertEquals(expected.setScale(SCALE, RoundingMode.HALF_UP), result);
    }

    @ParameterizedTest
    @CsvSource({
            "10, 0",    // Width is zero
            "10, -0"    // Width is zero (negative zero)
    })
    void throwsExceptionWhenWidthIsZero(BigDecimal cornerRadius, BigDecimal width) {
        assertThrows(IllegalArgumentException.class, () ->
                rectangularBeamShearVerifyService.getCurvatureToWebWidthRatio(cornerRadius, width)
        );
    }

    @ParameterizedTest
    @CsvSource({
            // geometricCorrectionCoefficient, elasticModulus, characteristicAverageFractureEnergy, layersNumber, thickness, expected
            "1.0, 210000, 0.12658987, 1, 0.165, 436.6553621681",
            "1.0, 60000, 0.264, 1, 0.167, 335.0356324662",
            "1.5, 200000, 0.169, 3, 1.4, 146.3850109364"
    })
    void returnsCorrectMaxReinforcementStress(
            BigDecimal geometricCorrectionCoefficient,
            BigDecimal elasticModulus,
            BigDecimal characteristicAverageFractureEnergy,
            BigDecimal layersNumber,
            BigDecimal thickness,
            BigDecimal expected
    ) {
        BigDecimal result = rectangularBeamShearVerifyService.getMaxReinforcementStress(
                geometricCorrectionCoefficient,
                elasticModulus,
                characteristicAverageFractureEnergy,
                layersNumber,
                thickness
        );
        assertEquals(expected.setScale(SCALE, RoundingMode.HALF_UP), result);
    }

    @ParameterizedTest
    @CsvSource({
            "1.2, 210000, 0.25, 0, 0.167",   // layersNumber zero
            "1.2, 210000, 0.25, 1, 0"        // thickness zero
    })
    void throwsExceptionForInvalidInputs(
            BigDecimal geometricCorrectionCoefficient,
            BigDecimal elasticModulus,
            BigDecimal characteristicAverageFractureEnergy,
            BigDecimal layersNumber,
            BigDecimal thickness
    ) {
        assertThrows(IllegalArgumentException.class, () ->
                rectangularBeamShearVerifyService.getMaxReinforcementStress(
                        geometricCorrectionCoefficient,
                        elasticModulus,
                        characteristicAverageFractureEnergy,
                        layersNumber,
                        thickness
                )
        );
    }

    @ParameterizedTest
    @CsvSource({
            // reinforcementLayout, maxReinforcementStress, optimalDesignAnchorageLength, stripInclination, effectiveDepth, freeHeight, phiR, projectTensionFRP, expected
            "OPEN_STIRRUP, 436.6553625, 120.7810762, 90, 470, 500, 0.466666667, 1558, 395.0953115851",   // Caso OPEN_STIRRUP (C40=1)
            "CLOSED_STIRRUP, 436.6553625, 120.7810762, 90, 470, 500, 0.466666667, 1558, 519.6197719928", // Caso CLOSED_STIRRUP (C40=2), cond >= 0
            "CLOSED_STIRRUP, 436.6553625, 120.7810762, 45, 470, 500, 0.306666667, 1558, 438.3750437852",     // Caso CLOSED_STIRRUP (C40=2),
    })
    void returnsCorrectEffectiveDesignStressFRP(
            ReinforcementLayout reinforcementLayout,
            BigDecimal maxReinforcementStress,
            BigDecimal optimalDesignAnchorageLength,
            BigDecimal stripInclination,
            BigDecimal effectiveDepth,
            BigDecimal freeHeight,
            BigDecimal phiR,
            BigDecimal projectTensionFRP,
            BigDecimal expected
    ) {
        BigDecimal result = rectangularBeamShearVerifyService.getEffectiveDesignStressFRP(
                reinforcementLayout,
                maxReinforcementStress,
                optimalDesignAnchorageLength,
                stripInclination,
                effectiveDepth,
                freeHeight,
                phiR,
                projectTensionFRP
        );
        assertEquals(expected.setScale(SCALE, RoundingMode.HALF_UP), result);
    }


    @ParameterizedTest
    @CsvSource({
            // shearPartialFactor, effectiveDesignStressFRP, frpArea, effectiveDepth, stripSpacingOrthogonal, concreteStrutInclination, stripInclination, expected
            "1.2, 395.10, 33, 470, 150, 45, 90, 30.6400049988",
            "1.2, 407.27, 33, 470, 106, 45, 45, 44.6940403284",
            "1.2, 395.10, 33, 470, 150, 22, 90, 75.8366735611",
            "1.2, 407.27, 33, 470, 106, 22, 45, 77.6578359856",
    })
    void returnsCorrectFrpShearContribution(
            BigDecimal shearPartialFactor,
            BigDecimal effectiveDesignStressFRP,
            BigDecimal frpArea,
            BigDecimal effectiveDepth,
            BigDecimal stripSpacingOrthogonal,
            BigDecimal concreteStrutInclination,
            BigDecimal stripInclination,
            BigDecimal expected
    ) {
        BigDecimal result = rectangularBeamShearVerifyService.getFrpShearContribution(
                shearPartialFactor,
                effectiveDesignStressFRP,
                frpArea,
                effectiveDepth,
                stripSpacingOrthogonal,
                concreteStrutInclination,
                stripInclination
        );
        assertEquals(expected.setScale(SCALE, RoundingMode.HALF_UP), result);
    }


    @ParameterizedTest
    @CsvSource({
            // effectiveDepth, transverseArea, stirrupSpacing, designYieldStrength, stirrupInclination, concreteStrutInclination, expected
            "470, 100.53, 150, 148.15, 90, 45, 41.9997249900",
            "470, 100.53, 150, 148.15, 45, 45, 59.3965806968",
            "470, 100.53, 150, 148.15, 90, 22, 103.9529671698",
            "470, 100.53, 150, 148.15, 45, 22, 103.2041383587"
    })
    void returnsCorrectStirrupShearContribution(
            BigDecimal effectiveDepth,
            BigDecimal transverseArea,
            BigDecimal stirrupSpacing,
            BigDecimal designYieldStrength,
            BigDecimal stirrupInclination,
            BigDecimal concreteStrutInclination,
            BigDecimal expected
    ) {
        BigDecimal result = rectangularBeamShearVerifyService.getStirrupShearContribution(
                effectiveDepth,
                transverseArea,
                stirrupSpacing,
                designYieldStrength,
                stirrupInclination,
                concreteStrutInclination
        );
        assertEquals(expected.setScale(SCALE, RoundingMode.HALF_UP), result);
    }


    @ParameterizedTest
    @CsvSource({
            // effectiveDepth, width, designCompressiveStrength, stirrupInclination, concreteStrutInclination, expected
            "470, 300, 7.90, 90, 45, 250.6275000000",
            "470, 300, 7.90, 45, 45, 501.2550000000",
            "470, 300, 7.90, 90, 22, 174.1004907422",
            "470, 300, 7.90, 45, 22, 244.4416549328"
    })
    void returnsCorrectConcreteShearContribution(
            BigDecimal effectiveDepth,
            BigDecimal width,
            BigDecimal designCompressiveStrength,
            BigDecimal stirrupInclination,
            BigDecimal concreteStrutInclination,
            BigDecimal expected
    ) {
        BigDecimal result = rectangularBeamShearVerifyService.getConcreteShearContribution(
                effectiveDepth,
                width,
                designCompressiveStrength,
                stirrupInclination,
                concreteStrutInclination
        );
        assertEquals(expected.setScale(SCALE, RoundingMode.HALF_UP), result);
    }
}