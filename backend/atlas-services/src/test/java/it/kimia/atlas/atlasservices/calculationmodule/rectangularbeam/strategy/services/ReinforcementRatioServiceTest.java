package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProduct;
import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProductImpl;
import it.kimia.atlas.atlasservices.calculationmodule.dto.MaterialProperties;
import it.kimia.atlas.atlasservices.calculationmodule.dto.MaterialPropertiesSteelGrade;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamGeometry;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamParams;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInputImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class ReinforcementRatioServiceTest {

    @Mock
    private ConcreteCoverService concreteCoverService;
    @Mock
    private MaterialStrengthService materialStrengthService;
    @Mock
    private FrpUtilsService frpUtilsService;

    private ReinforcementRatioService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        service = new ReinforcementRatioService(concreteCoverService, materialStrengthService, frpUtilsService);
    }

    @Nested
    @DisplayName("TensileSteelMechanicalRatio")
    class TensileSteelMechanicalRatio {

        @Test
        @DisplayName("Returns the correct value for valid inputs")
        void givenKnownMaterialCoefficients_whenCalculateTensileSteelMechanicalRatio_expectsCorrectResult() {
            RectangularBeamParams params = mock(RectangularBeamParams.class);
            RectangularBeamGeometry geometry = mock(RectangularBeamGeometry.class);
            when(concreteCoverService.getTensileSteelArea(params)).thenReturn(BigDecimal.valueOf(339.29));
            when(materialStrengthService.getDesignYieldStrengthForDuctileMechanisms(params.getMaterialProperties())).thenReturn(BigDecimal.valueOf(740.74));
            when(materialStrengthService.getDesignCompressiveStrengthForDuctileMechanisms(params.getMaterialProperties())).thenReturn(BigDecimal.valueOf(35.56));
            when(geometry.getWidth()).thenReturn(BigDecimal.valueOf(300));
            when(geometry.getEffectiveDepth()).thenReturn(BigDecimal.valueOf(470));
            when(params.getGeometry()).thenReturn(geometry);

            BigDecimal result = service.calculateTensileSteelMechanicalRatio(params);

            assertEquals(BigDecimal.valueOf(0.0501251854), result);
        }
    }

    @Nested
    @DisplayName("LimitMechanicalReinforcementRatio")
    class LimitMechanicalReinforcementRatio {

        @Test
        @DisplayName("Test with known material using valid input expects excel result")
        void givenKnownMaterialCoefficients_whenCalculateLimitMechanicalReinforcementRatio_expectsCorrectResult() {
            RectangularBeamParams params = mock(RectangularBeamParams.class);
            RectangularBeamGeometry geometry = mock(RectangularBeamGeometry.class);
            MaterialProperties materialProperties = mock(MaterialProperties.class);
            MaterialPropertiesSteelGrade steelGrade = mock(MaterialPropertiesSteelGrade.class);


            when(concreteCoverService.getTensileSteelArea(params)).thenReturn(BigDecimal.valueOf(339.29));
            when(concreteCoverService.getCompressiveSteelArea(params)).thenReturn(BigDecimal.valueOf(226.19));
            when(materialStrengthService.getDesignYieldStrengthForDuctileMechanisms(materialProperties)).thenReturn(BigDecimal.valueOf(740.74));
            when(materialStrengthService.getDesignCompressiveStrengthForDuctileMechanisms(materialProperties)).thenReturn(BigDecimal.valueOf(35.56));
            when(geometry.getHeight()).thenReturn(BigDecimal.valueOf(500));
            when(geometry.getWidth()).thenReturn(BigDecimal.valueOf(300));
            when(geometry.getEffectiveDepth()).thenReturn(BigDecimal.valueOf(470));
            when(steelGrade.getElasticModulus()).thenReturn(200000);
            when(materialProperties.getSteelGrade()).thenReturn(steelGrade);
            when(params.getInitialDeformation()).thenReturn(BigDecimal.ZERO);
            when(params.getGeometry()).thenReturn(geometry);
            when(params.getMaterialProperties()).thenReturn(materialProperties);


            BigDecimal result = service.calculateLimitMechanicalReinforcementRatio(params);

            assertEquals(BigDecimal.valueOf(0.3967902037), result);
        }
    }

    @Nested
    @DisplayName("MechanicalReinforcementRatio")
    class MechanicalReinforcementRatio {

        @Test
        @DisplayName("Test with known material using valid input expects excel result")
        void returnsCorrectValueForValidInputs() {
            // fdd,2
            BigDecimal maxStress = BigDecimal.valueOf(438.21);
            RectangularBeamParams params = mock(RectangularBeamParams.class);
            RectangularBeamGeometry geometry = mock(RectangularBeamGeometry.class);
            FlexuralVerifyExecutionInput input = mock(FlexuralVerifyExecutionInputImpl.class);
            ReinforcedConcreteProduct product = mock(ReinforcedConcreteProductImpl.class);
            when(input.getProduct()).thenReturn(product);
            when(frpUtilsService.computeFrpArea(input)).thenReturn(BigDecimal.valueOf(420));
            when(materialStrengthService.getDesignCompressiveStrengthForDuctileMechanisms(params.getMaterialProperties())).thenReturn(BigDecimal.valueOf(35.56));
            when(geometry.getWidth()).thenReturn(BigDecimal.valueOf(300));
            when(geometry.getEffectiveDepth()).thenReturn(BigDecimal.valueOf(470));
            when(params.getGeometry()).thenReturn(geometry);

            BigDecimal result = service.calculateMechanicalReinforcementRatio(params, input, maxStress);

            assertEquals(BigDecimal.valueOf(0.0367071536), result);
        }

        @Test
        @DisplayName("Returns zero if FRP area is zero")
        void returnsZeroIfFrpAreaIsZero() {
            // fdd,2
            BigDecimal maxStress = BigDecimal.valueOf(438.21);
            RectangularBeamParams params = mock(RectangularBeamParams.class);
            RectangularBeamGeometry geometry = mock(RectangularBeamGeometry.class);
            FlexuralVerifyExecutionInput input = mock(FlexuralVerifyExecutionInputImpl.class);
            ReinforcedConcreteProduct product = mock(ReinforcedConcreteProductImpl.class);
            when(input.getProduct()).thenReturn(product);
            when(frpUtilsService.computeFrpArea(input)).thenReturn(BigDecimal.ZERO);
            when(materialStrengthService.getDesignCompressiveStrengthForDuctileMechanisms(params.getMaterialProperties())).thenReturn(BigDecimal.valueOf(35.56));
            when(geometry.getWidth()).thenReturn(BigDecimal.valueOf(300));
            when(geometry.getEffectiveDepth()).thenReturn(BigDecimal.valueOf(470));
            when(params.getGeometry()).thenReturn(geometry);

            BigDecimal result = service.calculateMechanicalReinforcementRatio(params, input, maxStress);

            assertEquals(BigDecimal.ZERO.setScale(SCALE, RoundingMode.HALF_UP), result);
        }
    }
}