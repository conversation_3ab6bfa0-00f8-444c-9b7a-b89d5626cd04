package it.kimia.atlas.atlasservices.materials;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link SteelGradeServiceMongoDb}.
 */
@ExtendWith(MockitoExtension.class)
class SteelGradeServiceMongoDbTest {

    @Mock
    private SteelGradeRepository steelGradeRepository;

    @InjectMocks
    private SteelGradeServiceMongoDb steelGradeService;

    @Test
    void getSteelGrades_shouldReturnPageOfSteelGrades() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        SteelGrade steelGrade = new SteelGrade();
        List<SteelGrade> steelGrades = Collections.singletonList(steelGrade);
        Page<SteelGrade> expectedPage = new PageImpl<>(steelGrades, pageable, 1);

        when(steelGradeRepository.findAll(pageable)).thenReturn(expectedPage);

        // When
        Page<SteelGrade> actualPage = steelGradeService.getSteelGrades(pageable);

        // Then
        assertThat(actualPage).isEqualTo(expectedPage);
        verify(steelGradeRepository).findAll(pageable);
    }

    @Test
    void getSteelGradeById_whenSteelGradeExists_shouldReturnOptionalOfSteelGrade() {
        // Given
        String id = "some-id";
        SteelGrade steelGrade = new SteelGrade();
        when(steelGradeRepository.findById(id)).thenReturn(Optional.of(steelGrade));

        // When
        Optional<SteelGrade> result = steelGradeService.getSteelGradeById(id);

        // Then
        assertThat(result).isPresent().contains(steelGrade);
        verify(steelGradeRepository).findById(id);
    }

    @Test
    void getSteelGradeById_whenSteelGradeDoesNotExist_shouldReturnEmptyOptional() {
        // Given
        String id = "non-existent-id";
        when(steelGradeRepository.findById(id)).thenReturn(Optional.empty());

        // When
        Optional<SteelGrade> result = steelGradeService.getSteelGradeById(id);

        // Then
        assertThat(result).isNotPresent();
        verify(steelGradeRepository).findById(id);
    }
}