package it.kimia.atlas.atlasservices.product;

import it.kimia.atlas.atlasservices.product.dtos.ProductCreateDTO;
import it.kimia.atlas.atlasservices.product.dtos.ProductUpdateDTO;
import it.kimia.atlas.atlasservices.product.mappers.ProductMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ProductServiceMongoDbTest {

    private final ProductRepository productRepository = mock(ProductRepository.class);
    private final ProductMapper productMapper = mock(ProductMapper.class);
    private final MongoTemplate mongoTemplate = mock(MongoTemplate.class);
    private final ProductServiceMongoDb productService = new ProductServiceMongoDb(productRepository, productMapper, mongoTemplate);

    @Nested
    @DisplayName("getProducts")
    class GetProducts {

        @Test
        @DisplayName("Returns empty page when no products match criteria")
        void returnsEmptyPageWhenNoProductsMatchCriteria() {
            PageRequest pageable = PageRequest.of(0, 10);
            when(mongoTemplate.find(any(Query.class), eq(Product.class))).thenReturn(Collections.emptyList());
            when(mongoTemplate.count(any(Query.class), eq(Product.class))).thenReturn(0L);

            Page<Product> result = productService.getProducts(null, null, null, pageable);

            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("Returns paginated products when criteria match")
        void returnsPaginatedProductsWhenCriteriaMatch() {
            PageRequest pageable = PageRequest.of(0, 10);
            Product product = mock(Product.class);
            when(mongoTemplate.find(any(Query.class), eq(Product.class))).thenReturn(Collections.singletonList(product));
            when(mongoTemplate.count(any(Query.class), eq(Product.class))).thenReturn(1L);

            Page<Product> result = productService.getProducts("name", null, "type", pageable);

            assertFalse(result.isEmpty());
            assertEquals(1, result.getTotalElements());
            assertEquals(product, result.getContent().get(0));
        }
    }

    @Nested
    @DisplayName("getProductById")
    class GetProductById {

        @Test
        @DisplayName("Returns empty optional when product is not found")
        void returnsEmptyOptionalWhenProductIsNotFound() {
            when(productRepository.findById("invalidId")).thenReturn(Optional.empty());

            Optional<Product> result = productService.getProductById("invalidId");

            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("Returns product when found by ID")
        void returnsProductWhenFoundById() {
            Product product = mock(Product.class);
            when(productRepository.findById("validId")).thenReturn(Optional.of(product));

            Optional<Product> result = productService.getProductById("validId");

            assertTrue(result.isPresent());
            assertEquals(product, result.get());
        }
    }

    @Nested
    @DisplayName("createProduct")
    class CreateProduct {

        @Test
        @DisplayName("Successfully creates a new product")
        void successfullyCreatesNewProduct() {
            ProductCreateDTO productDTO = mock(ProductCreateDTO.class);
            Product product = mock(Product.class);
            when(productMapper.toEntity(productDTO)).thenReturn(product);
            when(productRepository.save(product)).thenReturn(product);

            Product result = productService.createProduct(productDTO);

            assertNotNull(result);
            assertEquals(product, result);
        }
    }

    @Nested
    @DisplayName("updateProduct")
    class UpdateProduct {

        @Test
        @DisplayName("Returns empty optional when product to update is not found")
        void returnsEmptyOptionalWhenProductToUpdateIsNotFound() {
            when(productRepository.findById("invalidId")).thenReturn(Optional.empty());

            Optional<Product> result = productService.updateProduct("invalidId", mock(ProductUpdateDTO.class));

            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("Successfully updates an existing product")
        void successfullyUpdatesExistingProduct() {
            Product existingProduct = mock(Product.class);
            ProductUpdateDTO updateDTO = mock(ProductUpdateDTO.class);
            Product updatedProduct = mock(Product.class);

            when(productRepository.findById("validId")).thenReturn(Optional.of(existingProduct));
            doNothing().when(productMapper).updateProductFromDto(updateDTO, existingProduct);
            when(productRepository.save(existingProduct)).thenReturn(updatedProduct);

            Optional<Product> result = productService.updateProduct("validId", updateDTO);

            assertTrue(result.isPresent());
            assertEquals(updatedProduct, result.get());
        }
    }

    @Nested
    @DisplayName("deleteProduct")
    class DeleteProduct {

        @Test
        @DisplayName("Successfully deletes a product by ID")
        void successfullyDeletesProductById() {
            doNothing().when(productRepository).deleteById("validId");

            productService.deleteProduct("validId");

            verify(productRepository).deleteById("validId");
        }
    }
}