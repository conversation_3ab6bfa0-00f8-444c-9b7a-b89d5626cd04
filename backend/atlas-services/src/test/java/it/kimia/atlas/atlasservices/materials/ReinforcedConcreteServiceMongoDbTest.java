package it.kimia.atlas.atlasservices.materials;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.Collections;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ReinforcedConcreteServiceMongoDbTest {

    @Mock
    private ReinforcedConcreteRepository reinforcedConcreteRepository;

    @InjectMocks
    private ReinforcedConcreteServiceMongoDb reinforcedConcreteService;

    @Test
    void getReinforcedConcretes_shouldReturnPageOfReinforcedConcretes() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        // Assuming ReinforcedConcrete has a default constructor for test data creation.
        ReinforcedConcrete concrete = new ReinforcedConcrete();
        Page<ReinforcedConcrete> expectedPage = new PageImpl<>(Collections.singletonList(concrete), pageable, 1);

        when(reinforcedConcreteRepository.findAll(pageable)).thenReturn(expectedPage);

        // Act
        Page<ReinforcedConcrete> actualPage = reinforcedConcreteService.getReinforcedConcretes(pageable);

        // Assert
        assertThat(actualPage).isNotNull();
        assertThat(actualPage).isEqualTo(expectedPage);
        verify(reinforcedConcreteRepository).findAll(pageable);
    }

    @Test
    void getReinforcedConcreteById_whenFound_shouldReturnOptionalOfReinforcedConcrete() {
        // Arrange
        String id = "test-id-123";
        ReinforcedConcrete expectedConcrete = new ReinforcedConcrete();
        when(reinforcedConcreteRepository.findById(id)).thenReturn(Optional.of(expectedConcrete));

        // Act
        Optional<ReinforcedConcrete> actualConcreteOptional = reinforcedConcreteService.getReinforcedConcreteById(id);

        // Assert
        assertThat(actualConcreteOptional).isPresent();
        assertThat(actualConcreteOptional.get()).isEqualTo(expectedConcrete);
        verify(reinforcedConcreteRepository).findById(id);
    }

    @Test
    void getReinforcedConcreteById_whenNotFound_shouldReturnEmptyOptional() {
        // Arrange
        String id = "not-found-id";
        when(reinforcedConcreteRepository.findById(id)).thenReturn(Optional.empty());

        // Act
        Optional<ReinforcedConcrete> actualConcreteOptional = reinforcedConcreteService.getReinforcedConcreteById(id);

        // Assert
        assertThat(actualConcreteOptional).isNotPresent();
        verify(reinforcedConcreteRepository).findById(id);
    }
}