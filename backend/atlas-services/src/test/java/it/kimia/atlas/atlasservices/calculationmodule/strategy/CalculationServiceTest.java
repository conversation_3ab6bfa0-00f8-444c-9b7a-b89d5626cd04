package it.kimia.atlas.atlasservices.calculationmodule.strategy;

import com.fasterxml.jackson.databind.ObjectMapper;
import it.kimia.atlas.atlasservices.calculationmodule.CalculationModule;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.dto.CalculationRequest;
import it.kimia.atlas.atlasservices.calculationmodule.strategy.factory.CalculationStrategyFactory;
import it.kimia.atlas.atlasservices.project.Project;
import it.kimia.atlas.atlasservices.project.ProjectRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

class CalculationServiceTest {

    private CalculationService calculationService;
    private ProjectRepository projectRepository;
    private StrategyFactoryRegistry strategyFactoryRegistry;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setup() {
        projectRepository = mock(ProjectRepository.class);
        strategyFactoryRegistry = mock(StrategyFactoryRegistry.class);
        objectMapper = mock(ObjectMapper.class);
        calculationService = new CalculationService(projectRepository, strategyFactoryRegistry, objectMapper);
    }

    @Nested
    @DisplayName("executeCalculation")
    class ExecuteCalculation {

        @Test
        @DisplayName("Returns empty when project is not found")
        void returnsEmptyWhenProjectNotFound() {
            when(projectRepository.findById("invalidProjectId")).thenReturn(Optional.empty());

            Optional<CalculationModule> result = calculationService.executeCalculation("invalidProjectId", "moduleId", mock(CalculationRequest.class));

            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("Returns empty when module is not found in project")
        void returnsEmptyWhenModuleNotFoundInProject() {
            Project project = mock(Project.class);
            when(projectRepository.findById("projectId")).thenReturn(Optional.of(project));
            when(project.getModules()).thenReturn(List.of());

            Optional<CalculationModule> result = calculationService.executeCalculation("projectId", "invalidModuleId", mock(CalculationRequest.class));

            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("Returns empty when no strategy factory is found for module type")
        void returnsEmptyWhenNoStrategyFactoryFound() {
            Project project = mock(Project.class);
            CalculationModule module = mock(CalculationModule.class);
            when(projectRepository.findById("projectId")).thenReturn(Optional.of(project));
            when(project.getModules()).thenReturn(List.of(module));
            when(module.getId()).thenReturn("moduleId");
            when(strategyFactoryRegistry.findFactory(module)).thenReturn(Optional.empty());

            Optional<CalculationModule> result = calculationService.executeCalculation("projectId", "moduleId", mock(CalculationRequest.class));

            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("Successfully executes calculation and updates module")
        void successfullyExecutesCalculationAndUpdatesModule() {
            Project project = mock(Project.class);
            CalculationModule module = mock(CalculationModule.class);
            CalculationRequest request = mock(CalculationRequest.class);
            CalculationStrategyFactory<CalculationModule> factory = mock(CalculationStrategyFactory.class);
            CalculationStrategy<CalculationModule, Object> strategy = mock(CalculationStrategy.class);
            CalculationModule updatedModule = mock(CalculationModule.class);
            Object inputData = new Object();

            when(request.getCalculationType()).thenReturn(CalculationType.SHEAR_VERIFY);
            when(request.getInput()).thenReturn(inputData);
            when(projectRepository.findById("projectId")).thenReturn(Optional.of(project));
            when(project.getModules()).thenReturn(List.of(module));
            when(module.getId()).thenReturn("moduleId");
            when(strategyFactoryRegistry.findFactory(module)).thenReturn(Optional.of(factory));
            when(factory.createStrategy(request.getCalculationType())).thenReturn(Optional.of(strategy));

            when(strategy.getInputType()).thenReturn(Object.class);
            when(objectMapper.convertValue(any(), eq(Object.class))).thenReturn(inputData);

            when(strategy.execute(module, inputData)).thenReturn(updatedModule);

            Optional<CalculationModule> result = calculationService.executeCalculation("projectId", "moduleId", request);

            assertTrue(result.isPresent());
            assertEquals(updatedModule, result.get());
            verify(updatedModule, times(1)).setLastModified(any(LocalDateTime.class));
            verify(projectRepository, times(1)).save(project);
        }
    }
}