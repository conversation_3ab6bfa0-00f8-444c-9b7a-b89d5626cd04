package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.KnowledgeLevel;
import it.kimia.atlas.atlasservices.calculationmodule.dto.MaterialProperties;
import it.kimia.atlas.atlasservices.calculationmodule.dto.MaterialPropertiesReinforcedConcreteClass;
import it.kimia.atlas.atlasservices.calculationmodule.dto.MaterialPropertiesSteelGrade;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static it.kimia.atlas.atlasservices.calculationmodule.KnowledgeLevel.*;
import static it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services.MaterialStrengthCalculatorService.BRITTLE_MECHANISM_COMPRESSIVE_CONSTANT;
import static it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services.MaterialStrengthCalculatorService.BRITTLE_MECHANISM_YIELD_CONSTANT;
import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;
import static org.junit.jupiter.api.Assertions.assertEquals;

class MaterialStrengthCalculatorServiceTest {

    private MaterialStrengthService materialStrengthService;

    @BeforeEach
    void setUp() {
        materialStrengthService = new MaterialStrengthCalculatorService();
    }

    @Nested
    @DisplayName("Tests for DesignYieldStrengthForDuctileMechanisms fyd,d")
    class DesignYieldStrengthForDuctileMechanismsTests {

        private MaterialProperties buildParamsForDesignYieldStrengthForDuctileMechanisms(KnowledgeLevel knowledgeLevel, BigDecimal yieldStrength) {

            var steelGrade = new MaterialPropertiesSteelGrade();
            steelGrade.setYieldStrength(yieldStrength);

            var materialProperties = new MaterialProperties();
            materialProperties.setKnowledgeLevel(knowledgeLevel);
            materialProperties.setSteelGrade(steelGrade);

            return materialProperties;
        }

        @Test
        @DisplayName("Test with knowledgeLevel LC1")
        void testWithKnowledgeLevelLC1() {
            var params = buildParamsForDesignYieldStrengthForDuctileMechanisms(LC1, BigDecimal.valueOf(230));
            var result = materialStrengthService.getDesignYieldStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(230).divide(LC1.getValue(), SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with knowledgeLevel LC2")
        void testWithKnowledgeLevelLC2() {
            var params = buildParamsForDesignYieldStrengthForDuctileMechanisms(LC2, BigDecimal.valueOf(230));
            var result = materialStrengthService.getDesignYieldStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(230).divide(LC2.getValue(), SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with yieldStrength low")
        void testWithLowYieldStrength() {
            var params = buildParamsForDesignYieldStrengthForDuctileMechanisms(LC1, BigDecimal.valueOf(150));
            var result = materialStrengthService.getDesignYieldStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(150).divide(LC1.getValue(), SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with yieldStrength high")
        void testWithHighYieldStrength() {
            var params = buildParamsForDesignYieldStrengthForDuctileMechanisms(LC1, BigDecimal.valueOf(500));
            var result = materialStrengthService.getDesignYieldStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(500).divide(LC1.getValue(), SCALE, RoundingMode.HALF_UP), result);
        }

        /**
         * Test DesignYieldStrengthForDuctileMechanisms using excel file values
         */

        @Test
        @DisplayName("Test using excel file values (LC1,440) -> 325.9259259259")
        void givenValidParams_whenGetDesignYieldStrength_expectExcelFileValues_LC1_440() {
            var params = buildParamsForDesignYieldStrengthForDuctileMechanisms(LC1, BigDecimal.valueOf(440));
            var result = materialStrengthService.getDesignYieldStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(325.9259259259), result);
        }

        @Test
        @DisplayName("Test using excel file values (LC2,440) -> 366.6666666667")
        void givenValidParams_whenGetDesignYieldStrength_expectExcelFileValues_LC2_440() {
            var params = buildParamsForDesignYieldStrengthForDuctileMechanisms(LC2, BigDecimal.valueOf(440));
            var result = materialStrengthService.getDesignYieldStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(366.6666666667), result);
        }

        @Test
        @DisplayName("Test using excel file values (LC3,440) -> 440")
        void givenValidParams_whenGetDesignYieldStrength_expectExcelFileValues_LC3_440() {
            var params = buildParamsForDesignYieldStrengthForDuctileMechanisms(LC3, BigDecimal.valueOf(440));
            var result = materialStrengthService.getDesignYieldStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(440).setScale(SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test using excel file values (LC1,1000) -> 740.7407407407")
        void givenValidParams_whenGetDesignYieldStrength_expectExcelFileValues_LC1_1000() {
            var params = buildParamsForDesignYieldStrengthForDuctileMechanisms(LC1, BigDecimal.valueOf(1000));
            var result = materialStrengthService.getDesignYieldStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(740.7407407407), result);
        }

        @Test
        @DisplayName("Test using excel file values (LC2,1000) -> 833.3333333333")
        void givenValidParams_whenGetDesignYieldStrength_expectExcelFileValues_LC2_1000() {
            var params = buildParamsForDesignYieldStrengthForDuctileMechanisms(LC2, BigDecimal.valueOf(1000));
            var result = materialStrengthService.getDesignYieldStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(833.3333333333), result);
        }

        @Test
        @DisplayName("Test using excel file values (LC3,1000) -> 1000.00")
        void givenValidParams_whenGetDesignYieldStrength_expectExcelFileValues_LC3_1000() {
            var params = buildParamsForDesignYieldStrengthForDuctileMechanisms(LC3, BigDecimal.valueOf(1000));
            var result = materialStrengthService.getDesignYieldStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(1000).setScale(SCALE, RoundingMode.HALF_UP), result);
        }
    }


    @Nested
    @DisplayName("Tests for DesignYieldStrengthForBrittleMechanisms fyd,f")
    class DesignYieldStrengthForBrittleMechanismsTests {

        private MaterialProperties buildParamsForDesignYieldStrengthForBrittleMechanisms(KnowledgeLevel knowledgeLevel, BigDecimal yieldStrength) {

            var steelGrade = new MaterialPropertiesSteelGrade();
            steelGrade.setYieldStrength(yieldStrength);

            var materialProperties = new MaterialProperties();
            materialProperties.setKnowledgeLevel(knowledgeLevel);
            materialProperties.setSteelGrade(steelGrade);

            return materialProperties;
        }

        @Test
        @DisplayName("Test with knowledgeLevel LC1")
        void testWithKnowledgeLevelLC1() {
            var params = buildParamsForDesignYieldStrengthForBrittleMechanisms(LC1, BigDecimal.valueOf(30.0));
            var result = materialStrengthService.getDesignYieldStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(30.0).divide(LC1.getValue(), SCALE, RoundingMode.HALF_UP).divide(BRITTLE_MECHANISM_YIELD_CONSTANT, SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with knowledgeLevel LC2")
        void testWithKnowledgeLevelLC2() {
            var params = buildParamsForDesignYieldStrengthForBrittleMechanisms(LC2, BigDecimal.valueOf(30.0));
            var result = materialStrengthService.getDesignYieldStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(30.0).divide(LC2.getValue(), SCALE, RoundingMode.HALF_UP).divide(BRITTLE_MECHANISM_YIELD_CONSTANT, SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with low yield strength")
        void testWithLowYieldStrength() {
            var params = buildParamsForDesignYieldStrengthForBrittleMechanisms(LC1, BigDecimal.valueOf(1.0));
            var result = materialStrengthService.getDesignYieldStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(1.0).divide(LC1.getValue(), SCALE, RoundingMode.HALF_UP).divide(BRITTLE_MECHANISM_YIELD_CONSTANT, SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with high yield strength")
        void testWithHighYieldStrength() {
            var params = buildParamsForDesignYieldStrengthForBrittleMechanisms(LC1, BigDecimal.valueOf(100.0));
            var result = materialStrengthService.getDesignYieldStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(100.0).divide(LC1.getValue(), SCALE, RoundingMode.HALF_UP).divide(BRITTLE_MECHANISM_YIELD_CONSTANT, SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC1,440) -> 283.4138486312")
        void givenValidParams_whenGetDesignYieldStrengthForBrittleMechanisms_expectExcelFileValues_LC1_440() {
            var params = buildParamsForDesignYieldStrengthForBrittleMechanisms(LC1, BigDecimal.valueOf(440.0));
            var result = materialStrengthService.getDesignYieldStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(283.4138486312), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC2,440) -> 318.8405797102")
        void givenValidParams_whenGetDesignYieldStrengthForBrittleMechanisms_expectExcelFileValues_LC2_440() {
            var params = buildParamsForDesignYieldStrengthForBrittleMechanisms(LC2, BigDecimal.valueOf(440.0));
            var result = materialStrengthService.getDesignYieldStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(318.8405797102), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC3,440) -> 382.6086956522")
        void givenValidParams_whenGetDesignYieldStrengthForBrittleMechanisms_expectExcelFileValues_LC3_440() {
            var params = buildParamsForDesignYieldStrengthForBrittleMechanisms(LC3, BigDecimal.valueOf(440.0));
            var result = materialStrengthService.getDesignYieldStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(382.6086956522).setScale(SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC1,1000) -> 644.1223832528")
        void givenValidParams_whenGetDesignYieldStrengthForBrittleMechanisms_expectExcelFileValues_LC1_1000() {
            var params = buildParamsForDesignYieldStrengthForBrittleMechanisms(LC1, BigDecimal.valueOf(1000.0));
            var result = materialStrengthService.getDesignYieldStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(644.1223832528), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC2,1000) -> 724.6376811594")
        void givenValidParams_whenGetDesignYieldStrengthForBrittleMechanisms_expectExcelFileValues_LC2_1000() {
            var params = buildParamsForDesignYieldStrengthForBrittleMechanisms(LC2, BigDecimal.valueOf(1000.0));
            var result = materialStrengthService.getDesignYieldStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(724.6376811594), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC3,1000) -> 869.5652173913")
        void givenValidParams_whenGetDesignYieldStrengthForBrittleMechanisms_expectExcelFileValues_LC3_1000() {
            var params = buildParamsForDesignYieldStrengthForBrittleMechanisms(LC3, BigDecimal.valueOf(1000.0));
            var result = materialStrengthService.getDesignYieldStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(869.5652173913).setScale(SCALE, RoundingMode.HALF_UP), result);
        }

    }


    @Nested
    @DisplayName("Tests for DesignCompressiveStrengthForDuctileMechanisms fcd,d")
    class DesignCompressiveStrengthForDuctileMechanismsTests {

        private MaterialProperties buildParamsForDesignCompressiveStrengthForDuctileMechanisms(KnowledgeLevel knowledgeLevel, BigDecimal compressiveStrength) {
            var concreteClass = new MaterialPropertiesReinforcedConcreteClass();
            concreteClass.setAverageCompressiveStrength(compressiveStrength);

            var materialProperties = new MaterialProperties();
            materialProperties.setKnowledgeLevel(knowledgeLevel);
            materialProperties.setConcreteClass(concreteClass);

            return materialProperties;
        }

        @Test
        @DisplayName("Test with knowledgeLevel LC1")
        void testWithKnowledgeLevelLC1() {
            var params = buildParamsForDesignCompressiveStrengthForDuctileMechanisms(LC1, BigDecimal.valueOf(30.0));
            var result = materialStrengthService.getDesignCompressiveStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(30.0).divide(LC1.getValue(), SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with knowledgeLevel LC2")
        void testWithKnowledgeLevelLC2() {
            var params = buildParamsForDesignCompressiveStrengthForDuctileMechanisms(LC2, BigDecimal.valueOf(30.0));
            var result = materialStrengthService.getDesignCompressiveStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(30.0).divide(LC2.getValue(), SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with low compressive strength")
        void testWithLowCompressiveStrength() {
            var params = buildParamsForDesignCompressiveStrengthForDuctileMechanisms(LC1, BigDecimal.valueOf(1.0));
            var result = materialStrengthService.getDesignCompressiveStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(1.0).divide(BigDecimal.valueOf(1.35), SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with high compressive strength")
        void testWithHighCompressiveStrength() {
            var params = buildParamsForDesignCompressiveStrengthForDuctileMechanisms(LC1, BigDecimal.valueOf(100.0));
            var result = materialStrengthService.getDesignCompressiveStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(100.0).divide(BigDecimal.valueOf(1.35), SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC1,25) -> 18.5185185185")
        void givenValidParams_whenGetDesignCompressiveStrength_expectExcelFileValues_LC1_25() {
            var params = buildParamsForDesignCompressiveStrengthForDuctileMechanisms(LC1, BigDecimal.valueOf(25.0));
            var result = materialStrengthService.getDesignCompressiveStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(18.5185185185), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC2,25) -> 20.8333333333")
        void givenValidParams_whenGetDesignCompressiveStrength_expectExcelFileValues_LC2_25() {
            var params = buildParamsForDesignCompressiveStrengthForDuctileMechanisms(LC2, BigDecimal.valueOf(25.0));
            var result = materialStrengthService.getDesignCompressiveStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(20.8333333333), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC3,25) -> 25.00")
        void givenValidParams_whenGetDesignCompressiveStrength_expectExcelFileValues_LC3_25() {
            var params = buildParamsForDesignCompressiveStrengthForDuctileMechanisms(LC3, BigDecimal.valueOf(25.0));
            var result = materialStrengthService.getDesignCompressiveStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(25).setScale(SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC1,16) -> 11.8518518519")
        void givenValidParams_whenGetDesignCompressiveStrength_expectExcelFileValues_LC1_16() {
            var params = buildParamsForDesignCompressiveStrengthForDuctileMechanisms(LC1, BigDecimal.valueOf(16.0));
            var result = materialStrengthService.getDesignCompressiveStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(11.8518518519), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC2,16) -> 13.3333333333")
        void givenValidParams_whenGetDesignCompressiveStrength_expectExcelFileValues_LC2_16() {
            var params = buildParamsForDesignCompressiveStrengthForDuctileMechanisms(LC2, BigDecimal.valueOf(16.0));
            var result = materialStrengthService.getDesignCompressiveStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(13.3333333333), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC3,16) -> 16.00")
        void givenValidParams_whenGetDesignCompressiveStrength_expectExcelFileValues_LC3_16() {
            var params = buildParamsForDesignCompressiveStrengthForDuctileMechanisms(LC3, BigDecimal.valueOf(16.0));
            var result = materialStrengthService.getDesignCompressiveStrengthForDuctileMechanisms(params);
            assertEquals(BigDecimal.valueOf(16).setScale(SCALE, RoundingMode.HALF_UP), result);
        }

    }


    @Nested
    @DisplayName("Tests for DesignCompressiveStrengthForBrittleMechanisms fcd,d")
    class DesignCompressiveStrengthForBrittleMechanismsTests {

        private MaterialProperties buildParamsForDesignCompressiveStrengthForBrittleMechanisms(KnowledgeLevel knowledgeLevel, BigDecimal compressiveStrength) {
            var concreteClass = new MaterialPropertiesReinforcedConcreteClass();
            concreteClass.setAverageCompressiveStrength(compressiveStrength);

            var materialProperties = new MaterialProperties();
            materialProperties.setKnowledgeLevel(knowledgeLevel);
            materialProperties.setConcreteClass(concreteClass);

            return materialProperties;
        }

        @Test
        @DisplayName("Test with knowledgeLevel LC1")
        void testWithKnowledgeLevelLC1() {
            var params = buildParamsForDesignCompressiveStrengthForBrittleMechanisms(LC1, BigDecimal.valueOf(30.0));
            var result = materialStrengthService.getDesignCompressiveStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(30.0).divide(LC1.getValue(), SCALE, RoundingMode.HALF_UP).divide(BRITTLE_MECHANISM_COMPRESSIVE_CONSTANT, SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with knowledgeLevel LC2")
        void testWithKnowledgeLevelLC2() {
            var params = buildParamsForDesignCompressiveStrengthForBrittleMechanisms(LC2, BigDecimal.valueOf(30.0));
            var result = materialStrengthService.getDesignCompressiveStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(30.0).divide(LC2.getValue(), SCALE, RoundingMode.HALF_UP).divide(BRITTLE_MECHANISM_COMPRESSIVE_CONSTANT, SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with low compressive strength")
        void testWithLowCompressiveStrength() {
            var params = buildParamsForDesignCompressiveStrengthForBrittleMechanisms(LC1, BigDecimal.valueOf(1.0));
            var result = materialStrengthService.getDesignCompressiveStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(1.0).divide(BigDecimal.valueOf(1.35), SCALE, RoundingMode.HALF_UP)
                    .divide(BRITTLE_MECHANISM_COMPRESSIVE_CONSTANT, SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with high compressive strength")
        void testWithHighCompressiveStrength() {
            var params = buildParamsForDesignCompressiveStrengthForBrittleMechanisms(LC1, BigDecimal.valueOf(100.0));
            var result = materialStrengthService.getDesignCompressiveStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(100.0).divide(BigDecimal.valueOf(1.35), SCALE, RoundingMode.HALF_UP)
                    .divide(BRITTLE_MECHANISM_COMPRESSIVE_CONSTANT, SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC1,48.00) -> 23.7037037037")
        void givenValidParams_whenGetDesignCompressiveStrengthForBrittleMechanisms_expectExcelFileValues_LC1_48() {
            var params = buildParamsForDesignCompressiveStrengthForBrittleMechanisms(LC1, BigDecimal.valueOf(48.00));
            var result = materialStrengthService.getDesignCompressiveStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(23.7037037037).setScale(SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC2,48.00) -> 26.6666666667")
        void givenValidParams_whenGetDesignCompressiveStrengthForBrittleMechanisms_expectExcelFileValues_LC2_48() {
            var params = buildParamsForDesignCompressiveStrengthForBrittleMechanisms(LC2, BigDecimal.valueOf(48.00));
            var result = materialStrengthService.getDesignCompressiveStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(26.6666666667), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC3,48.00) -> 32.00")
        void givenValidParams_whenGetDesignCompressiveStrengthForBrittleMechanisms_expectExcelFileValues_LC3_48() {
            var params = buildParamsForDesignCompressiveStrengthForBrittleMechanisms(LC3, BigDecimal.valueOf(48.00));
            var result = materialStrengthService.getDesignCompressiveStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(32.00).setScale(SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC1,16) -> 7.9012345679")
        void givenValidParams_whenGetDesignCompressiveStrengthForBrittleMechanisms_expectExcelFileValues_LC1_16() {
            var params = buildParamsForDesignCompressiveStrengthForBrittleMechanisms(LC1, BigDecimal.valueOf(16.0));
            var result = materialStrengthService.getDesignCompressiveStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(7.9012345679).setScale(SCALE, RoundingMode.HALF_UP), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC2,16) -> 8.8888888889")
        void givenValidParams_whenGetDesignCompressiveStrengthForBrittleMechanisms_expectExcelFileValues_LC2_16() {
            var params = buildParamsForDesignCompressiveStrengthForBrittleMechanisms(LC2, BigDecimal.valueOf(16.0));
            var result = materialStrengthService.getDesignCompressiveStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(8.8888888889), result);
        }

        @Test
        @DisplayName("Test with excel file values (LC3,16) -> 10.6666666667")
        void givenValidParams_whenGetDesignCompressiveStrengthForBrittleMechanisms_expectExcelFileValues_LC3_16() {
            var params = buildParamsForDesignCompressiveStrengthForBrittleMechanisms(LC3, BigDecimal.valueOf(16.0));
            var result = materialStrengthService.getDesignCompressiveStrengthForBrittleMechanisms(params);
            assertEquals(BigDecimal.valueOf(10.6666666667).setScale(SCALE, RoundingMode.HALF_UP), result);
        }

    }
}