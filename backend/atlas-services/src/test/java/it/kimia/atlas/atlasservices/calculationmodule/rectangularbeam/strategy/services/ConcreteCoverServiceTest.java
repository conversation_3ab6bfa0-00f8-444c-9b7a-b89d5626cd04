package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.Polarity;
import it.kimia.atlas.atlasservices.calculationmodule.dto.RebarsDefinition;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamGeometry;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamParams;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamRebar;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertEquals;

class ConcreteCoverServiceTest {
    private ConcreteCoverService concreteCoverService;

    @BeforeEach
    void setUp() {
        concreteCoverService = new ConcreteCoverService();
    }

    private RectangularBeamParams buildParams(Polarity polarity) {
        var geometry = new RectangularBeamGeometry();
        geometry.setTopConcreteCover(BigDecimal.valueOf(30));
        geometry.setBottomConcreteCover(BigDecimal.valueOf(25));

        var topBar = new RebarsDefinition();
        topBar.setArea(BigDecimal.valueOf(200));

        var bottomBar = new RebarsDefinition();
        bottomBar.setArea(BigDecimal.valueOf(180));

        var reinforcement = new RectangularBeamRebar();
        reinforcement.setTop(topBar);
        reinforcement.setBottom(bottomBar);

        var params = new RectangularBeamParams();
        params.setPolarity(polarity);
        params.setGeometry(geometry);
        params.setReinforcementBar(reinforcement);

        return params;
    }

    @Test
    @DisplayName("Restituisce il copriferro superiore per polarità POSITIVE")
    void getCompressedCover_positivePolarity() {
        var params = buildParams(Polarity.POSITIVE);

        var result = concreteCoverService.getCompressedCover(params);

        assertEquals(BigDecimal.valueOf(30), result);
    }

    @Test
    @DisplayName("Restituisce il copriferro inferiore per polarità NEGATIVE")
    void getCompressedCover_negativePolarity() {
        var params = buildParams(Polarity.NEGATIVE);

        var result = concreteCoverService.getCompressedCover(params);

        assertEquals(BigDecimal.valueOf(25), result);
    }


    @Test
    @DisplayName("Restituisce il copriferro teso per polarità POSITIVE")
    void getTensionCover_positivePolarity() {
        var params = buildParams(Polarity.POSITIVE);

        var result = concreteCoverService.getTensionCover(params);

        assertEquals(BigDecimal.valueOf(25), result);
    }

    @Test
    @DisplayName("Restituisce il copriferro teso per polarità NEGATIVE")
    void getTensionCover_negativePolarity() {
        var params = buildParams(Polarity.NEGATIVE);

        var result = concreteCoverService.getTensionCover(params);

        assertEquals(BigDecimal.valueOf(30), result);
    }

    @Test
    @DisplayName("Restituisce l'area dell'acciaio compresso per polarità POSITIVE")
    void getCompressiveSteelArea_positivePolarity() {
        var params = buildParams(Polarity.POSITIVE);

        var result = concreteCoverService.getCompressiveSteelArea(params);

        assertEquals(BigDecimal.valueOf(200), result);
    }

    @Test
    @DisplayName("Restituisce l'area dell'acciaio compresso per polarità NEGATIVE")
    void getCompressiveSteelArea_negativePolarity() {
        var params = buildParams(Polarity.NEGATIVE);

        var result = concreteCoverService.getCompressiveSteelArea(params);

        assertEquals(BigDecimal.valueOf(180), result);
    }

    @Test
    @DisplayName("Restituisce l'area dell'acciaio teso per polarità POSITIVE")
    void getTensileSteelArea_positivePolarity() {
        var params = buildParams(Polarity.POSITIVE);

        var result = concreteCoverService.getTensileSteelArea(params);

        assertEquals(BigDecimal.valueOf(180), result);
    }

    @Test
    @DisplayName("Restituisce l'area dell'acciaio teso per polarità NEGATIVE")
    void getTensileSteelArea_negativePolarity() {
        var params = buildParams(Polarity.NEGATIVE);

        var result = concreteCoverService.getTensileSteelArea(params);

        assertEquals(BigDecimal.valueOf(200), result);
    }
}