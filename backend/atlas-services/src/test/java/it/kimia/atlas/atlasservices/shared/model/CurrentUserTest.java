package it.kimia.atlas.atlasservices.shared.model;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class CurrentUserTest {

    @Test
    @DisplayName("User should have visibility to their own project")
    void hasVisibilityToWhenProjectIsAssignedToCurrentUser() {
        String projectUserId = "user-id";

        CurrentUser currentUser = CurrentUser.builder()
                .id(projectUserId)
                .build();

        boolean hasVisibility = currentUser.hasVisibilityTo(projectUserId);

        assertTrue(hasVisibility, "User should have visibility to their own project");
    }

    @Test
    @DisplayName("User that has subgroup that contains project user should have visibility to the project")
    void hasVisibilityToWhenProjectIsAssignedToUserInSubgroup() {
        String projectUserId = "group-user-id";

        UserGroup userGroup = mock(UserGroup.class);
        when(userGroup.getUserIds()).thenReturn(List.of(projectUserId));

        CurrentUser currentUser = CurrentUser.builder()
                .id("user-id-1")
                .groups(List.of(userGroup))
                .build();

        boolean hasVisibility = currentUser.hasVisibilityTo(projectUserId);

        assertTrue(hasVisibility, "User that has subgroup that contains project user should have visibility to the project");
    }

    @Test
    @DisplayName("User that has no subgroup that contains project user should not have visibility to the project")
    void hasVisibilityWhenProjectUserIdIsNullShouldReturnFalse() {
        CurrentUser currentUser = CurrentUser.builder()
                .id("user-id-1")
                .build();

        boolean hasVisibility = currentUser.hasVisibilityTo(null);
        assertFalse(hasVisibility, "hasVisibility should return false when projectUserId is null");
    }

    @Test
    @DisplayName("Given user without subgroup when get visibility cone should return only user id")
    void givenUserWithoutSubgroupWhenGetVisibilityConeShouldReturnOnlyUserId() {
        CurrentUser currentUser = CurrentUser.builder()
                .id("user-id-1")
                .build();

        Set<String> visibilityCone = currentUser.getVisibilityCone();

        assertNotNull(visibilityCone, "VisibilityCone should not be null");
        assertFalse(visibilityCone.isEmpty(), "Visibility cone should not be empty");
        assertEquals(1, visibilityCone.size(), "Visibility cone should contain only the user ID");
        assertTrue(visibilityCone.contains("user-id-1"), "Visibility cone should contain the user ID");
    }

    @Test
    @DisplayName("Given user with subgroup when get visibility cone should return all user ids and himself")
    void givenUserWithSubgroupWhenGetVisibilityConeShouldReturnAllUserIdsAndHimself() {
        List<String> subusersIds = List.of("subgroup-user-id-1", "subgroup-user-id-2");
        UserGroup userGroup = mock(UserGroup.class);
        when(userGroup.getUserIds()).thenReturn(subusersIds);

        CurrentUser currentUser = CurrentUser.builder()
                .id("user-id-1")
                .groups(List.of(userGroup))
                .build();
        Set<String> visibilityCone = currentUser.getVisibilityCone();

        assertNotNull(visibilityCone, "VisibilityCone should not be null");
        assertFalse(visibilityCone.isEmpty(), "Visibility cone should not be empty");
        assertEquals(subusersIds.size() + 1, visibilityCone.size(), "Visibility cone should contain all user IDs and himself");
        assertTrue(visibilityCone.contains("user-id-1"), "Visibility cone should contain the user ID");
        subusersIds.forEach(id -> assertTrue(visibilityCone.contains(id), "Visibility cone should contain the user ID"));
    }

}