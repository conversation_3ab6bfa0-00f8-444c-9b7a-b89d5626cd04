package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.*;
import it.kimia.atlas.atlasservices.calculationmodule.dto.MaterialProperties;
import it.kimia.atlas.atlasservices.calculationmodule.dto.MaterialPropertiesReinforcedConcreteClass;
import it.kimia.atlas.atlasservices.calculationmodule.dto.MaterialPropertiesSteelGrade;
import it.kimia.atlas.atlasservices.calculationmodule.dto.RebarsDefinition;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamGeometry;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamParams;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.RectangularBeamRebar;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInputImpl;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.output.FlexuralCalculationResult;
import it.kimia.atlas.atlasservices.calculationmodule.utility.BrentRootFinderAdapter;
import it.kimia.atlas.atlasservices.calculationmodule.utility.RootFinder;
import it.kimia.atlas.atlasservices.product.FiberType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class RectangularBeamFlexuralVerifyServiceTest {

    @Spy
    private ConcreteCoverService concreteCoverService;
    @Spy
    private GeometricCoefficientService geometricCoefficientService;
    @Spy
    private FrpUtilsService frpUtilsService;
    @Spy
    private EnvironmentalAndSafetyFactorService environmentalAndSafetyFactorService;
    @Spy
    private MaterialStrengthService materialStrengthService;
    @Mock
    private BrentRootFinderAdapter rootFinderAdapter;

    private ReinforcementRatioService reinforcementRatioService;
    private FailureModeService failureModeService;
    ;

    private RectangularBeamFlexuralVerifyService calculationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        reinforcementRatioService = new ReinforcementRatioService(
                concreteCoverService,
                materialStrengthService,
                frpUtilsService
        );
        failureModeService = new FailureModeService(reinforcementRatioService);
        calculationService = new RectangularBeamFlexuralVerifyService(concreteCoverService,
                environmentalAndSafetyFactorService,
                failureModeService,
                frpUtilsService,
                geometricCoefficientService,
                materialStrengthService,
                rootFinderAdapter);
    }


    /**
     * Test focalizzati esclusivamente su calculateMomentCapacity(...).
     * Formula Excel Mrd (N·mm):
     * = C71*C6*C69*C17*(C10 - C72*C69)   // calcestruzzo
     * + C12*C81*(C10 - C9)               // acciaio compresso
     * + C34*C35*C36*C82*(C10 - C9)       // FRP
     * poi ÷ 1_000_000 => [kN·m]
     */
    @Nested
    class CalculateMomentCapacityPolarityTests {

        private RectangularBeamParams buildParams(Polarity polarity,
                                                  BigDecimal C6_b, BigDecimal C10_d,
                                                  BigDecimal topCover, BigDecimal bottomCover,
                                                  BigDecimal AsTop, BigDecimal AsBottom) {
            var params = mock(RectangularBeamParams.class, RETURNS_DEEP_STUBS);
            when(params.getPolarity()).thenReturn(polarity);
            when(params.getGeometry().getWidth()).thenReturn(C6_b);               // C6
            when(params.getGeometry().getEffectiveDepth()).thenReturn(C10_d);     // C10
            when(params.getGeometry().getTopConcreteCover()).thenReturn(topCover);
            when(params.getGeometry().getBottomConcreteCover()).thenReturn(bottomCover);
            when(params.getReinforcementBar().getTop().getArea()).thenReturn(AsTop);
            when(params.getReinforcementBar().getBottom().getArea()).thenReturn(AsBottom);
            return params;
        }

        private FlexuralVerifyExecutionInput buildInput(int C35_bf, int C36_nf, BigDecimal C34_tf) {
            var input = mock(FlexuralVerifyExecutionInput.class, RETURNS_DEEP_STUBS);
            var product = mock(ReinforcedConcreteProduct.class);
            when(input.getProduct()).thenReturn(product);
            when(product.getThickness()).thenReturn(C34_tf);     // C34
            when(input.getStripWidth()).thenReturn(C35_bf);      // C35
            when(input.getLayersNumber()).thenReturn(C36_nf);    // C36
            when(input.getBendingMoment()).thenReturn(0);
            return input;
        }

        @Test
        void mPlus_usesTopCoverAsCompressed_andTopAs2() {
            // M+ -> cover compresso = top; As2 = top
            BigDecimal C6 = bd(300), C10 = bd(470);
            BigDecimal C9_top = bd(35), C8_bottom = bd(25);
            BigDecimal AsTop = bd(210), AsBottom = bd(190);
            BigDecimal C69 = bd(150), C71 = bd("0.85"), C72 = bd("0.42"), C17 = bd(20);
            BigDecimal C81 = bd(300), C34 = bd("0.167");
            int C35 = 100;
            int C36 = 2;
            BigDecimal C82 = bd(900);

            var params = buildParams(Polarity.POSITIVE, C6, C10, C9_top, C8_bottom, AsTop, AsBottom);
            var input = buildInput(C35, C36, C34);
            var svc = new TestableService(concreteCoverService,
                    environmentalAndSafetyFactorService,
                    failureModeService,
                    frpUtilsService,
                    geometricCoefficientService,
                    materialStrengthService,
                    rootFinderAdapter);
            svc.C69_x = C69;
            svc.C71_psi = C71;
            svc.C72_lambda = C72;
            svc.C17_fcd = C17;
            svc.C81_sigmas2 = C81;
            svc.C82_sigmaf = C82;

            // atteso
            BigDecimal frpArea = C34.multiply(bd(C35)).multiply(bd(C36));
            BigDecimal concrete = C71.multiply(C6).multiply(C69).multiply(C17)
                    .multiply(C10.subtract(C72.multiply(C69)));
            BigDecimal steel = AsTop.multiply(C81).multiply(C10.subtract(C9_top)); // top cover
            BigDecimal frp = frpArea.multiply(C82).multiply(C9_top);
            BigDecimal expectedNmm = concrete.add(steel).add(frp);

            var out = svc.calculateMomentCapacity(params, input, FlexuralCalculationResult.builder().build());
            BigDecimal gotNmm = out.getMomentCapacity().multiply(bd(1_000_000));
            assertEquals(0, gotNmm.compareTo(expectedNmm.setScale(SCALE, RoundingMode.HALF_UP)));
        }

        @Test
        void mMinus_usesBottomCoverAsCompressed_andBottomAs2() {
            // M− -> cover compresso = bottom; As2 = bottom
            BigDecimal C6 = bd(300), C10 = bd(470);
            BigDecimal C9_top = bd(40), C8_bottom = bd(30); // rendiamoli diversi per “vedere” il braccio
            BigDecimal AsTop = bd(220), AsBottom = bd(200);
            BigDecimal C69 = bd(140), C71 = bd("0.85"), C72 = bd("0.42"), C17 = bd(20);
            BigDecimal C81 = bd(290), C34 = bd("0.167");
            int C35 = 80;
            int C36 = 1;
            BigDecimal C82 = bd(850);

            var params = buildParams(Polarity.NEGATIVE, C6, C10, C9_top, C8_bottom, AsTop, AsBottom);
            var input = buildInput(C35, C36, C34);
            var svc = new TestableService(concreteCoverService,
                    environmentalAndSafetyFactorService,
                    failureModeService,
                    frpUtilsService,
                    geometricCoefficientService,
                    materialStrengthService,
                    rootFinderAdapter);
            svc.C69_x = C69;
            svc.C71_psi = C71;
            svc.C72_lambda = C72;
            svc.C17_fcd = C17;
            svc.C81_sigmas2 = C81;
            svc.C82_sigmaf = C82;

            // atteso
            BigDecimal frpArea = C34.multiply(bd(C35)).multiply(bd(C36));
            BigDecimal concrete = C71.multiply(C6).multiply(C69).multiply(C17)
                    .multiply(C10.subtract(C72.multiply(C69)));
            BigDecimal steel = AsBottom.multiply(C81).multiply(C10.subtract(C8_bottom)); // bottom cover come braccio
            BigDecimal frp = frpArea.multiply(C82).multiply(C8_bottom);
            BigDecimal expectedNmm = concrete.add(steel).add(frp);

            var out = svc.calculateMomentCapacity(params, input, FlexuralCalculationResult.builder().build());
            BigDecimal gotNmm = out.getMomentCapacity().multiply(bd(1_000_000));
            assertEquals(0, gotNmm.compareTo(expectedNmm.setScale(SCALE, RoundingMode.HALF_UP)));
        }

        private BigDecimal bd(double v) {
            return BigDecimal.valueOf(v);
        }

        private BigDecimal bd(String v) {
            return new BigDecimal(v);
        }

        /**
         * Service di test che inietta i valori dei “Cxx” per validare l’algebra, non la catena completa.
         */
        class TestableService extends RectangularBeamFlexuralVerifyService {
            BigDecimal C69_x;       // neutral axis
            BigDecimal C71_psi;     // ψ
            BigDecimal C72_lambda;  // λ
            BigDecimal C17_fcd;     // fcd
            BigDecimal C81_sigmas2; // σs2 (acciaio compresso)
            BigDecimal C82_sigmaf;  // σf (FRP)

            public TestableService(ConcreteCoverService concreteCoverService,
                                   EnvironmentalAndSafetyFactorService environmentalAndSafetyFactorService,
                                   FailureModeService failureModeService,
                                   FrpUtilsService frpUtilsService,
                                   GeometricCoefficientService geometricCoefficientService,
                                   MaterialStrengthService materialStrengthService,
                                   RootFinder rootFinderAdapter) {
                super(concreteCoverService,
                        environmentalAndSafetyFactorService,
                        failureModeService,
                        frpUtilsService,
                        geometricCoefficientService,
                        materialStrengthService, rootFinderAdapter);
            }

            @Override
            protected FlexuralCalculationResult calculateNeutralAxisPosition(RectangularBeamParams params,
                                                                             FlexuralVerifyExecutionInput input,
                                                                             FlexuralCalculationResult previous) {
                return previous.toBuilder().neutralAxisPosition(C69_x).build();
            }

            @Override
            protected FlexuralCalculationResult calculateNeutralAxisPositionEnhanced(RectangularBeamParams params,
                                                                                     FlexuralVerifyExecutionInput input,
                                                                                     FlexuralCalculationResult previous) {
                return previous.toBuilder().neutralAxisPosition(C69_x).build();
            }

            @Override
            protected FlexuralCalculationResult calculateFirstAdimensionalCoefficient(RectangularBeamParams params,
                                                                                      FlexuralVerifyExecutionInput input,
                                                                                      BigDecimal neutralAxisPosition,
                                                                                      FlexuralCalculationResult previous) {
                return previous.toBuilder().firstAdimensionalCoefficient(C71_psi).build();
            }

            @Override
            protected FlexuralCalculationResult calculateSecondDimensionlessCoefficient(RectangularBeamParams params,
                                                                                        FlexuralVerifyExecutionInput input,
                                                                                        BigDecimal neutralAxisPosition,
                                                                                        FlexuralCalculationResult previous) {
                return previous.toBuilder().secondAdimensionalCoefficient(C72_lambda).build();
            }

            @Override
            protected BigDecimal getDesignCompressiveStrengthForDuctileMechanisms(RectangularBeamParams params) {
                return C17_fcd;
            }

            @Override
            protected FlexuralCalculationResult calculateSteelCompressedTension(RectangularBeamParams params,
                                                                                FlexuralVerifyExecutionInput input,
                                                                                BigDecimal neutralAxisPosition,
                                                                                FlexuralCalculationResult previous) {
                return previous.toBuilder().steelCompressedTension(C81_sigmas2).build();
            }

            @Override
            protected FlexuralCalculationResult calculateFrpStress(RectangularBeamParams params,
                                                                   FlexuralVerifyExecutionInput input,
                                                                   BigDecimal neutralAxisPosition,
                                                                   FlexuralCalculationResult previous) {
                return previous.toBuilder().frpStress(C82_sigmaf).build();
            }
        }
    }

    /**
     * TEST calculateCompressedSteelStrain C77
     *
     */
    @Nested
    @DisplayName("Sezione: Calcolo della deformazione dell'acciaio compresso")
    class CalculateCompressedSteelStrainTests {

        @DisplayName("Calculate compressed steel strain for ductile failure mode")
        void calculateCompressedSteelStrain_givenDuctileFailureMode_returnsCorrectStrain() {
            // Setup
            var params = new RectangularBeamParams();
            var geometry = new RectangularBeamGeometry();
            geometry.setBottomConcreteCover(BigDecimal.valueOf(30));
            geometry.setHeight(BigDecimal.valueOf(500));
            geometry.setWidth(BigDecimal.valueOf(300));
            geometry.setEffectiveDepth(BigDecimal.valueOf(470));
            geometry.setExposure(Exposure.INTERNAL);
            params.setGeometry(geometry);
            params.setInitialDeformation(BigDecimal.valueOf(0.001));
            var materialProps = new MaterialProperties();
            var concreteClass = new MaterialPropertiesReinforcedConcreteClass();
            concreteClass.setAverageCompressiveStrength(BigDecimal.valueOf(30));
            materialProps.setConcreteClass(concreteClass);
            var steelGrade = new MaterialPropertiesSteelGrade();
            steelGrade.setElasticModulus(200_000);
            steelGrade.setYieldStrength(BigDecimal.valueOf(450));
            materialProps.setSteelGrade(steelGrade);
            materialProps.setKnowledgeLevel(KnowledgeLevel.LC1);
            params.setMaterialProperties(materialProps);
            // Ensure reinforcement is present
            var topBar = new RebarsDefinition();
            topBar.setArea(BigDecimal.valueOf(226.19));
            var bottomBar = new RebarsDefinition();
            bottomBar.setArea(BigDecimal.valueOf(226.19));
            var reinforcement = new RectangularBeamRebar();
            reinforcement.setTop(topBar);
            reinforcement.setBottom(bottomBar);
            params.setReinforcementBar(reinforcement);
            params.setPolarity(Polarity.NEGATIVE);
            geometry.setTopConcreteCover(BigDecimal.valueOf(30));

            var product = new ReinforcedConcreteProductImpl(
                    "test",
                    "testProduct",
                    BigDecimal.valueOf(1.4),
                    BigDecimal.valueOf(150_000),
                    BigDecimal.valueOf(0.015),
                    FiberType.PREFORMED_CARBON
            );

            var input = new FlexuralVerifyExecutionInputImpl(300, 1, 50, product);
            var previous = FlexuralCalculationResult.builder().failureMode(1).maxDesignReinforcementStrain(BigDecimal.valueOf(0.002)).build();

            var neutralAxis = BigDecimal.valueOf(200);
            var result = calculationService.calculateCompressedSteelStrain(params, input, neutralAxis, previous);

            // Assert
            var expected = BigDecimal.valueOf(0.002)
                    .add(BigDecimal.valueOf(0.001))
                    .multiply(neutralAxis.subtract(BigDecimal.valueOf(30)))
                    .divide(geometry.getHeight().subtract(neutralAxis), SCALE, RoundingMode.HALF_UP);

            assertNotNull(result);
            assertEquals(0, result.getCompressedSteelStrain().compareTo(expected));
        }

        @DisplayName("Calculate compressed steel strain for brittle failure mode")
        void calculateCompressedSteelStrain_givenBrittleFailureMode_returnsCorrectStrain() {
            // Setup
            var params = new RectangularBeamParams();
            var geometry = new RectangularBeamGeometry();
            geometry.setBottomConcreteCover(BigDecimal.valueOf(30));
            geometry.setWidth(BigDecimal.valueOf(300));
            geometry.setEffectiveDepth(BigDecimal.valueOf(470));
            geometry.setExposure(Exposure.INTERNAL);
            params.setGeometry(geometry);
            params.setPolarity(Polarity.NEGATIVE);
            // Material properties
            var materialProps = new MaterialProperties();
            var concreteClass = new MaterialPropertiesReinforcedConcreteClass();
            concreteClass.setAverageCompressiveStrength(BigDecimal.valueOf(30));
            materialProps.setConcreteClass(concreteClass);
            var steelGrade = new MaterialPropertiesSteelGrade();
            steelGrade.setElasticModulus(200_000);
            steelGrade.setYieldStrength(BigDecimal.valueOf(450));
            materialProps.setSteelGrade(steelGrade);
            materialProps.setKnowledgeLevel(KnowledgeLevel.LC1);
            params.setMaterialProperties(materialProps);
            // Ensure reinforcement is present
            var topBar = new RebarsDefinition();
            topBar.setArea(BigDecimal.valueOf(226.19));
            var bottomBar = new RebarsDefinition();
            bottomBar.setArea(BigDecimal.valueOf(226.19));
            var reinforcement = new RectangularBeamRebar();
            reinforcement.setTop(topBar);
            reinforcement.setBottom(bottomBar);
            params.setReinforcementBar(reinforcement);
            var product = new ReinforcedConcreteProductImpl(
                    "test",
                    "testProduct",
                    BigDecimal.valueOf(1.4),
                    BigDecimal.valueOf(150_000),
                    BigDecimal.valueOf(0.015),
                    FiberType.PREFORMED_CARBON
            );

            var input = new FlexuralVerifyExecutionInputImpl(300, 1, 50, product);
            var previous = FlexuralCalculationResult.builder().failureMode(2).build();

            var neutralAxis = BigDecimal.valueOf(200);
            var result = calculationService.calculateCompressedSteelStrain(params, input, neutralAxis, previous);

            // Assert
            var compressedCover = geometry.getBottomConcreteCover();
            var expected = new BigDecimal("0.0035")
                    .multiply(neutralAxis.subtract(compressedCover))
                    .divide(neutralAxis, SCALE, RoundingMode.HALF_UP);

            assertNotNull(result);
            assertEquals(0, result.getCompressedSteelStrain().compareTo(expected));
        }

        @DisplayName("Calculate compressed steel strain with neutral axis at bottom concrete cover")
        void calculateCompressedSteelStrain_givenNeutralAxisAtBottomConcreteCover_returnsZeroStrain() {
            // Setup
            var params = new RectangularBeamParams();
            var geometry = new RectangularBeamGeometry();
            geometry.setBottomConcreteCover(BigDecimal.valueOf(30));
            geometry.setWidth(BigDecimal.valueOf(300));
            geometry.setEffectiveDepth(BigDecimal.valueOf(470));
            geometry.setExposure(Exposure.INTERNAL);
            params.setGeometry(geometry);
            params.setPolarity(Polarity.NEGATIVE);
            // Material properties
            var materialProps = new MaterialProperties();
            var concreteClass = new MaterialPropertiesReinforcedConcreteClass();
            concreteClass.setAverageCompressiveStrength(BigDecimal.valueOf(30));
            materialProps.setConcreteClass(concreteClass);
            var steelGrade = new MaterialPropertiesSteelGrade();
            steelGrade.setElasticModulus(200_000);
            steelGrade.setYieldStrength(BigDecimal.valueOf(450));
            materialProps.setSteelGrade(steelGrade);
            materialProps.setKnowledgeLevel(KnowledgeLevel.LC1);
            params.setMaterialProperties(materialProps);
            // Ensure reinforcement is present
            var topBar = new RebarsDefinition();
            topBar.setArea(BigDecimal.valueOf(226.19));
            var bottomBar = new RebarsDefinition();
            bottomBar.setArea(BigDecimal.valueOf(226.19));
            var reinforcement = new RectangularBeamRebar();
            reinforcement.setTop(topBar);
            reinforcement.setBottom(bottomBar);
            params.setReinforcementBar(reinforcement);

            var product = new ReinforcedConcreteProductImpl(
                    "test",
                    "testProduct",
                    BigDecimal.valueOf(1.4),
                    BigDecimal.valueOf(150_000),
                    BigDecimal.valueOf(0.015),
                    FiberType.PREFORMED_CARBON
            );

            var input = new FlexuralVerifyExecutionInputImpl(300, 1, 50, product);
            var previous = FlexuralCalculationResult.builder().failureMode(1).maxDesignReinforcementStrain(BigDecimal.valueOf(0.002)).build();

            // Act
            var result = calculationService.calculateCompressedSteelStrain(params, input, BigDecimal.valueOf(30), previous);

            // Assert
            assertNotNull(result);
            assertEquals(BigDecimal.ZERO, result.getCompressedSteelStrain());
        }
    }

    @Nested
    @DisplayName("Sezione: Calcolo della modalità di rottura (Failure Mode)")
    class GetFailureModeTests {

        @DisplayName("Restituisce modalità duttile se il rapporto di armatura meccanica è uguale al limite")
        void getFailureMode_mechanicalReinforcementRatioEqualsLimit_returnsDuctileFailureMode() {
            var params = new RectangularBeamParams();
            var geometry = new RectangularBeamGeometry();
            geometry.setWidth(BigDecimal.valueOf(300));
            geometry.setEffectiveDepth(BigDecimal.valueOf(470));
            geometry.setHeight(BigDecimal.valueOf(500));
            params.setGeometry(geometry);

            var materialProps = new MaterialProperties();
            var concreteClass = new MaterialPropertiesReinforcedConcreteClass();
            concreteClass.setAverageCompressiveStrength(BigDecimal.valueOf(30));
            concreteClass.setAverageTensileStrength(BigDecimal.valueOf(2.38));
            materialProps.setConcreteClass(concreteClass);
            var steelGrade = new MaterialPropertiesSteelGrade();
            steelGrade.setElasticModulus(200_000);
            steelGrade.setYieldStrength(BigDecimal.valueOf(440));
            materialProps.setSteelGrade(steelGrade);
            materialProps.setKnowledgeLevel(KnowledgeLevel.LC1);
            params.setMaterialProperties(materialProps);

            var reinforcement = new RectangularBeamRebar();
            var topBar = new RebarsDefinition();
            topBar.setArea(BigDecimal.valueOf(226.19));
            var bottomBar = new RebarsDefinition();
            bottomBar.setArea(BigDecimal.valueOf(226.19));
            reinforcement.setTop(topBar);
            reinforcement.setBottom(bottomBar);
            params.setReinforcementBar(reinforcement);

            var product = new ReinforcedConcreteProductImpl(
                    "test",
                    "testProduct",
                    BigDecimal.valueOf(1.4),
                    BigDecimal.valueOf(150_000),
                    BigDecimal.valueOf(0.015),
                    FiberType.PREFORMED_CARBON
            );

            var input = new FlexuralVerifyExecutionInputImpl(300, 1, 50, product);
            var previous = FlexuralCalculationResult.builder().build();

            var result = calculationService.getFailureMode(params, input, previous);

            assertNotNull(result);
            assertEquals(1, result.getFailureMode());
        }

        @DisplayName("Restituisce modalità fragile se il rapporto di armatura meccanica è maggiore del limite")
        void getFailureMode_mechanicalReinforcementRatioGreaterThanLimit_returnsBrittleFailureMode() {
            var params = new RectangularBeamParams();
            var geometry = new RectangularBeamGeometry();
            geometry.setWidth(BigDecimal.valueOf(300));
            geometry.setEffectiveDepth(BigDecimal.valueOf(470));
            geometry.setHeight(BigDecimal.valueOf(500));
            params.setGeometry(geometry);

            var materialProps = new MaterialProperties();
            var concreteClass = new MaterialPropertiesReinforcedConcreteClass();
            concreteClass.setAverageCompressiveStrength(BigDecimal.valueOf(30));
            concreteClass.setAverageTensileStrength(BigDecimal.valueOf(2.38));
            materialProps.setConcreteClass(concreteClass);
            params.setMaterialProperties(materialProps);

            var reinforcement = new RectangularBeamRebar();
            var topBar = new RebarsDefinition();
            topBar.setArea(BigDecimal.valueOf(100));
            var bottomBar = new RebarsDefinition();
            bottomBar.setArea(BigDecimal.valueOf(500));
            reinforcement.setTop(topBar);
            reinforcement.setBottom(bottomBar);
            params.setReinforcementBar(reinforcement);

            var product = new ReinforcedConcreteProductImpl(
                    "test",
                    "testProduct",
                    BigDecimal.valueOf(1.4),
                    BigDecimal.valueOf(150_000),
                    BigDecimal.valueOf(0.015),
                    FiberType.PREFORMED_CARBON
            );

            var input = new FlexuralVerifyExecutionInputImpl(300, 1, 50, product);
            var previous = FlexuralCalculationResult.builder().build();

            var result = calculationService.getFailureMode(params, input, previous);

            assertNotNull(result);
            assertEquals(2, result.getFailureMode());
        }

        @DisplayName("Gestisce parametri nulli lanciando eccezione")
        void getFailureMode_givenNullParams_throwsException() {
            var input = new FlexuralVerifyExecutionInputImpl(300, 1, 50, null);
            var previous = FlexuralCalculationResult.builder().build();

            assertThrows(NullPointerException.class, () -> calculationService.getFailureMode(null, input, previous));
        }
    }
}