package it.kimia.atlas.atlasservices.calculationmodule.wood;

import com.fasterxml.jackson.databind.ObjectMapper;
import it.kimia.atlas.atlasservices.calculationmodule.CalculationModule;
import it.kimia.atlas.atlasservices.calculationmodule.ModuleType;
import it.kimia.atlas.atlasservices.calculationmodule.ParamApplierFactory;
import it.kimia.atlas.atlasservices.materials.WoodCategory;
import it.kimia.atlas.atlasservices.calculationmodule.wood.strategy.enums.WoodGeometryLoadDuration;
import it.kimia.atlas.atlasservices.calculationmodule.wood.strategy.enums.WoodGeometryServiceClass;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for Wood module parameter application.
 */
@SpringBootTest
@DisplayName("Wood Module Integration Tests")
class WoodModuleIntegrationTest {

    @Autowired
    private ParamApplierFactory paramApplierFactory;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @DisplayName("Should successfully apply parameters to Wood module")
    void shouldApplyParametersToWoodModule() {
        // Given
        WoodModule woodModule = new WoodModule();
        woodModule.setName("Test Wood Module");

        // Create test parameters
        WoodParams params = createTestWoodParams();

        // When
        var applier = paramApplierFactory.get(woodModule);
        applier.apply(woodModule, params);

        // Then
        assertNotNull(woodModule.getParams());
        assertEquals(ModuleType.WOOD, applier.getModuleType());
        assertEquals(WoodCategory.CONIFER_AND_POPLAR_WOOD, woodModule.getParams().getMaterialProperties().getCategory());
        assertEquals(new BigDecimal("200.0"), woodModule.getParams().getGeometry().getBeamSectionWidth());
    }

    @Test
    @DisplayName("Should handle partial parameter updates")
    void shouldHandlePartialParameterUpdates() {
        // Given
        WoodModule woodModule = new WoodModule();
        woodModule.setParams(createTestWoodParams());

        // Create partial update with only geometry changes
        WoodParams partialUpdate = new WoodParams();
        WoodGeometry newGeometry = new WoodGeometry();
        newGeometry.setBeamSectionWidth(new BigDecimal("250.0"));
        newGeometry.setBeamSectionHeight(new BigDecimal("350.0"));
        partialUpdate.setGeometry(newGeometry);

        // When
        var applier = paramApplierFactory.get(woodModule);
        applier.apply(woodModule, partialUpdate);

        // Then
        assertNotNull(woodModule.getParams());
        assertNotNull(woodModule.getParams().getMaterialProperties()); // Should be preserved
        assertEquals(new BigDecimal("250.0"), woodModule.getParams().getGeometry().getBeamSectionWidth());
        assertEquals(new BigDecimal("350.0"), woodModule.getParams().getGeometry().getBeamSectionHeight());
    }

    private WoodParams createTestWoodParams() {
        WoodParams params = new WoodParams();
        params.setInitialDeformation(new BigDecimal("0.0"));

        // Material properties
        WoodProperties properties = new WoodProperties();
        properties.setCategory(WoodCategory.CONIFER_AND_POPLAR_WOOD);
        properties.setCharacteristicBendingStrength(new BigDecimal("24.0"));
        properties.setCharacteristicShearStrength(new BigDecimal("2.5"));
        properties.setMeanDensity(new BigDecimal("380.0"));
        properties.setPartialMaterialFactor(new BigDecimal("1.3"));
        params.setMaterialProperties(properties);

        // Geometry
        WoodGeometry geometry = new WoodGeometry();
        geometry.setBeamSectionWidth(new BigDecimal("200.0"));
        geometry.setBeamSectionHeight(new BigDecimal("300.0"));
        geometry.setBeamSpacing(new BigDecimal("600.0"));
        geometry.setBeamSpan(new BigDecimal("4000.0"));
        geometry.setServiceClass(WoodGeometryServiceClass.SERVICE_CLASS_1);
        geometry.setLoadDuration(WoodGeometryLoadDuration.MEDIA);
        geometry.setCorrectionFactor(new BigDecimal("1.0"));
        geometry.setDeformabilityFactor(new BigDecimal("0.8"));
        params.setGeometry(geometry);

        // Pre-intervention check
        WoodPreIntervationCheck check = new WoodPreIntervationCheck();
        check.setMaximumBendingMoment(new BigDecimal("50.0"));
        check.setMaximumShearForce(new BigDecimal("15.0"));
        check.setDesignBendingStrength(new BigDecimal("18.5"));
        check.setDesignShearStrength(new BigDecimal("1.9"));
        params.setPreIntervationCheck(check);

        return params;
    }
}
