package it.kimia.atlas.atlasservices.security;

import it.kimia.atlas.atlasservices.security.model.Group;
import it.kimia.atlas.atlasservices.security.model.IdpUser;
import it.kimia.atlas.atlasservices.security.model.Role;
import it.kimia.atlas.atlasservices.security.model.User;
import it.kimia.atlas.atlasservices.shared.model.CurrentUser;
import it.kimia.atlas.atlasservices.shared.model.UserGroup;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class IdpUserConverterTest {

    private final IdpUserConverter converter = new IdpUserConverter();

    @Nested
    @DisplayName("convertToCurrentUser")
    class ConvertToCurrentUser {

        @Test
        @DisplayName("Returns null when IdpUser is null")
        void returnsNullWhenIdpUserIsNull() {
            assertNull(converter.convertToCurrentUser(null));
        }

        @Test
        @DisplayName("Successfully converts IdpUser to CurrentUser")
        void successfullyConvertsIdpUserToCurrentUser() {
            User user = mock(User.class);
            when(user.getId()).thenReturn("userId");
            when(user.getUsername()).thenReturn("username");
            when(user.getFirstName()).thenReturn("firstName");
            when(user.getLastName()).thenReturn("lastName");
            when(user.getEmail()).thenReturn("<EMAIL>");
            when(user.getEmailVerified()).thenReturn(true);
            when(user.getCreatedTimestamp()).thenReturn(123456789L);
            when(user.getEnabled()).thenReturn(true);

            Group group = mock(Group.class);
            when(group.getName()).thenReturn("groupName");
            when(group.getUsers()).thenReturn(Collections.singletonList(user));

            IdpUser idpUser = mock(IdpUser.class);
            when(idpUser.getUser()).thenReturn(user);
            when(idpUser.getRoles()).thenReturn(Collections.singletonList(Role.USER));
            when(idpUser.getGroups()).thenReturn(Collections.singletonList(group));

            CurrentUser currentUser = converter.convertToCurrentUser(idpUser);

            assertNotNull(currentUser);
            assertEquals("userId", currentUser.getId());
            assertEquals("username", currentUser.getUsername());
            assertEquals("firstName", currentUser.getFirstName());
            assertEquals("lastName", currentUser.getLastName());
            assertEquals("<EMAIL>", currentUser.getEmail());
            assertTrue(currentUser.getEmailVerified());
            assertEquals(123456789L, currentUser.getCreatedTimestamp());
            assertTrue(currentUser.getEnabled());
            assertEquals(Collections.singletonList("USER"), currentUser.getRoles());
            assertEquals(1, currentUser.getGroups().size());
            assertEquals("groupName", currentUser.getGroups().get(0).getName());
            assertEquals(Collections.singletonList("userId"), currentUser.getGroups().get(0).getUserIds());
        }
    }

    @Nested
    @DisplayName("convertToUserGroup")
    class ConvertToUserGroup {

        @Test
        @DisplayName("Successfully converts Group to UserGroup")
        void successfullyConvertsGroupToUserGroup() {
            User user = mock(User.class);
            when(user.getId()).thenReturn("userId");

            Group group = mock(Group.class);
            when(group.getName()).thenReturn("groupName");
            when(group.getUsers()).thenReturn(Collections.singletonList(user));

            UserGroup userGroup = converter.convertToUserGroup(group);

            assertNotNull(userGroup);
            assertEquals("groupName", userGroup.getName());
            assertEquals(Collections.singletonList("userId"), userGroup.getUserIds());
        }
    }
}