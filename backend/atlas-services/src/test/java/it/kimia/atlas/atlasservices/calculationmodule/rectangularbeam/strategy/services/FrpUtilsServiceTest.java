package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProduct;
import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProductImpl;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInput;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.FlexuralVerifyExecutionInputImpl;
import it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.input.ShearVerifyExecutionInput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class FrpUtilsServiceTest {

    private FrpUtilsService frpUtilsService;

    @BeforeEach
    void setUp() {
        frpUtilsService = new FrpUtilsService();
    }

    @Nested
    class ComputeFrpAreaTests {

        @DisplayName("computeFrpArea returns correct area for valid inputs")
        @Test
        void computeFrpAreaReturnsCorrectAreaForValidInputs() {
            ReinforcedConcreteProduct product = mock(ReinforcedConcreteProductImpl.class);
            FlexuralVerifyExecutionInput input = mock(FlexuralVerifyExecutionInputImpl.class);

            when(product.getThickness()).thenReturn(BigDecimal.valueOf(0.02));
            when(input.getStripWidth()).thenReturn(100);
            when(input.getLayersNumber()).thenReturn(3);
            when(input.getProduct()).thenReturn(product);
            BigDecimal result = frpUtilsService.computeFrpArea(input);

            assertEquals(BigDecimal.valueOf(6).setScale(SCALE, RoundingMode.HALF_UP), result);
        }

        @DisplayName("computeFrpArea returns zero when thickness is zero")
        @Test
        void computeFrpAreaReturnsZeroWhenThicknessIsZero() {
            ReinforcedConcreteProduct product = mock(ReinforcedConcreteProductImpl.class);
            FlexuralVerifyExecutionInput input = mock(FlexuralVerifyExecutionInputImpl.class);

            when(product.getThickness()).thenReturn(BigDecimal.ZERO);
            when(input.getStripWidth()).thenReturn(100);
            when(input.getLayersNumber()).thenReturn(3);
            when(input.getProduct()).thenReturn(product);
            BigDecimal result = frpUtilsService.computeFrpArea(input);

            assertEquals(BigDecimal.ZERO.setScale(SCALE, RoundingMode.HALF_UP), result);
        }

        @DisplayName("computeFrpArea returns zero when strip width is zero")
        @Test
        void computeFrpAreaReturnsZeroWhenStripWidthIsZero() {
            ReinforcedConcreteProduct product = mock(ReinforcedConcreteProductImpl.class);
            FlexuralVerifyExecutionInput input = mock(FlexuralVerifyExecutionInputImpl.class);

            when(product.getThickness()).thenReturn(BigDecimal.valueOf(0.02));
            when(input.getStripWidth()).thenReturn(0);
            when(input.getLayersNumber()).thenReturn(3);
            when(input.getProduct()).thenReturn(product);

            BigDecimal result = frpUtilsService.computeFrpArea(input);

            assertEquals(BigDecimal.ZERO.setScale(SCALE, RoundingMode.HALF_UP), result);
        }

        @DisplayName("computeFrpArea returns zero when layers number is zero")
        @Test
        void computeFrpAreaReturnsZeroWhenLayersNumberIsZero() {
            ReinforcedConcreteProduct product = mock(ReinforcedConcreteProductImpl.class);
            FlexuralVerifyExecutionInput input = mock(FlexuralVerifyExecutionInputImpl.class);
            when(input.getProduct()).thenReturn(product);
            when(product.getThickness()).thenReturn(BigDecimal.valueOf(0.02));
            when(input.getStripWidth()).thenReturn(100);
            when(input.getLayersNumber()).thenReturn(0);

            BigDecimal result = frpUtilsService.computeFrpArea(input);

            assertEquals(BigDecimal.ZERO.setScale(SCALE, RoundingMode.HALF_UP), result);
        }
    }

    @Nested
    class ComputeFrpAreaForShearResistanceTests {
        @DisplayName("computeFrpAreaForShearResistance returns correct area from excel example 1")
        @Test
        void computeFrpAreaForShearResistanceReturnsCorrectAreaForExcelInputs1() {
            BigDecimal thickness = BigDecimal.valueOf(0.337);
            int stripWidth = 100;
            int layersNumber = 1;

            ReinforcedConcreteProduct product = mock(ReinforcedConcreteProduct.class);
            when(product.getThickness()).thenReturn(thickness);

            ShearVerifyExecutionInput input = mock(ShearVerifyExecutionInput.class);
            when(input.getProduct()).thenReturn(product);
            when(input.getStripWidth()).thenReturn(stripWidth);
            when(input.getLayersNumber()).thenReturn(layersNumber);

            BigDecimal result = frpUtilsService.computeFrpAreaForShearResistance(input);

            assertEquals(BigDecimal.valueOf(67.4).setScale(SCALE, RoundingMode.HALF_UP), result);
        }

        @DisplayName("computeFrpAreaForShearResistance returns correct area from excel example 2")
        @Test
        void computeFrpAreaForShearResistanceReturnsCorrectAreaForExcelInputs2() {
            BigDecimal thickness = BigDecimal.valueOf(0.165);
            int stripWidth = 100;
            int layersNumber = 1;

            ReinforcedConcreteProduct product = mock(ReinforcedConcreteProduct.class);
            when(product.getThickness()).thenReturn(thickness);

            ShearVerifyExecutionInput input = mock(ShearVerifyExecutionInput.class);
            when(input.getProduct()).thenReturn(product);
            when(input.getStripWidth()).thenReturn(stripWidth);
            when(input.getLayersNumber()).thenReturn(layersNumber);

            BigDecimal result = frpUtilsService.computeFrpAreaForShearResistance(input);

            assertEquals(BigDecimal.valueOf(33).setScale(SCALE, RoundingMode.HALF_UP), result);
        }

        @DisplayName("computeFrpAreaForShearResistance returns correct area from excel example 3")
        @Test
        void computeFrpAreaForShearResistanceReturnsCorrectAreaForExcelInputs3() {
            BigDecimal thickness = BigDecimal.valueOf(0.165);
            int stripWidth = 100;
            int layersNumber = 5;

            ReinforcedConcreteProduct product = mock(ReinforcedConcreteProduct.class);
            when(product.getThickness()).thenReturn(thickness);

            ShearVerifyExecutionInput input = mock(ShearVerifyExecutionInput.class);
            when(input.getProduct()).thenReturn(product);
            when(input.getStripWidth()).thenReturn(stripWidth);
            when(input.getLayersNumber()).thenReturn(layersNumber);

            BigDecimal result = frpUtilsService.computeFrpAreaForShearResistance(input);

            assertEquals(BigDecimal.valueOf(165).setScale(SCALE, RoundingMode.HALF_UP), result);
        }

        @DisplayName("computeFrpAreaForShearResistance returns correct area from excel example 4")
        @Test
        void computeFrpAreaForShearResistanceReturnsCorrectAreaForExcelInputs4() {
            BigDecimal thickness = BigDecimal.valueOf(0.165);
            int stripWidth = 500;
            int layersNumber = 1;

            ReinforcedConcreteProduct product = mock(ReinforcedConcreteProduct.class);
            when(product.getThickness()).thenReturn(thickness);

            ShearVerifyExecutionInput input = mock(ShearVerifyExecutionInput.class);
            when(input.getProduct()).thenReturn(product);
            when(input.getStripWidth()).thenReturn(stripWidth);
            when(input.getLayersNumber()).thenReturn(layersNumber);

            BigDecimal result = frpUtilsService.computeFrpAreaForShearResistance(input);

            assertEquals(BigDecimal.valueOf(165).setScale(SCALE, RoundingMode.HALF_UP), result);
        }

        @DisplayName("computeFrpAreaForShearResistance returns zero when thickness is zero")
        @Test
        void computeFrpAreaForShearResistanceReturnsZeroWhenThicknessIsZero() {
            BigDecimal thickness = BigDecimal.ZERO;
            int stripWidth = 100; // esempio
            int layersNumber = 2; // esempio

            ReinforcedConcreteProduct product = mock(ReinforcedConcreteProduct.class);
            when(product.getThickness()).thenReturn(thickness);

            ShearVerifyExecutionInput input = mock(ShearVerifyExecutionInput.class);
            when(input.getProduct()).thenReturn(product);
            when(input.getStripWidth()).thenReturn(stripWidth);
            when(input.getLayersNumber()).thenReturn(layersNumber);

            BigDecimal result = frpUtilsService.computeFrpAreaForShearResistance(input);

            assertEquals(BigDecimal.ZERO.setScale(SCALE, RoundingMode.HALF_UP), result);
        }

        @DisplayName("computeFrpAreaForShearResistance returns zero if strip width is zero")
        @Test
        void computeFrpAreaForShearResistanceReturnsZeroWhenStripWidthIsZero() {
            BigDecimal thickness = BigDecimal.valueOf(0.02); // esempio
            int stripWidth = 0;
            int layersNumber = 2; // esempio

            ReinforcedConcreteProduct product = mock(ReinforcedConcreteProduct.class);
            when(product.getThickness()).thenReturn(thickness);

            ShearVerifyExecutionInput input = mock(ShearVerifyExecutionInput.class);
            when(input.getProduct()).thenReturn(product);
            when(input.getStripWidth()).thenReturn(stripWidth);
            when(input.getLayersNumber()).thenReturn(layersNumber);

            BigDecimal result = frpUtilsService.computeFrpAreaForShearResistance(input);

            assertEquals(BigDecimal.ZERO.setScale(SCALE, RoundingMode.HALF_UP), result);
        }

        @DisplayName("computeFrpAreaForShearResistance returns zero if layers number is zero")
        @Test
        void computeFrpAreaForShearResistanceReturnsZeroWhenLayersNumberIsZero() {
            BigDecimal thickness = BigDecimal.valueOf(0.02); // esempio
            int stripWidth = 100; // esempio
            int layersNumber = 0;

            ReinforcedConcreteProduct product = mock(ReinforcedConcreteProduct.class);
            when(product.getThickness()).thenReturn(thickness);

            ShearVerifyExecutionInput input = mock(ShearVerifyExecutionInput.class);
            when(input.getProduct()).thenReturn(product);
            when(input.getStripWidth()).thenReturn(stripWidth);
            when(input.getLayersNumber()).thenReturn(layersNumber);

            BigDecimal result = frpUtilsService.computeFrpAreaForShearResistance(input);

            assertEquals(BigDecimal.ZERO.setScale(SCALE, RoundingMode.HALF_UP), result);
        }
    }
}