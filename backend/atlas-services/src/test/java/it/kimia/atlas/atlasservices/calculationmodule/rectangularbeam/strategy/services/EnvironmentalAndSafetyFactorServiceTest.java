package it.kimia.atlas.atlasservices.calculationmodule.rectangularbeam.strategy.services;

import it.kimia.atlas.atlasservices.calculationmodule.Exposure;
import it.kimia.atlas.atlasservices.calculationmodule.ReinforcedConcreteProduct;
import it.kimia.atlas.atlasservices.product.FiberType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static it.kimia.atlas.atlasservices.config.CalculationModuleConfiguration.SCALE;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class EnvironmentalAndSafetyFactorServiceTest {

    @InjectMocks
    private EnvironmentalAndSafetyFactorService environmentalAndSafetyFactorService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @DisplayName("getAmbientFactor returns correct factor from excel file for STEEL fiber type on AGGRESSIVE exposure -> 0.75")
    @Test
    void getAmbientFactorReturnsCorrectFactorForSteelOnAggressiveExposure_expectsValueFromExcel() {
        Exposure exposure = Exposure.AGGRESSIVE;
        ReinforcedConcreteProduct product = mock(ReinforcedConcreteProduct.class);

        when(product.getFiberType()).thenReturn(FiberType.STEEL);

        BigDecimal result = environmentalAndSafetyFactorService.getAmbientFactor(exposure, product);

        Assertions.assertEquals(BigDecimal.valueOf(0.75).setScale(SCALE, RoundingMode.HALF_UP), result);
    }

    @DisplayName("getAmbientFactor returns correct factor from excel file for STEEL fiber type on INTERNAL exposure -> 0.95")
    @Test
    void getAmbientFactorReturnsCorrectFactorForSteelOnInternalExposure_expectsValueFromExcel() {
        Exposure exposure = Exposure.INTERNAL;
        ReinforcedConcreteProduct product = mock(ReinforcedConcreteProduct.class);

        when(product.getFiberType()).thenReturn(FiberType.STEEL);

        BigDecimal result = environmentalAndSafetyFactorService.getAmbientFactor(exposure, product);

        Assertions.assertEquals(BigDecimal.valueOf(0.95).setScale(SCALE, RoundingMode.HALF_UP), result);
    }

    @DisplayName("getAmbientFactor returns correct factor from excel file for STEEL fiber type on EXTERNAL exposure -> 0.80")
    @Test
    void getAmbientFactorReturnsCorrectFactorForSteelOnExternalExposure_expectsValueFromExcel() {
        Exposure exposure = Exposure.EXTERNAL;
        ReinforcedConcreteProduct product = mock(ReinforcedConcreteProduct.class);

        when(product.getFiberType()).thenReturn(FiberType.STEEL);

        BigDecimal result = environmentalAndSafetyFactorService.getAmbientFactor(exposure, product);

        Assertions.assertEquals(BigDecimal.valueOf(0.80).setScale(SCALE, RoundingMode.HALF_UP), result);
    }

    @DisplayName("getAmbientFactor returns correct factor from excel file for PREFORMED_CARBON fiber type on AGGRESSIVE exposure -> 0.90")
    @Test
    void getAmbientFactorReturnsCorrectFactorForPreformedCarbonOnAggressiveExposure_expectsValueFromExcel() {
        Exposure exposure = Exposure.AGGRESSIVE;
        ReinforcedConcreteProduct product = mock(ReinforcedConcreteProduct.class);

        when(product.getFiberType()).thenReturn(FiberType.PREFORMED_CARBON);

        BigDecimal result = environmentalAndSafetyFactorService.getAmbientFactor(exposure, product);

        Assertions.assertEquals(BigDecimal.valueOf(0.90).setScale(SCALE, RoundingMode.HALF_UP), result);
    }

    @DisplayName("getAmbientFactor returns correct factor from excel file for PREFORMED_CARBON fiber type on INTERNAL exposure -> 0.95")
    @Test
    void getAmbientFactorReturnsCorrectFactorForPreformedCarbonOnInternalExposure_expectsValueFromExcel() {
        Exposure exposure = Exposure.INTERNAL;
        ReinforcedConcreteProduct product = mock(ReinforcedConcreteProduct.class);

        when(product.getFiberType()).thenReturn(FiberType.PREFORMED_CARBON);

        BigDecimal result = environmentalAndSafetyFactorService.getAmbientFactor(exposure, product);

        Assertions.assertEquals(BigDecimal.valueOf(0.95).setScale(SCALE, RoundingMode.HALF_UP), result);
    }

    @DisplayName("getAmbientFactor returns correct factor from excel file for PREFORMED_CARBON fiber type on EXTERNAL exposure -> 0.95")
    @Test
    void getAmbientFactorReturnsCorrectFactorForPreformedCarbonOnExternalExposure_expectsValueFromExcel() {
        Exposure exposure = Exposure.EXTERNAL;
        ReinforcedConcreteProduct product = mock(ReinforcedConcreteProduct.class);

        when(product.getFiberType()).thenReturn(FiberType.PREFORMED_CARBON);

        BigDecimal result = environmentalAndSafetyFactorService.getAmbientFactor(exposure, product);

        Assertions.assertEquals(BigDecimal.valueOf(0.95).setScale(SCALE, RoundingMode.HALF_UP), result);
    }

    @DisplayName("getAmbientFactor throws exception for null product")
    @Test
    void getAmbientFactorThrowsExceptionForNullProduct() {
        Exposure exposure = mock(Exposure.class);

        Assertions.assertThrows(NullPointerException.class, () -> environmentalAndSafetyFactorService.getAmbientFactor(exposure, null));
    }

    @DisplayName("calculateMaterialPartialFactor returns 1.25 for preformed carbon fiber type")
    @Test
    void calculateMaterialPartialFactorReturnsCorrectValueForPreformedCarbon() {
        ReinforcedConcreteProduct product = mock(ReinforcedConcreteProduct.class);
        when(product.getFiberType()).thenReturn(FiberType.PREFORMED_CARBON);
        BigDecimal result = environmentalAndSafetyFactorService.calculateMaterialPartialFactor(product);

        Assertions.assertEquals(BigDecimal.valueOf(1.25), result);
    }

    @DisplayName("calculateMaterialPartialFactor returns 1.3 for non-preformed carbon fiber type")
    @Test
    void calculateMaterialPartialFactorReturnsCorrectValueForNonPreformedCarbon() {
        ReinforcedConcreteProduct product = mock(ReinforcedConcreteProduct.class);
        when(product.getFiberType()).thenReturn(FiberType.STEEL);
        BigDecimal result = environmentalAndSafetyFactorService.calculateMaterialPartialFactor(product);

        Assertions.assertEquals(BigDecimal.valueOf(1.3), result);
    }
}