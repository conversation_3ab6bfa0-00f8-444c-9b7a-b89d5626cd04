package it.kimia.atlas.atlasservices.calculationmodule;

import it.kimia.atlas.atlasservices.calculationmodule.dto.ModuleCreateDTO;
import it.kimia.atlas.atlasservices.calculationmodule.dto.ModuleUpdateDTO;
import it.kimia.atlas.atlasservices.project.Project;
import it.kimia.atlas.atlasservices.project.ProjectRepository;
import it.kimia.atlas.atlasservices.project.ProjectService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.security.access.AccessDeniedException;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class CalculationModuleServiceImplTest {

    private final ProjectService projectService = mock(ProjectService.class);
    private final ProjectRepository projectRepository = mock(ProjectRepository.class);
    private final ParamApplierFactory paramApplierFactory = mock(ParamApplierFactory.class);
    private final CalculationModuleServiceImpl service = new CalculationModuleServiceImpl(projectService, projectRepository, paramApplierFactory);

    @Nested
    @DisplayName("getAllModules")
    class GetAllModules {

        @Test
        @DisplayName("Throws AccessDeniedException when project is not found")
        void throwsAccessDeniedExceptionWhenProjectNotFound() {
            when(projectService.getProjectById("invalidProjectId")).thenReturn(Optional.empty());

            assertThrows(AccessDeniedException.class, () -> service.getAllModules("invalidProjectId"));
        }

        @Test
        @DisplayName("Returns all modules when project is found")
        void returnsAllModulesWhenProjectIsFound() {
            Project project = mock(Project.class);
            List<CalculationModule> modules = List.of(mock(CalculationModule.class));
            when(projectService.getProjectById("validProjectId")).thenReturn(Optional.of(project));
            when(project.getModules()).thenReturn(modules);

            List<CalculationModule> result = service.getAllModules("validProjectId");

            assertEquals(modules, result);
        }
    }

    @Nested
    @DisplayName("getModuleById")
    class GetModuleById {

        @Test
        @DisplayName("Throws AccessDeniedException when project is not found")
        void throwsAccessDeniedExceptionWhenProjectNotFound() {
            when(projectService.getProjectById("invalidProjectId")).thenReturn(Optional.empty());

            assertThrows(AccessDeniedException.class, () -> service.getModuleById("invalidProjectId", "moduleId"));
        }

        @Test
        @DisplayName("Returns empty optional when module is not found")
        void returnsEmptyOptionalWhenModuleNotFound() {
            Project project = mock(Project.class);
            when(projectService.getProjectById("validProjectId")).thenReturn(Optional.of(project));
            when(project.getModules()).thenReturn(List.of());

            Optional<CalculationModule> result = service.getModuleById("validProjectId", "invalidModuleId");

            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("Returns module when found by ID")
        void returnsModuleWhenFoundById() {
            Project project = mock(Project.class);
            CalculationModule module = mock(CalculationModule.class);
            when(module.getId()).thenReturn("moduleId");
            when(projectService.getProjectById("validProjectId")).thenReturn(Optional.of(project));
            when(project.getModules()).thenReturn(List.of(module));

            Optional<CalculationModule> result = service.getModuleById("validProjectId", "moduleId");

            assertTrue(result.isPresent());
            assertEquals(module, result.get());
        }
    }

    @Nested
    @DisplayName("createModule")
    class CreateModule {

        @Test
        @DisplayName("Throws AccessDeniedException when project is not found")
        void throwsAccessDeniedExceptionWhenProjectNotFound() {
            when(projectService.getProjectById("invalidProjectId")).thenReturn(Optional.empty());

            assertThrows(AccessDeniedException.class, () -> service.createModule("invalidProjectId", mock(ModuleCreateDTO.class)));
        }

        @Test
        @DisplayName("Successfully creates a new module")
        void successfullyCreatesNewModule() {
            Project project = mock(Project.class);
            ModuleCreateDTO dto = mock(ModuleCreateDTO.class);
            when(dto.getType()).thenReturn(ModuleType.RECTANGULAR_BEAM);
            when(projectService.getProjectById("validProjectId")).thenReturn(Optional.of(project));
            when(project.getModules()).thenReturn(List.of());

            CalculationModule result = service.createModule("validProjectId", dto);

            assertNotNull(result);
            verify(projectRepository).save(project);
        }
    }

    @Nested
    @DisplayName("updateModule")
    class UpdateModule {

        @Test
        @DisplayName("Returns empty optional when project is not found")
        void returnsEmptyOptionalWhenProjectNotFound() {
            when(projectService.getProjectById("invalidProjectId")).thenReturn(Optional.empty());

            Optional<CalculationModule> result = service.updateModule("invalidProjectId", "moduleId", mock(ModuleUpdateDTO.class));

            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("Returns empty optional when module is not found")
        void returnsEmptyOptionalWhenModuleNotFound() {
            Project project = mock(Project.class);
            when(projectService.getProjectById("validProjectId")).thenReturn(Optional.of(project));
            when(project.getModules()).thenReturn(List.of());

            Optional<CalculationModule> result = service.updateModule("validProjectId", "invalidModuleId", mock(ModuleUpdateDTO.class));

            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("Successfully updates an existing module")
        void successfullyUpdatesExistingModule() {
            Project project = mock(Project.class);
            CalculationModule module = mock(CalculationModule.class);
            ModuleUpdateDTO dto = mock(ModuleUpdateDTO.class);
            when(projectService.getProjectById("validProjectId")).thenReturn(Optional.of(project));
            when(project.getModules()).thenReturn(List.of(module));
            when(module.getId()).thenReturn("moduleId");

            Optional<CalculationModule> result = service.updateModule("validProjectId", "moduleId", dto);

            assertTrue(result.isPresent());
            verify(projectRepository).save(project);
        }
    }

    @Nested
    @DisplayName("deleteModule")
    class DeleteModule {

        @Test
        @DisplayName("Does nothing when project is not found")
        void doesNothingWhenProjectNotFound() {
            when(projectService.getProjectById("invalidProjectId")).thenReturn(Optional.empty());

            service.deleteModule("invalidProjectId", "moduleId");

            verify(projectRepository, never()).save(any());
        }

        @Test
        @DisplayName("Successfully deletes a module")
        void successfullyDeletesModule() {
            Project project = mock(Project.class);
            CalculationModule module = mock(CalculationModule.class);
            when(module.getId()).thenReturn("moduleId");
            when(projectService.getProjectById("validProjectId")).thenReturn(Optional.of(project));
            when(project.getModules()).thenReturn(List.of(module));

            service.deleteModule("validProjectId", "moduleId");

            verify(projectRepository).save(project);
        }
    }

    @Nested
    @DisplayName("updateModuleParams")
    class UpdateModuleParams {

        @Test
        @DisplayName("Returns empty optional when project is not found")
        void returnsEmptyOptionalWhenProjectNotFound() {
            when(projectService.getProjectById("invalidProjectId")).thenReturn(Optional.empty());

            Optional<CalculationModule> result = service.updateModuleParams("invalidProjectId", "moduleId", new Object());

            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("Returns empty optional when module is not found")
        void returnsEmptyOptionalWhenModuleNotFound() {
            Project project = mock(Project.class);
            when(projectService.getProjectById("validProjectId")).thenReturn(Optional.of(project));
            when(project.getModules()).thenReturn(List.of());

            Optional<CalculationModule> result = service.updateModuleParams("validProjectId", "invalidModuleId", new Object());

            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("Successfully updates module parameters")
        void successfullyUpdatesModuleParameters() {
            Project project = mock(Project.class);
            CalculationModule module = mock(CalculationModule.class);
            ParamApplier applier = mock(ParamApplier.class);
            when(module.getId()).thenReturn("moduleId");
            when(projectService.getProjectById("validProjectId")).thenReturn(Optional.of(project));
            when(project.getModules()).thenReturn(List.of(module));
            when(paramApplierFactory.get(module)).thenReturn(applier);
            Object input = new Object();

            Optional<CalculationModule> result = service.updateModuleParams("validProjectId", "moduleId", input);

            assertTrue(result.isPresent());
            verify(applier).apply(module, input);
            verify(projectRepository).save(project);
        }
    }
}